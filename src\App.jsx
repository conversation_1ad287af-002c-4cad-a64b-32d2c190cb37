import React, { Suspense, lazy } from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { DatabaseProvider } from './contexts/DatabaseContext';
import { MessageProvider } from './contexts/MessageContext';
import { MenuProvider } from './contexts/MenuContext';
import WelcomeScreen from './pages/WelcomeScreen';
import Layout from './components/Layout';
import LoadingSpinner from './components/LoadingSpinner';
import ElectronWrapper from './components/ElectronWrapper';
import AboutModal from './components/modals/AboutModal';
import KeyboardShortcutsModal from './components/modals/KeyboardShortcutsModal';


// Lazy load heavy components to reduce initial bundle size
const Dashboard = lazy(() => import('./pages/Dashboard'));
const AdminPanel = lazy(() => import('./pages/AdminPanel'));
const BatchManagement = lazy(() => import('./pages/BatchManagement'));
const CreateBatch = lazy(() => import('./pages/CreateBatch'));
const CreateAssessment = lazy(() => import('./pages/CreateAssessment'));
const AssessmentManagement = lazy(() => import('./pages/AssessmentManagement'));
const TakeAssessment = lazy(() => import('./pages/TakeAssessment'));
const Settings = lazy(() => import('./pages/Settings'));
const Institution = lazy(() => import('./pages/Institution'));
const Tutorial = lazy(() => import('./pages/Tutorial'));

// Simplified App without authentication barriers

// Main App Component
function AppContent() {
  const { user, loading } = useAuth();

  // Show loading spinner while checking auth
  if (loading) {
    return <LoadingSpinner />;
  }

  // Show welcome screen if no user is selected
  if (!user) {
    return (
      <Router>
        <Routes>
          <Route path="*" element={<WelcomeScreen />} />
        </Routes>
      </Router>
    );
  }

  return (
    <Router>
      <MenuProvider>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route
              path="dashboard"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <Dashboard />
                </Suspense>
              }
            />

            <Route
              path="admin"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <AdminPanel />
                </Suspense>
              }
            />

            <Route
              path="batches"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <BatchManagement />
                </Suspense>
              }
            />

            <Route
              path="create-batch"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <CreateBatch />
                </Suspense>
              }
            />

            <Route
              path="batch-management"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <BatchManagement />
                </Suspense>
              }
            />

            <Route
              path="create-assessment"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <CreateAssessment />
                </Suspense>
              }
            />

            <Route
              path="assessments"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <AssessmentManagement />
                </Suspense>
              }
            />

            <Route
              path="take-assessment"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <TakeAssessment />
                </Suspense>
              }
            />

            <Route
              path="test"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <TakeAssessment />
                </Suspense>
              }
            />

            <Route
              path="settings"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <Settings />
                </Suspense>
              }
            />

            <Route
              path="institution"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <Institution />
                </Suspense>
              }
            />

            <Route
              path="tutorial"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <Tutorial />
                </Suspense>
              }
            />
          </Route>

          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>

        {/* Global Modals */}
        <AboutModal />
        <KeyboardShortcutsModal />
      </MenuProvider>
    </Router>
  );
}

function App() {
  return (
    <ElectronWrapper>
      <DatabaseProvider>
        <AuthProvider>
          <MessageProvider>
            <div className="min-h-screen bg-gray-50">
              <AppContent />
            </div>
          </MessageProvider>
        </AuthProvider>
      </DatabaseProvider>
    </ElectronWrapper>
  );
}

export default App;
