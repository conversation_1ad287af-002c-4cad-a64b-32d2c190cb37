import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // Production mode for deployment
  const isDev = false;
  const isProd = true;

  return {
    // Base path configuration for Electron file protocol
    base: './',

    // Plugin configuration
    plugins: [
      react({
        // JSX transformation configuration - using automatic runtime (no React import needed)
        jsxRuntime: 'automatic',
        // Enable fast refresh in development mode
        fastRefresh: isDev,
        // Include .jsx files
        include: /\.(jsx|js|ts|tsx)$/,
      }),
    ],



    // Build configuration
    build: {
      // Output directory compatible with electron/main.js
      outDir: 'dist',

      // Asset directory
      assetsDir: 'assets',

      // Source maps configuration - disable for file-based loading
      sourcemap: false,

      // Minification
      minify: isProd ? 'esbuild' : false,

      // Target configuration for Electron compatibility
      target: 'esnext',

      // Chunk size warnings and limits
      chunkSizeWarningLimit: 500,

      // Ensure proper asset handling for file protocol
      assetsInlineLimit: 0, // Don't inline assets for better file protocol compatibility

      // Rollup options for advanced bundling
      rollupOptions: {
        // Input configuration
        input: {
          main: resolve(__dirname, 'index.html'),
        },

        // External dependencies (handled by Electron)
        external: [],

        // Output configuration
        output: {
          // Manual chunks for code splitting optimization
          manualChunks: (id) => {
            // Chart.js - large library, separate chunk
            if (id.includes('chart.js') || id.includes('react-chartjs-2')) {
              return 'chart';
            }

            // Lucide React - icons library
            if (id.includes('lucide-react')) {
              return 'icons';
            }

            // React core libraries
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-core';
            }
            if (id.includes('react-router')) {
              return 'react-router';
            }

            // Utility libraries (only if they exist)
            if (id.includes('uuid') || id.includes('dompurify') || id.includes('bcryptjs')) {
              return 'utils';
            }

            // Node modules vendor chunk for everything else
            if (id.includes('node_modules')) {
              return 'vendor';
            }
          },

          // Chunk file naming
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId
              ? chunkInfo.facadeModuleId.split('/').pop().replace('.js', '')
              : 'chunk';
            return `assets/${facadeModuleId}-[hash].js`;
          },

          // Asset file naming
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/\.(css)$/.test(assetInfo.name)) {
              return `assets/[name]-[hash].${ext}`;
            }
            if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
              return `assets/images/[name]-[hash].${ext}`;
            }
            return `assets/[name]-[hash].${ext}`;
          },

          // Entry file naming
          entryFileNames: 'assets/[name]-[hash].js',
        },
      },

      // Build optimizations
      cssCodeSplit: true,
      reportCompressedSize: isProd,
    },

    // CSS configuration
    css: {
      // PostCSS configuration (works with existing postcss.config.js)
      postcss: './postcss.config.js',

      // CSS modules configuration
      modules: {
        localsConvention: 'camelCase',
        generateScopedName: isDev
          ? '[name]__[local]__[hash:base64:5]'
          : '[hash:base64:8]',
      },

      // CSS preprocessing
      preprocessorOptions: {
        scss: {
          additionalData: `@import "src/styles/variables.scss";`,
        },
      },

      // CSS dev source maps
      devSourcemap: isDev,
    },

    // Asset handling configuration
    publicDir: 'public',

    // Path resolution
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@pages': resolve(__dirname, 'src/pages'),
        '@utils': resolve(__dirname, 'src/utils'),
        '@contexts': resolve(__dirname, 'src/contexts'),
        '@hooks': resolve(__dirname, 'src/hooks'),
        '@styles': resolve(__dirname, 'src/styles'),
      },
    },

    // Environment variables configuration
    define: {
      __DEV__: isDev,
      __PROD__: isProd,
      'process.env.NODE_ENV': JSON.stringify(isProd ? 'production' : 'development'),
    },

    // Development server configuration - disabled for file-based loading
    server: {
      // Disable dev server since we're using file-based loading
      open: false,
      // Configure for build-and-serve workflow
      hmr: false,
    },

    // Optimization configuration
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'chart.js',
        'react-chartjs-2',
        'lucide-react',
        'uuid',
        'dompurify',
        'bcryptjs',
      ],
      exclude: [
        'electron',
        'sql.js',
        'pdfkit',
      ],
      // Force optimization of specific dependencies
      force: isProd,
    },

    // Electron-specific configuration
    electronRenderer: {
      // Enable Node.js integration in renderer
      nodeIntegration: false,
      contextIsolation: true,
    },

    // Performance optimizations
    esbuild: {
      // Drop console and debugger in production
      drop: isProd ? ['console', 'debugger'] : [],
      // Target ES2020 for better performance
      target: 'es2020',
      // Let React plugin handle JSX transformation
    },

    // Preview server configuration (for production testing)
    preview: {
      port: 4173,
      host: '127.0.0.1',
      strictPort: true,
    },

    // Logging configuration - production mode
    logLevel: 'warn',

    // Clear screen configuration
    clearScreen: false,
  };
});
