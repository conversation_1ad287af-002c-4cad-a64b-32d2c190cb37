/**
 * Application Menu System for Peer Review System
 *
 * Comprehensive menu bar implementation with native Electron Menu API
 * Includes File, Edit, View, Window, and Help menus with keyboard shortcuts
 *
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <hrishi<PERSON><EMAIL>>
 * Company: Ajinkyacreatiion PVT. LTD.
 */

const { Menu, shell, dialog, app, BrowserWindow } = require('electron');

// Get package.json for version information
const packageJson = require('../../package.json');

class ApplicationMenu {
  constructor() {
    this.currentUser = null;
    // Remove developer mode detection - always production mode
  }

  // Set current user context for menu state management
  setUserContext(user) {
    this.currentUser = user;
    this.updateMenu();
  }

  // Create the complete application menu
  createMenu() {
    const template = [
      this.createFileMenu(),
      this.createEditMenu(),
      this.createViewMenu(),
      this.createWindowMenu(),
      this.createHelpMenu()
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);

    return menu;
  }

  // File Menu
  createFileMenu() {
    return {
      label: 'File',
      submenu: [
        {
          label: 'New Assessment',
          accelerator: 'CmdOrCtrl+N',
          enabled: this.isSuperAdmin(),
          click: () => this.sendToRenderer('menu:navigate', '/create-assessment')
        },
        {
          label: 'Open Assessment',
          accelerator: 'CmdOrCtrl+O',
          enabled: this.isTeacherOrAdmin(),
          click: () => this.sendToRenderer('menu:navigate', '/assessments')
        },
        {
          label: 'Save Assessment',
          accelerator: 'CmdOrCtrl+S',
          enabled: this.isSuperAdmin(),
          click: () => this.sendToRenderer('menu:action', 'save-assessment')
        },
        { type: 'separator' },
        {
          label: 'Export Data',
          submenu: [
            {
              label: 'Export Assessment Results',
              click: () => this.handleExportData('assessment-results')
            },
            {
              label: 'Export Student Data',
              click: () => this.handleExportData('student-data')
            },
            {
              label: 'Export Reports',
              click: () => this.handleExportData('reports')
            },
            { type: 'separator' },
            {
              label: 'Create Backup',
              click: () => this.handleCreateBackup()
            }
          ]
        },
        {
          label: 'Import Data',
          submenu: [
            {
              label: 'Import Student Data',
              click: () => this.handleImportData('student-data')
            },
            {
              label: 'Import Assessment Data',
              click: () => this.handleImportData('assessment-data')
            },
            { type: 'separator' },
            {
              label: 'Restore Backup',
              click: () => this.handleRestoreBackup()
            }
          ]
        },
        { type: 'separator' },
        {
          label: 'Print Report',
          accelerator: 'CmdOrCtrl+P',
          click: () => this.handlePrintReport()
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => app.quit()
        }
      ]
    };
  }

  // Edit Menu
  createEditMenu() {
    return {
      label: 'Edit',
      submenu: [
        {
          label: 'Undo',
          accelerator: 'CmdOrCtrl+Z',
          role: 'undo'
        },
        {
          label: 'Redo',
          accelerator: 'CmdOrCtrl+Shift+Z',
          role: 'redo'
        },
        { type: 'separator' },
        {
          label: 'Cut',
          accelerator: 'CmdOrCtrl+X',
          role: 'cut'
        },
        {
          label: 'Copy',
          accelerator: 'CmdOrCtrl+C',
          role: 'copy'
        },
        {
          label: 'Paste',
          accelerator: 'CmdOrCtrl+V',
          role: 'paste'
        },
        {
          label: 'Select All',
          accelerator: 'CmdOrCtrl+A',
          role: 'selectall'
        },
        { type: 'separator' },
        {
          label: 'Find',
          accelerator: 'CmdOrCtrl+F',
          click: () => this.sendToRenderer('menu:action', 'show-search')
        },
        { type: 'separator' },
        {
          label: 'Preferences',
          accelerator: 'CmdOrCtrl+,',
          click: () => this.sendToRenderer('menu:navigate', '/settings')
        }
      ]
    };
  }

  // View Menu
  createViewMenu() {
    return {
      label: 'View',
      submenu: [
        {
          label: 'Dashboard',
          accelerator: 'CmdOrCtrl+1',
          click: () => this.sendToRenderer('menu:navigate', '/dashboard')
        },
        {
          label: 'Assessments',
          accelerator: 'CmdOrCtrl+2',
          enabled: this.isTeacherOrAdmin(),
          click: () => this.sendToRenderer('menu:navigate', '/assessments')
        },
        {
          label: 'Students',
          accelerator: 'CmdOrCtrl+3',
          enabled: this.isTeacherOrAdmin(),
          click: () => this.sendToRenderer('menu:navigate', '/batches')
        },
        {
          label: 'Reports',
          accelerator: 'CmdOrCtrl+4',
          enabled: this.isTeacherOrAdmin(),
          click: () => this.sendToRenderer('menu:navigate', '/reports')
        },
        {
          label: 'Statistics',
          accelerator: 'CmdOrCtrl+5',
          enabled: this.isTeacherOrAdmin(),
          click: () => this.sendToRenderer('menu:navigate', '/statistics')
        },
        { type: 'separator' },
        {
          label: 'Toggle Full Screen',
          accelerator: 'F11',
          click: () => this.toggleFullScreen()
        },
        { type: 'separator' },
        {
          label: 'Zoom In',
          accelerator: 'CmdOrCtrl+Plus',
          click: () => this.adjustZoom(0.1)
        },
        {
          label: 'Zoom Out',
          accelerator: 'CmdOrCtrl+-',
          click: () => this.adjustZoom(-0.1)
        },
        {
          label: 'Reset Zoom',
          accelerator: 'CmdOrCtrl+0',
          click: () => this.resetZoom()
        },
        { type: 'separator' },
        {
          label: 'Refresh',
          accelerator: 'CmdOrCtrl+R',
          click: () => this.refreshWindow()
        }
      ]
    };
  }

  // Window Menu
  createWindowMenu() {
    return {
      label: 'Window',
      submenu: [
        {
          label: 'Minimize',
          accelerator: 'CmdOrCtrl+M',
          role: 'minimize'
        },
        {
          label: 'Maximize',
          click: () => this.toggleMaximize()
        },
        {
          label: 'Close',
          accelerator: 'CmdOrCtrl+W',
          role: 'close'
        },
        { type: 'separator' },
        {
          label: 'Always on Top',
          type: 'checkbox',
          click: (menuItem) => this.toggleAlwaysOnTop(menuItem.checked)
        }
        // Developer Tools completely removed for production
      ]
    };
  }

  // Help Menu
  createHelpMenu() {
    return {
      label: 'Help',
      submenu: [
        {
          label: 'Tutorial',
          click: () => this.showTutorial()
        },
        {
          label: 'User Manual',
          click: () => this.openUserManual()
        },
        {
          label: 'Keyboard Shortcuts',
          accelerator: 'CmdOrCtrl+?',
          click: () => this.showKeyboardShortcuts()
        },
        { type: 'separator' },
        {
          label: 'Check for Updates',
          click: () => this.checkForUpdates()
        },
        {
          label: 'Report Issue',
          click: () => this.reportIssue()
        },
        { type: 'separator' },
        {
          label: 'About Peer Review System',
          click: () => this.showAboutDialog()
        }
      ]
    };
  }

  // Helper methods for user role checking
  isSuperAdmin() {
    return this.currentUser?.role === 'super_admin';
  }

  isAdmin() {
    return this.currentUser?.role === 'admin' || this.isSuperAdmin();
  }

  isTeacher() {
    return this.currentUser?.role === 'teacher';
  }

  isTeacherOrAdmin() {
    return this.isTeacher() || this.isAdmin();
  }

  // Send message to renderer process
  sendToRenderer(channel, data) {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send(channel, data);
    }
  }

  // Update menu based on current state
  updateMenu() {
    this.createMenu();
  }

  // Menu action handlers
  async handleExportData(type) {
    try {
      const result = await dialog.showSaveDialog({
        title: `Export ${type.replace('-', ' ')}`,
        defaultPath: `${type}-${new Date().toISOString().split('T')[0]}.csv`,
        filters: [
          { name: 'CSV Files', extensions: ['csv'] },
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (!result.canceled) {
        this.sendToRenderer('menu:export-data', { type, filePath: result.filePath });
      }
    } catch (error) {
      this.showErrorDialog('Export Error', `Failed to export ${type}: ${error.message}`);
    }
  }

  async handleImportData(type) {
    try {
      const result = await dialog.showOpenDialog({
        title: `Import ${type.replace('-', ' ')}`,
        filters: [
          { name: 'CSV Files', extensions: ['csv'] },
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        properties: ['openFile']
      });

      if (!result.canceled && result.filePaths.length > 0) {
        this.sendToRenderer('menu:import-data', { type, filePath: result.filePaths[0] });
      }
    } catch (error) {
      this.showErrorDialog('Import Error', `Failed to import ${type}: ${error.message}`);
    }
  }

  async handleCreateBackup() {
    try {
      const result = await dialog.showSaveDialog({
        title: 'Create Backup',
        defaultPath: `peer-review-backup-${new Date().toISOString().split('T')[0]}.backup`,
        filters: [
          { name: 'Backup Files', extensions: ['backup'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (!result.canceled) {
        this.sendToRenderer('menu:create-backup', { filePath: result.filePath });
      }
    } catch (error) {
      this.showErrorDialog('Backup Error', `Failed to create backup: ${error.message}`);
    }
  }

  async handleRestoreBackup() {
    try {
      const result = await dialog.showOpenDialog({
        title: 'Restore Backup',
        filters: [
          { name: 'Backup Files', extensions: ['backup'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        properties: ['openFile']
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const confirmResult = await dialog.showMessageBox({
          type: 'warning',
          buttons: ['Restore', 'Cancel'],
          defaultId: 1,
          title: 'Confirm Restore',
          message: 'Are you sure you want to restore from backup?',
          detail: 'This will replace all current data. This action cannot be undone.'
        });

        if (confirmResult.response === 0) {
          this.sendToRenderer('menu:restore-backup', { filePath: result.filePaths[0] });
        }
      }
    } catch (error) {
      this.showErrorDialog('Restore Error', `Failed to restore backup: ${error.message}`);
    }
  }

  handlePrintReport() {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.print({
        silent: false,
        printBackground: true,
        deviceName: '',
        color: true,
        margins: {
          marginType: 'printableArea'
        },
        landscape: false,
        scaleFactor: 100
      });
    }
  }

  toggleFullScreen() {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.setFullScreen(!mainWindow.isFullScreen());
    }
  }

  adjustZoom(delta) {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
    if (mainWindow && !mainWindow.isDestroyed()) {
      const currentZoom = mainWindow.webContents.getZoomFactor();
      const newZoom = Math.max(0.25, Math.min(3.0, currentZoom + delta));
      mainWindow.webContents.setZoomFactor(newZoom);
    }
  }

  resetZoom() {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.setZoomFactor(1.0);
    }
  }

  refreshWindow() {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.reload();
    }
  }

  toggleMaximize() {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
    if (mainWindow && !mainWindow.isDestroyed()) {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      } else {
        mainWindow.maximize();
      }
    }
  }

  toggleAlwaysOnTop(checked) {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.setAlwaysOnTop(checked);
    }
  }

  // toggleDevTools method removed for production security

  // Help menu handlers
  showTutorial() {
    this.sendToRenderer('menu:show-tutorial');
  }

  openUserManual() {
    shell.openExternal('https://ajinkyacreatiion.com/peer-review-system/manual');
  }

  showKeyboardShortcuts() {
    const shortcuts = [
      { action: 'New Assessment', shortcut: 'Ctrl+N' },
      { action: 'Open Assessment', shortcut: 'Ctrl+O' },
      { action: 'Save Assessment', shortcut: 'Ctrl+S' },
      { action: 'Print Report', shortcut: 'Ctrl+P' },
      { action: 'Find', shortcut: 'Ctrl+F' },
      { action: 'Preferences', shortcut: 'Ctrl+,' },
      { action: 'Dashboard', shortcut: 'Ctrl+1' },
      { action: 'Assessments', shortcut: 'Ctrl+2' },
      { action: 'Students', shortcut: 'Ctrl+3' },
      { action: 'Reports', shortcut: 'Ctrl+4' },
      { action: 'Statistics', shortcut: 'Ctrl+5' },
      { action: 'Toggle Full Screen', shortcut: 'F11' },
      { action: 'Zoom In', shortcut: 'Ctrl+Plus' },
      { action: 'Zoom Out', shortcut: 'Ctrl+-' },
      { action: 'Reset Zoom', shortcut: 'Ctrl+0' },
      { action: 'Refresh', shortcut: 'Ctrl+R' }
      // Developer Tools shortcut removed for production security
    ];

    this.sendToRenderer('menu:show-shortcuts', shortcuts);
  }

  async checkForUpdates() {
    try {
      this.sendToRenderer('menu:check-updates');
    } catch (error) {
      this.showErrorDialog('Update Error', `Failed to check for updates: ${error.message}`);
    }
  }

  reportIssue() {
    // Offline application - no external issue reporting
    this.showInfoDialog('Issue Reporting', 'This is an offline application. Please document any issues locally and refer to the built-in help system for troubleshooting.');
  }

  showAboutDialog() {
    const aboutInfo = {
      name: packageJson.build.productName || packageJson.name,
      version: packageJson.version,
      description: packageJson.description,
      author: packageJson.author,
      developer: {
        name: 'Hrishikesh Mohite',
        email: '<EMAIL>',
        website: 'https://www.hrishikeshmohite.com'
      },
      company: {
        name: 'Ajinkyacreatiion PVT. LTD.',
        website: 'https://ajinkyacreatiion.com',
        contact: '<EMAIL>'
      },
      copyright: packageJson.build.copyright || `Copyright © ${new Date().getFullYear()} Ajinkyacreatiion PVT. LTD.`,
      license: packageJson.license,
      electron: process.versions.electron,
      node: process.versions.node,
      chrome: process.versions.chrome
    };

    this.sendToRenderer('menu:show-about', aboutInfo);
  }

  // Utility methods
  showErrorDialog(title, message) {
    dialog.showErrorBox(title, message);
  }

  showInfoDialog(title, message) {
    dialog.showMessageBox({
      type: 'info',
      title,
      message,
      buttons: ['OK']
    });
  }
}

// Create and export the menu instance
const applicationMenu = new ApplicationMenu();

// Export functions for use in main process
function createApplicationMenu() {
  return applicationMenu.createMenu();
}

function setUserContext(user) {
  applicationMenu.setUserContext(user);
}

function updateApplicationMenu() {
  applicationMenu.updateMenu();
}

module.exports = {
  createApplicationMenu,
  setUserContext,
  updateApplicationMenu,
  ApplicationMenu
};
