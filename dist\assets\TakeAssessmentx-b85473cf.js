import { r as reactExports, R as React, j as jsxDevRuntimeExports, u as useNavigate } from './chunk-2ef8e52b.js';
import { b as useDatabase, u as useAuth } from './main-ab4f3d46.js';
import { u as Upload, X, q as User, d as BookOpen, A as AlertCircle, C as CheckCircle, F as FileText, a5 as BarChart3, w as Calendar, U as Users, a6 as TrendingUp, a7 as Target, a8 as Award, R as RefreshCw, m as Search, Q as Filter, a9 as Play, G as GraduationCap, r as Clock, W as Download, aa as FileDown, I as Info, b as ArrowRight } from './chunk-028772a4.js';
import { C as Chart, a as CategoryScale, L as LinearScale, B as BarElement, A as ArcElement, p as plugin_title, b as plugin_tooltip, c as plugin_legend, d as Bar, P as Pie } from './chunk-87c5b779.js';
import './chunk-03d61bd9.js';

const UploadResponseModal = ({
  isOpen,
  onClose,
  student,
  assessment,
  onUpload,
  uploading
}) => {
  const [responses, setResponses] = reactExports.useState({});
  const [errors, setErrors] = reactExports.useState({});
  const [questions, setQuestions] = reactExports.useState([]);
  React.useEffect(() => {
    if (isOpen && assessment) {
      loadAssessmentQuestions();
    }
  }, [isOpen, assessment]);
  const loadAssessmentQuestions = async () => {
    try {
      if (!assessmentId || !electronAPI?.assessments?.getQuestions) {
        throw new Error("Assessment ID or API not available");
      }
      const result = await electronAPI.assessments.getQuestions(assessmentId);
      if (result.success) {
        setQuestions(result.data);
      } else {
        throw new Error(result.error || "Failed to load questions");
      }
    } catch (error) {
      console.error("Failed to load questions:", error);
      showMessage("error", `Failed to load assessment questions: ${error.message}`);
    }
  };
  const handleResponseChange = (questionId, optionId) => {
    setResponses((prev) => ({
      ...prev,
      [questionId]: optionId
    }));
    if (errors[questionId]) {
      setErrors((prev) => ({
        ...prev,
        [questionId]: null
      }));
    }
  };
  const validateResponses = () => {
    const newErrors = {};
    questions.forEach((question) => {
      if (!responses[question.id]) {
        newErrors[question.id] = "Please select an answer for this question";
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateResponses()) {
      return;
    }
    try {
      await onUpload(responses);
      onClose();
      setResponses({});
      setErrors({});
    } catch (error) {
      console.error("Upload failed:", error);
    }
  };
  const handleClose = () => {
    onClose();
    setResponses({});
    setErrors({});
  };
  if (!isOpen) {
    return null;
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center pb-4 border-b border-gray-200", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Upload, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 109,
            columnNumber: 15
          }, globalThis),
          "Upload Student Response"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 108,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-1", children: "Enter the student's responses for offline assessment submission" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 112,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 107,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: handleClose,
          className: "text-gray-400 hover:text-gray-600",
          disabled: uploading,
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-6 w-6" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 121,
            columnNumber: 13
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 116,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
      lineNumber: 106,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 grid grid-cols-1 md:grid-cols-2 gap-4", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-blue-50 border border-blue-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(User, { className: "h-5 w-5 text-blue-400 mr-2" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 129,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-blue-900", children: "Student Information" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 131,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-blue-800", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: [
            student?.first_name,
            " ",
            student?.last_name
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 133,
            columnNumber: 19
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 132,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-blue-800", children: [
            "ID: ",
            student?.student_id
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 135,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-blue-800", children: student?.email }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 136,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 130,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 128,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 127,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-green-50 border border-green-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BookOpen, { className: "h-5 w-5 text-green-400 mr-2" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 143,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-green-900", children: "Assessment Information" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 145,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-green-800", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: assessment?.name }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 147,
            columnNumber: 19
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 146,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-green-800", children: [
            "Type: ",
            assessment?.assessment_type
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 149,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-green-800", children: [
            "Questions: ",
            questions.length
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 150,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 144,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 142,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 141,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
      lineNumber: 126,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("form", { onSubmit: handleSubmit, className: "mt-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-h-96 overflow-y-auto", children: questions.length > 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: questions.map((question, questionIndex) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "border border-gray-200 rounded-lg p-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900", children: [
            "Question ",
            questionIndex + 1,
            ": ",
            question.text
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 164,
            columnNumber: 23
          }, globalThis),
          errors[question.id] && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
              lineNumber: 169,
              columnNumber: 27
            }, globalThis),
            errors[question.id]
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 168,
            columnNumber: 25
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 163,
          columnNumber: 21
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-2", children: question.options.map((option, optionIndex) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "label",
          {
            className: `flex items-center p-3 border rounded-md cursor-pointer transition-colors ${responses[question.id] === option.id ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"}`,
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "radio",
                  name: `question_${question.id}`,
                  value: option.id,
                  checked: responses[question.id] === option.id,
                  onChange: () => handleResponseChange(question.id, option.id),
                  className: "mr-3",
                  disabled: uploading
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
                  lineNumber: 185,
                  columnNumber: 27
                },
                globalThis
              ),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm text-gray-900", children: [
                  String.fromCharCode(65 + optionIndex),
                  ". ",
                  option.text
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
                  lineNumber: 195,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "ml-2 text-sm font-medium text-green-600", children: [
                  "(",
                  option.marks,
                  " marks)"
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
                  lineNumber: 198,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
                lineNumber: 194,
                columnNumber: 27
              }, globalThis),
              responses[question.id] === option.id && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-blue-600" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
                lineNumber: 203,
                columnNumber: 29
              }, globalThis)
            ]
          },
          option.id,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 177,
            columnNumber: 25
          },
          globalThis
        )) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 175,
          columnNumber: 21
        }, globalThis)
      ] }, question.id, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 162,
        columnNumber: 19
      }, globalThis)) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 160,
        columnNumber: 15
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 213,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "Loading Questions" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 214,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "Please wait while we load the assessment questions..." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 215,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 212,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 158,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-6 flex justify-end space-x-3 pt-4 border-t border-gray-200", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            type: "button",
            onClick: handleClose,
            disabled: uploading,
            className: "btn-outline",
            children: "Cancel"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 224,
            columnNumber: 13
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            type: "submit",
            disabled: uploading || questions.length === 0,
            className: "btn-primary flex items-center space-x-2",
            children: uploading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
                lineNumber: 239,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Uploading..." }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
                lineNumber: 240,
                columnNumber: 19
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
              lineNumber: 238,
              columnNumber: 17
            }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Upload, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
                lineNumber: 244,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Upload Response" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
                lineNumber: 245,
                columnNumber: 19
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
              lineNumber: 243,
              columnNumber: 17
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 232,
            columnNumber: 13
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 223,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
      lineNumber: 157,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5 text-yellow-400 mt-0.5 mr-3" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 255,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-yellow-900", children: "Upload Instructions" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 257,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-yellow-800 mt-1 space-y-1", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Select the student's chosen answer for each question" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 259,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Ensure all questions are answered before uploading" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 260,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• The system will automatically calculate the total score" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 261,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Once uploaded, the response cannot be modified" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
            lineNumber: 262,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
          lineNumber: 258,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
        lineNumber: 256,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
      lineNumber: 254,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
      lineNumber: 253,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
    lineNumber: 104,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",
    lineNumber: 103,
    columnNumber: 5
  }, globalThis);
};

const StudentStatisticsModal = ({
  isOpen,
  onClose,
  student,
  assessment,
  onLoadStatistics,
  loading
}) => {
  const [statistics, setStatistics] = reactExports.useState(null);
  const [error, setError] = reactExports.useState(null);
  reactExports.useEffect(() => {
    if (isOpen && student && assessment) {
      loadStatistics();
    }
  }, [isOpen, student, assessment]);
  const loadStatistics = async () => {
    try {
      setError(null);
      const stats = await onLoadStatistics(student.id, assessment.id);
      setStatistics(stats);
    } catch (error2) {
      setError(error2.message);
    }
  };
  const formatDate = (dateString) => {
    if (!dateString) {
      return "Not submitted";
    }
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  };
  const getPerformanceColor = (percentage) => {
    if (percentage >= 80) {
      return "text-green-600 bg-green-100";
    }
    if (percentage >= 60) {
      return "text-yellow-600 bg-yellow-100";
    }
    return "text-red-600 bg-red-100";
  };
  const getRankSuffix = (rank) => {
    if (rank === 1) {
      return "st";
    }
    if (rank === 2) {
      return "nd";
    }
    if (rank === 3) {
      return "rd";
    }
    return "th";
  };
  if (!isOpen) {
    return null;
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center pb-4 border-b border-gray-200", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BarChart3, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 77,
            columnNumber: 15
          }, globalThis),
          "Student Assessment Statistics"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 76,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-1", children: "Detailed performance analysis and batch comparison" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 80,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 75,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: onClose,
          className: "text-gray-400 hover:text-gray-600",
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-6 w-6" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 88,
            columnNumber: 13
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 84,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
      lineNumber: 74,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-6", children: loading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-12", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 96,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-4 text-sm text-gray-500", children: "Loading statistics..." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 97,
        columnNumber: 15
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
      lineNumber: 95,
      columnNumber: 13
    }, globalThis) : error ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-12", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "mx-auto h-12 w-12 text-red-500" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 101,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "Error Loading Statistics" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 102,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: error }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 103,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: loadStatistics,
          className: "mt-4 btn-primary",
          children: "Retry"
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 104,
          columnNumber: 15
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
      lineNumber: 100,
      columnNumber: 13
    }, globalThis) : statistics ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-blue-50 border border-blue-200 rounded-lg p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(User, { className: "h-6 w-6 text-blue-600 mr-3" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 117,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-blue-900", children: [
              statistics.student.first_name,
              " ",
              statistics.student.last_name
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 119,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-blue-700", children: [
              "ID: ",
              statistics.student.student_id
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 122,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-blue-700", children: statistics.student.email }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 123,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 118,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 116,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 115,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-green-50 border border-green-200 rounded-lg p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BookOpen, { className: "h-6 w-6 text-green-600 mr-3" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 130,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-green-900", children: statistics.assessment.name }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 132,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-green-700", children: [
              "Type: ",
              statistics.assessment.assessment_type
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 135,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-green-700", children: [
              "Due: ",
              formatDate(statistics.assessment.due_date)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 136,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 131,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 129,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 128,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 114,
        columnNumber: 15
      }, globalThis),
      statistics.hasSubmitted ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border border-gray-200 rounded-lg p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-4 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-green-600 mr-2" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 148,
            columnNumber: 21
          }, globalThis),
          "Submission Details"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 147,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-blue-600", children: statistics.response.total_score }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 154,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Total Score" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 157,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 153,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-green-600", children: statistics.questionStats.length }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 161,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Questions Answered" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 164,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 160,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-purple-600", children: [
              Math.round(statistics.response.total_score / Math.max(...statistics.questionStats.map((q) => q.maxMarks)) / statistics.questionStats.length * 100),
              "%"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 168,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Average Performance" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 171,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 167,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500 flex items-center justify-center", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Calendar, { className: "h-4 w-4 mr-1" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
                lineNumber: 176,
                columnNumber: 25
              }, globalThis),
              "Submitted"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 175,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-gray-900", children: formatDate(statistics.response.submitted_at) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 179,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 174,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 152,
          columnNumber: 19
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 146,
        columnNumber: 17
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "mx-auto h-12 w-12 text-yellow-500" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 187,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-lg font-medium text-yellow-900", children: "No Submission Found" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 188,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-yellow-700", children: "This student has not submitted a response for this assessment yet." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 189,
          columnNumber: 19
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 186,
        columnNumber: 17
      }, globalThis),
      statistics.batchComparison && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border border-gray-200 rounded-lg p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-4 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-5 w-5 text-blue-600 mr-2" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 199,
            columnNumber: 21
          }, globalThis),
          "Batch Performance Comparison"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 198,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-5 gap-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-blue-600", children: [
              statistics.batchComparison.studentRank,
              getRankSuffix(statistics.batchComparison.studentRank)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 205,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Rank in Batch" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 208,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 204,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-green-600", children: Math.round(statistics.batchComparison.batchAverage) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 212,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Batch Average" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 215,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 211,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-yellow-600", children: statistics.batchComparison.batchMax }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 219,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Highest Score" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 222,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 218,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-red-600", children: statistics.batchComparison.batchMin }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 226,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Lowest Score" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 229,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 225,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-purple-600", children: statistics.batchComparison.totalStudents }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 233,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Total Students" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 236,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 232,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 203,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 text-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statistics.batchComparison.studentScore > statistics.batchComparison.batchAverage ? "text-green-800 bg-green-100" : statistics.batchComparison.studentScore === statistics.batchComparison.batchAverage ? "text-yellow-800 bg-yellow-100" : "text-red-800 bg-red-100"}`, children: statistics.batchComparison.studentScore > statistics.batchComparison.batchAverage ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(TrendingUp, { className: "h-4 w-4 mr-1" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 251,
            columnNumber: 27
          }, globalThis),
          "Above Average"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 250,
          columnNumber: 25
        }, globalThis) : statistics.batchComparison.studentScore === statistics.batchComparison.batchAverage ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Target, { className: "h-4 w-4 mr-1" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 256,
            columnNumber: 27
          }, globalThis),
          "At Average"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 255,
          columnNumber: 25
        }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 261,
            columnNumber: 27
          }, globalThis),
          "Below Average"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 260,
          columnNumber: 25
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 242,
          columnNumber: 21
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 241,
          columnNumber: 19
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 197,
        columnNumber: 17
      }, globalThis),
      statistics.questionStats && statistics.questionStats.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border border-gray-200 rounded-lg p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-4 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Award, { className: "h-5 w-5 text-blue-600 mr-2" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 274,
            columnNumber: 21
          }, globalThis),
          "Question-wise Performance"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 273,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("table", { className: "min-w-full divide-y divide-gray-200", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("thead", { className: "bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Question" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 282,
              columnNumber: 27
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Selected Answer" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 285,
              columnNumber: 27
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Marks Earned" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 288,
              columnNumber: 27
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Max Marks" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 291,
              columnNumber: 27
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Performance" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 294,
              columnNumber: 27
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 281,
            columnNumber: 25
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 280,
            columnNumber: 23
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tbody", { className: "bg-white divide-y divide-gray-200", children: statistics.questionStats.map((question, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { className: "hover:bg-gray-50", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-gray-900", children: [
                "Q",
                index + 1
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
                lineNumber: 303,
                columnNumber: 31
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500 max-w-xs truncate", children: question.questionText }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
                lineNumber: 306,
                columnNumber: 31
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 302,
              columnNumber: 29
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 text-sm text-gray-900", children: question.selectedOption }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 310,
              columnNumber: 29
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 text-sm font-medium text-gray-900", children: question.marksEarned }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 313,
              columnNumber: 29
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 text-sm text-gray-900", children: question.maxMarks }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 316,
              columnNumber: 29
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPerformanceColor(question.percentage)}`, children: [
              Math.round(question.percentage),
              "%"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 320,
              columnNumber: 31
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
              lineNumber: 319,
              columnNumber: 29
            }, globalThis)
          ] }, question.questionId, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 301,
            columnNumber: 27
          }, globalThis)) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
            lineNumber: 299,
            columnNumber: 23
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 279,
          columnNumber: 21
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
          lineNumber: 278,
          columnNumber: 19
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 272,
        columnNumber: 17
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
      lineNumber: 112,
      columnNumber: 13
    }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-12", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BarChart3, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 334,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No Statistics Available" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 335,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "Statistics will be available once the student submits their assessment." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 336,
        columnNumber: 15
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
      lineNumber: 333,
      columnNumber: 13
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
      lineNumber: 93,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-6 flex justify-end pt-4 border-t border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      "button",
      {
        onClick: onClose,
        className: "btn-primary",
        children: "Close"
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
        lineNumber: 345,
        columnNumber: 11
      },
      globalThis
    ) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
      lineNumber: 344,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
    lineNumber: 72,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",
    lineNumber: 71,
    columnNumber: 5
  }, globalThis);
};

Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  plugin_title,
  plugin_tooltip,
  plugin_legend
);
const StatisticsDashboard = ({
  isOpen,
  onClose,
  assessment,
  batch,
  onLoadStatistics,
  loading: _loading
}) => {
  const [statistics, setStatistics] = reactExports.useState(null);
  const [loadingStats, setLoadingStats] = reactExports.useState(false);
  const [error, setError] = reactExports.useState("");
  const [selectedQuestion, setSelectedQuestion] = reactExports.useState(null);
  reactExports.useEffect(() => {
    if (isOpen && assessment && batch) {
      loadStatistics();
    }
  }, [isOpen, assessment, batch]);
  const loadStatistics = async () => {
    try {
      setLoadingStats(true);
      setError("");
      const stats = await onLoadStatistics(assessment.id, batch.id);
      setStatistics(stats);
    } catch (error2) {
      setError(error2.message);
    } finally {
      setLoadingStats(false);
    }
  };
  const getScoreDistributionChartData = () => {
    if (!statistics?.batchStats?.scoreDistribution) {
      return null;
    }
    return {
      labels: statistics.batchStats.scoreDistribution.map((range) => range.label),
      datasets: [
        {
          label: "Number of Students",
          data: statistics.batchStats.scoreDistribution.map((range) => range.count),
          backgroundColor: [
            "#10B981",
            // Green for 90-100%
            "#3B82F6",
            // Blue for 80-89%
            "#8B5CF6",
            // Purple for 70-79%
            "#F59E0B",
            // Yellow for 60-69%
            "#EF4444",
            // Red for 50-59%
            "#6B7280"
            // Gray for below 50%
          ],
          borderColor: [
            "#059669",
            "#2563EB",
            "#7C3AED",
            "#D97706",
            "#DC2626",
            "#4B5563"
          ],
          borderWidth: 1
        }
      ]
    };
  };
  const getQuestionOptionChartData = (questionStat) => {
    if (!questionStat?.options) {
      return null;
    }
    return {
      labels: questionStat.options.map((option) => `Option ${option.option_order}: ${option.option_text.substring(0, 30)}...`),
      datasets: [
        {
          data: questionStat.options.map((option) => option.percentage),
          backgroundColor: [
            "#3B82F6",
            "#10B981",
            "#F59E0B",
            "#EF4444",
            "#8B5CF6",
            "#6B7280"
          ],
          borderColor: [
            "#2563EB",
            "#059669",
            "#D97706",
            "#DC2626",
            "#7C3AED",
            "#4B5563"
          ],
          borderWidth: 2
        }
      ]
    };
  };
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "bottom"
      },
      title: {
        display: true,
        text: "Score Distribution"
      }
    }
  };
  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "right"
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.label}: ${context.parsed}%`;
          }
        }
      }
    }
  };
  if (!isOpen) {
    return null;
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-4 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mb-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-xl font-bold text-gray-900 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BarChart3, { className: "h-6 w-6 mr-2 text-blue-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 171,
            columnNumber: 15
          }, globalThis),
          "Assessment Statistics"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 170,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-1", children: [
          assessment?.name,
          " - ",
          batch?.name
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 174,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 169,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: loadStatistics,
            disabled: loadingStats,
            className: "btn-outline flex items-center space-x-1",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RefreshCw, { className: `h-4 w-4 ${loadingStats ? "animate-spin" : ""}` }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 184,
                columnNumber: 15
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Refresh" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 185,
                columnNumber: 15
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 179,
            columnNumber: 13
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: onClose,
            className: "text-gray-400 hover:text-gray-600",
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-6 w-6" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 191,
              columnNumber: 15
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 187,
            columnNumber: 13
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 178,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
      lineNumber: 168,
      columnNumber: 9
    }, globalThis),
    loadingStats && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-12", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 199,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-4 text-gray-600", children: "Loading statistics..." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 200,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
      lineNumber: 198,
      columnNumber: 11
    }, globalThis),
    error && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-red-50 border border-red-200 rounded-md p-4 mb-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5 text-red-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 208,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-sm font-medium text-red-800", children: "Error Loading Statistics" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 210,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-700", children: error }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 211,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 209,
        columnNumber: 15
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
      lineNumber: 207,
      columnNumber: 13
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
      lineNumber: 206,
      columnNumber: 11
    }, globalThis),
    statistics && !loadingStats && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-blue-50 p-4 rounded-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-8 w-8 text-blue-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 224,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm font-medium text-blue-600", children: "Total Students" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 226,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-2xl font-bold text-blue-900", children: statistics.batchStats.totalStudents }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 227,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 225,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 223,
          columnNumber: 17
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 222,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-green-50 p-4 rounded-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Target, { className: "h-8 w-8 text-green-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 234,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm font-medium text-green-600", children: "Average Score" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 236,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-2xl font-bold text-green-900", children: [
              statistics.batchStats.averageScore,
              "%"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 237,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 235,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 233,
          columnNumber: 17
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 232,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-yellow-50 p-4 rounded-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Award, { className: "h-8 w-8 text-yellow-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 244,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm font-medium text-yellow-600", children: "Highest Score" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 246,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-2xl font-bold text-yellow-900", children: [
              statistics.batchStats.highestScore,
              "%"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 247,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 245,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 243,
          columnNumber: 17
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 242,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-purple-50 p-4 rounded-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(TrendingUp, { className: "h-8 w-8 text-purple-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 254,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm font-medium text-purple-600", children: "Pass Rate" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 256,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-2xl font-bold text-purple-900", children: [
              statistics.batchStats.passRate,
              "%"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 257,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 255,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 253,
          columnNumber: 17
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 252,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 221,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-4", children: "Score Distribution" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 267,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "h-64", children: getScoreDistributionChartData() && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Bar, { data: getScoreDistributionChartData(), options: chartOptions }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 270,
            columnNumber: 21
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 268,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 266,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-4", children: "Question Analysis" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 277,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-2 max-h-64 overflow-y-auto", children: statistics.questionStats.map((questionStat, _) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "div",
            {
              className: `p-3 border rounded cursor-pointer transition-colors ${selectedQuestion?.questionId === questionStat.questionId ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"}`,
              onClick: () => setSelectedQuestion(questionStat),
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm font-medium", children: [
                    "Q",
                    questionStat.questionOrder
                  ] }, void 0, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                    lineNumber: 290,
                    columnNumber: 25
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-xs text-gray-500", children: [
                    questionStat.responseRate,
                    "% response rate"
                  ] }, void 0, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                    lineNumber: 291,
                    columnNumber: 25
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                  lineNumber: 289,
                  columnNumber: 23
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-600 mt-1 truncate", children: questionStat.questionText }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                  lineNumber: 293,
                  columnNumber: 23
                }, globalThis)
              ]
            },
            questionStat.questionId,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 280,
              columnNumber: 21
            },
            globalThis
          )) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 278,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 276,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 264,
        columnNumber: 13
      }, globalThis),
      selectedQuestion && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-4", children: [
          "Question ",
          selectedQuestion.questionOrder,
          " - Option Distribution"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 305,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mb-4", children: selectedQuestion.questionText }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 308,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "h-64", children: getQuestionOptionChartData(selectedQuestion) && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Pie, { data: getQuestionOptionChartData(selectedQuestion), options: pieChartOptions }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 313,
            columnNumber: 23
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 311,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-2", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "font-medium text-gray-900", children: "Option Details:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 318,
              columnNumber: 21
            }, globalThis),
            selectedQuestion.options.map((option, _) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center p-2 bg-gray-50 rounded", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm font-medium", children: [
                  "Option ",
                  option.option_order,
                  ":"
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                  lineNumber: 322,
                  columnNumber: 27
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm text-gray-600 ml-2", children: option.option_text }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                  lineNumber: 323,
                  columnNumber: 27
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 321,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-right", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium", children: [
                  option.percentage,
                  "%"
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                  lineNumber: 326,
                  columnNumber: 27
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-xs text-gray-500", children: [
                  "(",
                  option.count,
                  " students)"
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                  lineNumber: 327,
                  columnNumber: 27
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-xs text-blue-600", children: [
                  option.marks,
                  " marks"
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                  lineNumber: 328,
                  columnNumber: 27
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 325,
                columnNumber: 25
              }, globalThis)
            ] }, option.id, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 320,
              columnNumber: 23
            }, globalThis)),
            selectedQuestion.mostSelectedOption && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 p-3 bg-green-50 border border-green-200 rounded", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4 text-green-600" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                  lineNumber: 336,
                  columnNumber: 27
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "ml-2 text-sm font-medium text-green-800", children: "Most Selected (Recommended Answer):" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                  lineNumber: 337,
                  columnNumber: 27
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 335,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-green-700 mt-1", children: [
                "Option ",
                selectedQuestion.mostSelectedOption.option_order,
                ": ",
                selectedQuestion.mostSelectedOption.option_text
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 339,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-green-600 mt-1", children: [
                selectedQuestion.mostSelectedOption.marks,
                " marks - Selected by ",
                selectedQuestion.mostSelectedOption.percentage,
                "% of students"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 342,
                columnNumber: 25
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 334,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 317,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 310,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 304,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-4", children: "Student Rankings" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 354,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "overflow-x-auto", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("table", { className: "min-w-full divide-y divide-gray-200", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("thead", { className: "bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Rank" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 359,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Student" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 360,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Student ID" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 361,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Score" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 362,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Percentile" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 363,
                columnNumber: 23
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 358,
              columnNumber: 21
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 357,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tbody", { className: "bg-white divide-y divide-gray-200", children: statistics.studentRankings.slice(0, 10).map((student) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900", children: [
                "#",
                student.rank
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 369,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: [
                student.first_name,
                " ",
                student.last_name
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 372,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500", children: student.student_id }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 375,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: [
                student.total_score,
                "%"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 378,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500", children: [
                student.percentile,
                "%"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
                lineNumber: 381,
                columnNumber: 25
              }, globalThis)
            ] }, student.id, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 368,
              columnNumber: 23
            }, globalThis)) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
              lineNumber: 366,
              columnNumber: 19
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 356,
            columnNumber: 17
          }, globalThis),
          statistics.studentRankings.length > 10 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-500 text-center mt-4", children: [
            "Showing top 10 students. Total: ",
            statistics.studentRankings.length,
            " students"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
            lineNumber: 389,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
          lineNumber: 355,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 353,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
      lineNumber: 219,
      columnNumber: 11
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-6 flex justify-end", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      "button",
      {
        onClick: onClose,
        className: "btn-outline",
        children: "Close"
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
        lineNumber: 400,
        columnNumber: 11
      },
      globalThis
    ) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
      lineNumber: 399,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
    lineNumber: 166,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",
    lineNumber: 165,
    columnNumber: 5
  }, globalThis);
};

const TakeAssessment = () => {
  const navigate = useNavigate();
  const {
    getUserAccessibleBatches,
    getAssessmentsForBatch,
    getBatchStudentsWithProgress,
    canStudentAccessAssessment,
    generateAssessmentForm,
    uploadStudentResponse,
    getStudentStatistics,
    getBatchCompletionStatus,
    processAssessmentForPeerReview,
    getAssessmentStatistics
  } = useDatabase();
  const { user } = useAuth();
  const [batches, setBatches] = reactExports.useState([]);
  const [selectedBatch, setSelectedBatch] = reactExports.useState(null);
  const [assessments, setAssessments] = reactExports.useState([]);
  const [selectedAssessment, setSelectedAssessment] = reactExports.useState(null);
  const [students, setStudents] = reactExports.useState([]);
  const [loading, setLoading] = reactExports.useState(true);
  const [loadingAssessments, setLoadingAssessments] = reactExports.useState(false);
  const [loadingStudents, setLoadingStudents] = reactExports.useState(false);
  const [message, setMessage] = reactExports.useState({ type: "", text: "" });
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [statusFilter, setStatusFilter] = reactExports.useState("all");
  const [showFilters, setShowFilters] = reactExports.useState(false);
  const [uploadModalOpen, setUploadModalOpen] = reactExports.useState(false);
  const [statisticsModalOpen, setStatisticsModalOpen] = reactExports.useState(false);
  const [selectedStudentForAction, setSelectedStudentForAction] = reactExports.useState(null);
  const [actionLoading, setActionLoading] = reactExports.useState({});
  const [uploading, setUploading] = reactExports.useState(false);
  const [loadingStats, setLoadingStats] = reactExports.useState(false);
  const [completionStatus, setCompletionStatus] = reactExports.useState(null);
  const [processing, setProcessing] = reactExports.useState(false);
  const [showProcessConfirmation, setShowProcessConfirmation] = reactExports.useState(false);
  const [showStatistics, setShowStatistics] = reactExports.useState(false);
  reactExports.useEffect(() => {
    loadUserBatches();
  }, []);
  reactExports.useEffect(() => {
    if (selectedBatch) {
      loadBatchAssessments();
    } else {
      setAssessments([]);
      setSelectedAssessment(null);
      setStudents([]);
    }
  }, [selectedBatch]);
  reactExports.useEffect(() => {
    if (selectedBatch && selectedAssessment) {
      loadBatchStudents();
      if (user.role === "teacher") {
        loadCompletionStatus();
      }
    } else {
      setStudents([]);
      setCompletionStatus(null);
    }
  }, [selectedBatch, selectedAssessment]);
  reactExports.useEffect(() => {
    if (selectedBatch && selectedAssessment && user.role === "teacher") {
      const interval = setInterval(() => {
        loadCompletionStatus();
      }, 3e4);
      return () => clearInterval(interval);
    }
  }, [selectedBatch, selectedAssessment]);
  const loadUserBatches = async () => {
    try {
      setLoading(true);
      const batchData = await getUserAccessibleBatches(
        user.id,
        user.role,
        user.institution_id
      );
      setBatches(batchData);
      if (user.role === "student" && batchData.length === 1) {
        setSelectedBatch(batchData[0]);
      }
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setLoading(false);
    }
  };
  const loadBatchAssessments = async () => {
    try {
      setLoadingAssessments(true);
      const assessmentData = await getAssessmentsForBatch(
        selectedBatch.id,
        user.id,
        user.role
      );
      setAssessments(assessmentData);
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setLoadingAssessments(false);
    }
  };
  const loadBatchStudents = async () => {
    try {
      setLoadingStudents(true);
      const studentData = await getBatchStudentsWithProgress(
        selectedBatch.id,
        selectedAssessment.id,
        user.id,
        user.role
      );
      setStudents(studentData);
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setLoadingStudents(false);
    }
  };
  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: "", text: "" }), 5e3);
  };
  const handleBatchSelect = (batch) => {
    setSelectedBatch(batch);
    setSelectedAssessment(null);
    setStudents([]);
  };
  const handleAssessmentSelect = (assessment) => {
    setSelectedAssessment(assessment);
  };
  const handleTakeAssessment = async (assessment) => {
    try {
      if (user.role === "student") {
        const accessCheck = await canStudentAccessAssessment(user.id, assessment.id);
        if (!accessCheck.canAccess) {
          showMessage("error", accessCheck.reason);
          return;
        }
      }
      navigate(`/assessment/${assessment.id}/take`);
    } catch (error) {
      showMessage("error", error.message);
    }
  };
  const getAssessmentStatusColor = (status) => {
    switch (status) {
      case "active":
        return "text-green-600 bg-green-100";
      case "overdue":
        return "text-red-600 bg-red-100";
      case "completed":
        return "text-blue-600 bg-blue-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };
  const getStudentStatusColor = (status) => {
    switch (status) {
      case "completed":
        return "text-green-600 bg-green-100";
      case "in_progress":
        return "text-yellow-600 bg-yellow-100";
      case "not_started":
        return "text-gray-600 bg-gray-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };
  const formatDate = (dateString) => {
    if (!dateString) {
      return "Not set";
    }
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  };
  const handleDownloadAssessmentForm = async (student) => {
    const actionKey = `download-form-${student.id}`;
    try {
      setActionLoading((prev) => ({ ...prev, [actionKey]: true }));
      const formData = await generateAssessmentForm(
        selectedAssessment.id,
        user.id,
        user.institution_id
      );
      const blob = new Blob([formData.html], { type: "text/html" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${student.first_name}_${student.last_name}_${formData.filename}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      showMessage("success", `Assessment form downloaded for ${student.first_name} ${student.last_name}`);
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setActionLoading((prev) => ({ ...prev, [actionKey]: false }));
    }
  };
  const handleUploadStudentResponse = (student) => {
    setSelectedStudentForAction(student);
    setUploadModalOpen(true);
  };
  const handleUploadResponse = async (responses) => {
    try {
      setUploading(true);
      const result = await uploadStudentResponse(
        selectedAssessment.id,
        selectedStudentForAction.id,
        responses,
        user.id,
        user.institution_id
      );
      showMessage("success", `Response uploaded successfully. Score: ${result.totalScore}`);
      loadBatchStudents();
      loadCompletionStatus();
      setUploadModalOpen(false);
      setSelectedStudentForAction(null);
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setUploading(false);
    }
  };
  const loadCompletionStatus = async () => {
    try {
      const status = await getBatchCompletionStatus(
        selectedBatch.id,
        selectedAssessment.id,
        user.id,
        user.institution_id
      );
      setCompletionStatus(status);
    } catch (error) {
      console.error("Failed to load completion status:", error);
    }
  };
  const handleProcessAssessment = () => {
    setShowProcessConfirmation(true);
  };
  const confirmProcessAssessment = async () => {
    try {
      setProcessing(true);
      setShowProcessConfirmation(false);
      const result = await processAssessmentForPeerReview(
        selectedAssessment.id,
        selectedBatch.id,
        user.id,
        user.institution_id
      );
      showMessage("success", `${result.message} Statistics and PDF reports have been generated successfully.`);
      loadCompletionStatus();
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setProcessing(false);
    }
  };
  const handleDownloadPeerReviewPDF = async (student) => {
    const actionKey = `download-pdf-${student.id}`;
    try {
      setActionLoading((prev) => ({ ...prev, [actionKey]: true }));
      showMessage("info", "Peer review PDF generation will be available when peer reviews are implemented");
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setActionLoading((prev) => ({ ...prev, [actionKey]: false }));
    }
  };
  const handleViewStatistics = (student) => {
    setSelectedStudentForAction(student);
    setStatisticsModalOpen(true);
  };
  const handleLoadStatistics = async (studentId, assessmentId) => {
    setLoadingStats(true);
    try {
      return await getStudentStatistics(
        studentId,
        assessmentId,
        user.id,
        user.institution_id
      );
    } finally {
      setLoadingStats(false);
    }
  };
  const handleLoadAssessmentStatistics = async (assessmentId, batchId) => {
    try {
      return await getAssessmentStatistics(assessmentId, batchId, user.id);
    } catch (error) {
      throw new Error(`Failed to load assessment statistics: ${error.message}`);
    }
  };
  const filteredAssessments = assessments.filter((assessment) => {
    const matchesSearch = !searchTerm || assessment.name.toLowerCase().includes(searchTerm.toLowerCase()) || assessment.assessment_type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || assessment.status === statusFilter;
    return matchesSearch && matchesStatus;
  });
  if (!user || !["teacher", "student", "admin", "super_admin"].includes(user.role)) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-center min-h-screen", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "mx-auto h-12 w-12 text-red-500" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 409,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "mt-2 text-lg font-medium text-gray-900", children: "Access Denied" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 410,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "You need appropriate privileges to access assessments." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 411,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 408,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 407,
      columnNumber: 7
    }, globalThis);
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", "data-testid": "take-assessment", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-2xl font-bold text-gray-900", children: "Take Assessment" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 424,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: user.role === "student" ? "Access your assigned assessments and track your progress" : "Monitor student progress and manage assessment access" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 425,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 423,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: loadUserBatches,
          className: "btn-outline flex items-center space-x-2",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RefreshCw, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 436,
              columnNumber: 11
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Refresh" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 437,
              columnNumber: 11
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 432,
          columnNumber: 9
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 422,
      columnNumber: 7
    }, globalThis),
    message.text && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `rounded-md p-4 ${message.type === "error" ? "bg-red-50" : "bg-green-50"}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: message.type === "error" ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5 text-red-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 447,
        columnNumber: 17
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-green-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 449,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 445,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: `text-sm ${message.type === "error" ? "text-red-800" : "text-green-800"}`, children: message.text }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 453,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 452,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 444,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 443,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-6 py-4 border-b border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 465,
          columnNumber: 13
        }, globalThis),
        "Step 1: Select Batch"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 464,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 463,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: loading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 473,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-gray-500", children: "Loading batches..." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 474,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 472,
        columnNumber: 13
      }, globalThis) : batches.length > 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", children: batches.map((batch) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "div",
        {
          className: `border rounded-lg p-4 cursor-pointer transition-colors ${selectedBatch?.id === batch.id ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"}`,
          onClick: () => handleBatchSelect(batch),
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-start justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-gray-900", children: batch.name }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 490,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-500 mt-1", children: batch.description || "No description" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 491,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-2 space-y-1", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center text-xs text-gray-500", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Calendar, { className: "h-3 w-3 mr-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 494,
                    columnNumber: 27
                  }, globalThis),
                  batch.academic_year
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 493,
                  columnNumber: 25
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center text-xs text-gray-500", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BookOpen, { className: "h-3 w-3 mr-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 498,
                    columnNumber: 27
                  }, globalThis),
                  batch.course_type
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 497,
                  columnNumber: 25
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center text-xs text-gray-500", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(User, { className: "h-3 w-3 mr-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 502,
                    columnNumber: 27
                  }, globalThis),
                  batch.student_count,
                  " students"
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 501,
                  columnNumber: 25
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 492,
                columnNumber: 23
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 489,
              columnNumber: 21
            }, globalThis),
            selectedBatch?.id === batch.id && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-blue-600" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 508,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 488,
            columnNumber: 19
          }, globalThis)
        },
        batch.id,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 479,
          columnNumber: 17
        },
        globalThis
      )) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 477,
        columnNumber: 13
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 516,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No batches available" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 517,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: user.role === "student" ? "You are not enrolled in any batches yet." : "You have not created any batches yet." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 518,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 515,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 470,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 462,
      columnNumber: 7
    }, globalThis),
    selectedBatch && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-6 py-4 border-b border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 535,
            columnNumber: 17
          }, globalThis),
          "Step 2: Select Assessment"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 534,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: [
          "Batch: ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium", children: selectedBatch.name }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 539,
            columnNumber: 24
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 538,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 533,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 532,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4 flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1 max-w-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Search, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 549,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "text",
                placeholder: "Search assessments...",
                className: "pl-10 input",
                value: searchTerm,
                onChange: (e) => setSearchTerm(e.target.value)
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 550,
                columnNumber: 19
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 548,
            columnNumber: 17
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 547,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: () => setShowFilters(!showFilters),
              className: `btn-outline flex items-center space-x-2 ${showFilters ? "bg-blue-50 border-blue-300" : ""}`,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Filter, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 565,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Filters" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 566,
                  columnNumber: 19
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 561,
              columnNumber: 17
            },
            globalThis
          ) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 560,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 546,
          columnNumber: 13
        }, globalThis),
        showFilters && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4 p-4 bg-gray-50 rounded-md", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Status" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 576,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              className: "input",
              value: statusFilter,
              onChange: (e) => setStatusFilter(e.target.value),
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "all", children: "All Status" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 584,
                  columnNumber: 23
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "active", children: "Active" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 585,
                  columnNumber: 23
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "overdue", children: "Overdue" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 586,
                  columnNumber: 23
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "completed", children: "Completed" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 587,
                  columnNumber: 23
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 579,
              columnNumber: 21
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 575,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 574,
          columnNumber: 17
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 573,
          columnNumber: 15
        }, globalThis),
        loadingAssessments ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 597,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-gray-500", children: "Loading assessments..." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 598,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 596,
          columnNumber: 15
        }, globalThis) : filteredAssessments.length > 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-4", children: filteredAssessments.map((assessment) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "div",
          {
            className: `border rounded-lg p-4 cursor-pointer transition-colors ${selectedAssessment?.id === assessment.id ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"}`,
            onClick: () => handleAssessmentSelect(assessment),
            "data-testid": "question-container",
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-start justify-between", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-gray-900", children: assessment.name }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 616,
                    columnNumber: 27
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getAssessmentStatusColor(assessment.status)}`, children: assessment.status }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 617,
                    columnNumber: 27
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 615,
                  columnNumber: 25
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BookOpen, { className: "h-4 w-4 mr-1" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 624,
                      columnNumber: 29
                    }, globalThis),
                    assessment.assessment_type
                  ] }, void 0, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 623,
                    columnNumber: 27
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Calendar, { className: "h-4 w-4 mr-1" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 628,
                      columnNumber: 29
                    }, globalThis),
                    "Due: ",
                    formatDate(assessment.due_date)
                  ] }, void 0, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 627,
                    columnNumber: 27
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "h-4 w-4 mr-1" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 632,
                      columnNumber: 29
                    }, globalThis),
                    assessment.question_count,
                    " questions"
                  ] }, void 0, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 631,
                    columnNumber: 27
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 622,
                  columnNumber: 25
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-2 flex items-center justify-between", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-xs text-gray-500", children: [
                    "Created by: ",
                    assessment.created_by_name
                  ] }, void 0, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 638,
                    columnNumber: 27
                  }, globalThis),
                  user.role === "student" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                    "button",
                    {
                      onClick: (e) => {
                        e.stopPropagation();
                        handleTakeAssessment(assessment);
                      },
                      className: "btn-primary text-sm flex items-center space-x-1",
                      children: [
                        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Play, { className: "h-3 w-3" }, void 0, false, {
                          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                          lineNumber: 649,
                          columnNumber: 31
                        }, globalThis),
                        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Take Assessment" }, void 0, false, {
                          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                          lineNumber: 650,
                          columnNumber: 31
                        }, globalThis)
                      ]
                    },
                    void 0,
                    true,
                    {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 642,
                      columnNumber: 29
                    },
                    globalThis
                  )
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 637,
                  columnNumber: 25
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 614,
                columnNumber: 23
              }, globalThis),
              selectedAssessment?.id === assessment.id && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-blue-600 ml-3" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 657,
                columnNumber: 25
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 613,
              columnNumber: 21
            }, globalThis)
          },
          assessment.id,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 603,
            columnNumber: 19
          },
          globalThis
        )) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 601,
          columnNumber: 15
        }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 665,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No assessments found" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 666,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: searchTerm || statusFilter !== "all" ? "Try adjusting your search or filters." : "No assessments have been assigned to this batch yet." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 667,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 664,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 544,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 531,
      columnNumber: 9
    }, globalThis),
    selectedBatch && selectedAssessment && user.role === "teacher" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-6 py-4 border-b border-gray-200", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(GraduationCap, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 685,
              columnNumber: 17
            }, globalThis),
            "Step 3: Student Progress"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 684,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: [
            "Assessment: ",
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium", children: selectedAssessment.name }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 689,
              columnNumber: 29
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 688,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 683,
          columnNumber: 13
        }, globalThis),
        completionStatus && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 flex items-center justify-between", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-600", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-900", children: [
                completionStatus.statistics.completedStudents,
                " of ",
                completionStatus.statistics.totalStudents
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 698,
                columnNumber: 21
              }, globalThis),
              " students completed"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 697,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1 bg-gray-200 rounded-full h-2 w-48", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "div",
              {
                className: `h-2 rounded-full transition-all duration-300 ${completionStatus.statistics.allCompleted ? "bg-green-600" : "bg-blue-600"}`,
                style: { width: `${completionStatus.statistics.completionPercentage}%` }
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 703,
                columnNumber: 21
              },
              globalThis
            ) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 702,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-gray-900", children: [
              completionStatus.statistics.completionPercentage,
              "%"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 710,
              columnNumber: 19
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 696,
            columnNumber: 17
          }, globalThis),
          completionStatus.statistics.allCompleted && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center text-green-600", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 717,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm font-medium", children: "All students completed" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 718,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 716,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 695,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 682,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
        completionStatus && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-6 flex justify-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                onClick: handleProcessAssessment,
                disabled: !completionStatus.canProcess || processing,
                className: `btn-primary flex items-center space-x-2 px-6 py-3 text-lg ${!completionStatus.canProcess ? "opacity-50 cursor-not-allowed" : ""}`,
                title: completionStatus.processedStatus ? "Assessment has already been processed" : !completionStatus.statistics.allCompleted ? `${completionStatus.statistics.pendingStudents} students have not completed their responses` : "Process assessment for peer review allocation",
                children: processing ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-5 w-5 border-b-2 border-white" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 747,
                    columnNumber: 27
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Processing..." }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 748,
                    columnNumber: 27
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 746,
                  columnNumber: 25
                }, globalThis) : completionStatus.processedStatus ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 752,
                    columnNumber: 27
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Assessment Processed" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 753,
                    columnNumber: 27
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 751,
                  columnNumber: 25
                }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Play, { className: "h-5 w-5" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 757,
                    columnNumber: 27
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Process Assessment" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 758,
                    columnNumber: 27
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 756,
                  columnNumber: 25
                }, globalThis)
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 731,
                columnNumber: 21
              },
              globalThis
            ),
            completionStatus.processedStatus && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                onClick: () => setShowStatistics(true),
                className: "btn-outline flex items-center space-x-2 px-6 py-3 text-lg",
                title: "View detailed assessment statistics and analytics",
                children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BarChart3, { className: "h-5 w-5" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 770,
                    columnNumber: 25
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "See Stats" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                    lineNumber: 771,
                    columnNumber: 25
                  }, globalThis)
                ]
              },
              void 0,
              true,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 765,
                columnNumber: 23
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 730,
            columnNumber: 19
          }, globalThis),
          !completionStatus.canProcess && !completionStatus.processedStatus && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-gray-500", children: [
            completionStatus.statistics.pendingStudents,
            " student",
            completionStatus.statistics.pendingStudents !== 1 ? "s" : "",
            " still need to complete their response",
            completionStatus.statistics.pendingStudents !== 1 ? "s" : ""
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 777,
            columnNumber: 21
          }, globalThis),
          completionStatus.processedStatus && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-green-600", children: [
            "Processed on ",
            formatDate(completionStatus.processedStatus.processed_at)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 783,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 729,
          columnNumber: 17
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 728,
          columnNumber: 15
        }, globalThis),
        loadingStudents ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 793,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-gray-500", children: "Loading student progress..." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 794,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 792,
          columnNumber: 15
        }, globalThis) : students.length > 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("table", { className: "min-w-full divide-y divide-gray-200", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("thead", { className: "bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Student" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 801,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Student ID" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 804,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Status" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 807,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Submitted" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 810,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Score" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 813,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Actions" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 816,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 800,
            columnNumber: 21
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 799,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tbody", { className: "bg-white divide-y divide-gray-200", children: students.map((student) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { className: "hover:bg-gray-50", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-gray-900", children: [
                student.first_name,
                " ",
                student.last_name
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 825,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: student.email }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 828,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 824,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: student.student_id }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 830,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
              student.assessment_status === "completed" ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-center w-6 h-6 bg-green-100 rounded-full", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4 text-green-600" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 838,
                columnNumber: 33
              }, globalThis) }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 837,
                columnNumber: 31
              }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-center w-6 h-6 bg-yellow-100 rounded-full", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Clock, { className: "h-4 w-4 text-yellow-600" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 842,
                columnNumber: 33
              }, globalThis) }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 841,
                columnNumber: 31
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStudentStatusColor(student.assessment_status)}`, children: student.assessment_status === "completed" ? "Completed" : "Pending" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 847,
                columnNumber: 29
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 834,
              columnNumber: 27
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 833,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500", children: student.submitted_at ? formatDate(student.submitted_at) : "-" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 852,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: student.total_score !== null ? `${student.total_score}%` : "-" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 855,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm font-medium", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex flex-col lg:flex-row lg:items-center lg:space-x-2 space-y-2 lg:space-y-0", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "button",
                {
                  onClick: () => handleDownloadAssessmentForm(student),
                  disabled: actionLoading[`download-form-${student.id}`],
                  className: "btn-outline text-xs flex items-center space-x-1 px-2 py-1",
                  title: "Download Assessment Form",
                  children: [
                    actionLoading[`download-form-${student.id}`] ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 868,
                      columnNumber: 33
                    }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Download, { className: "h-3 w-3" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 870,
                      columnNumber: 33
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "hidden lg:inline", children: "Form" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 872,
                      columnNumber: 31
                    }, globalThis)
                  ]
                },
                void 0,
                true,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 861,
                  columnNumber: 29
                },
                globalThis
              ),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "button",
                {
                  onClick: () => handleUploadStudentResponse(student),
                  disabled: student.assessment_status === "completed",
                  className: "btn-outline text-xs flex items-center space-x-1 px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed",
                  title: "Upload Student Response",
                  children: [
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Upload, { className: "h-3 w-3" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 882,
                      columnNumber: 31
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "hidden lg:inline", children: "Upload" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 883,
                      columnNumber: 31
                    }, globalThis)
                  ]
                },
                void 0,
                true,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 876,
                  columnNumber: 29
                },
                globalThis
              ),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "button",
                {
                  onClick: () => handleDownloadPeerReviewPDF(student),
                  disabled: actionLoading[`download-pdf-${student.id}`] || student.assessment_status !== "completed",
                  className: "btn-outline text-xs flex items-center space-x-1 px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed",
                  title: "Download Peer Review PDF",
                  children: [
                    actionLoading[`download-pdf-${student.id}`] ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 894,
                      columnNumber: 33
                    }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileDown, { className: "h-3 w-3" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 896,
                      columnNumber: 33
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "hidden lg:inline", children: "PDF" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 898,
                      columnNumber: 31
                    }, globalThis)
                  ]
                },
                void 0,
                true,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 887,
                  columnNumber: 29
                },
                globalThis
              ),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "button",
                {
                  onClick: () => handleViewStatistics(student),
                  disabled: student.assessment_status !== "completed",
                  className: "btn-outline text-xs flex items-center space-x-1 px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed",
                  title: "View Statistics",
                  children: [
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BarChart3, { className: "h-3 w-3" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 908,
                      columnNumber: 31
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "hidden lg:inline", children: "Stats" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                      lineNumber: 909,
                      columnNumber: 31
                    }, globalThis)
                  ]
                },
                void 0,
                true,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 902,
                  columnNumber: 29
                },
                globalThis
              )
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 859,
              columnNumber: 27
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 858,
              columnNumber: 25
            }, globalThis)
          ] }, student.id, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 823,
            columnNumber: 23
          }, globalThis)) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 821,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 798,
          columnNumber: 17
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 797,
          columnNumber: 15
        }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(GraduationCap, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 920,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No students found" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 921,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "No students are enrolled in this batch." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 922,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 919,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 725,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 681,
      columnNumber: 9
    }, globalThis),
    selectedBatch && selectedAssessment && user.role === "student" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-6 py-4 border-b border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Play, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 936,
          columnNumber: 15
        }, globalThis),
        "Assessment Access"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 935,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 934,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-blue-50 border border-blue-200 rounded-md p-4 mb-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Info, { className: "h-5 w-5 text-blue-400 mt-0.5 mr-3" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 944,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-blue-900", children: "Assessment Information" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 946,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-blue-800 mt-1 space-y-1", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Assessment:" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 948,
                  columnNumber: 24
                }, globalThis),
                " ",
                selectedAssessment.name
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 948,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Type:" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 949,
                  columnNumber: 24
                }, globalThis),
                " ",
                selectedAssessment.assessment_type
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 949,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Due Date:" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 950,
                  columnNumber: 24
                }, globalThis),
                " ",
                formatDate(selectedAssessment.due_date)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 950,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Questions:" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 951,
                  columnNumber: 24
                }, globalThis),
                " ",
                selectedAssessment.question_count
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                lineNumber: 951,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 947,
              columnNumber: 19
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 945,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 943,
          columnNumber: 15
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 942,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: () => handleTakeAssessment(selectedAssessment),
              className: "btn-primary flex items-center space-x-2 mx-auto px-8 py-3 text-lg",
              "data-testid": "submit-assessment-button",
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Play, { className: "h-5 w-5" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 963,
                  columnNumber: 17
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Start Assessment" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 964,
                  columnNumber: 17
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowRight, { className: "h-5 w-5" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
                  lineNumber: 965,
                  columnNumber: 17
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
              lineNumber: 958,
              columnNumber: 15
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-4 text-sm text-gray-500", children: "Make sure you have enough time to complete the assessment before starting." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 968,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 957,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 941,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 933,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      UploadResponseModal,
      {
        isOpen: uploadModalOpen,
        onClose: () => {
          setUploadModalOpen(false);
          setSelectedStudentForAction(null);
        },
        student: selectedStudentForAction,
        assessment: selectedAssessment,
        onUpload: handleUploadResponse,
        uploading
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 977,
        columnNumber: 7
      },
      globalThis
    ),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      StudentStatisticsModal,
      {
        isOpen: statisticsModalOpen,
        onClose: () => {
          setStatisticsModalOpen(false);
          setSelectedStudentForAction(null);
        },
        student: selectedStudentForAction,
        assessment: selectedAssessment,
        onLoadStatistics: handleLoadStatistics,
        loading: loadingStats
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 990,
        columnNumber: 7
      },
      globalThis
    ),
    showProcessConfirmation && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-3 text-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-6 w-6 text-yellow-600" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 1008,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 1007,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 mt-4", children: "Process Assessment for Peer Review?" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 1010,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-2 px-7 py-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-500", children: [
          'This will process the assessment "',
          selectedAssessment?.name,
          '" for peer review allocation. All ',
          completionStatus?.statistics.totalStudents,
          " students have completed their responses."
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 1014,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-500 mt-2", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "This action cannot be undone." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 1019,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
          lineNumber: 1018,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 1013,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "items-center px-4 py-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setShowProcessConfirmation(false),
            className: "btn-outline flex-1",
            children: "Cancel"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 1024,
            columnNumber: 19
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: confirmProcessAssessment,
            className: "btn-primary flex-1",
            children: "Process Assessment"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
            lineNumber: 1030,
            columnNumber: 19
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 1023,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 1022,
        columnNumber: 15
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 1006,
      columnNumber: 13
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 1005,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
      lineNumber: 1004,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      StatisticsDashboard,
      {
        isOpen: showStatistics,
        onClose: () => setShowStatistics(false),
        assessment: selectedAssessment,
        batch: selectedBatch,
        onLoadStatistics: handleLoadAssessmentStatistics,
        loading: false
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
        lineNumber: 1044,
        columnNumber: 7
      },
      globalThis
    )
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",
    lineNumber: 420,
    columnNumber: 5
  }, globalThis);
};

export { TakeAssessment as default };
