/**
 * About Modal Component for Peer Review System
 *
 * Displays application information, version, developer details, and system info
 * Triggered from the Help menu
 *
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <hrishi<PERSON><EMAIL>>
 * Company: Ajinkyacreatiion PVT. LTD.
 */

import React, { useState, useEffect } from 'react';
import { X, ExternalLink, Mail, Globe, Code, Info } from 'lucide-react';

const AboutModal = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [aboutInfo, setAboutInfo] = useState(null);

  useEffect(() => {
    const handleShowAbout = (event) => {
      setAboutInfo(event.detail);
      setIsOpen(true);
    };

    window.addEventListener('menu:show-about', handleShowAbout);

    return () => {
      window.removeEventListener('menu:show-about', handleShowAbout);
    };
  }, []);

  const handleClose = () => {
    setIsOpen(false);
    setAboutInfo(null);
  };

  const handleLinkClick = (url) => {
    if (window.electronAPI) {
      window.electronAPI.invoke('shell:openExternal', url);
    } else {
      window.open(url, '_blank');
    }
  };

  if (!isOpen || !aboutInfo) {return null;}

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
              <Info className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">About</h2>
              <p className="text-sm text-gray-600">{aboutInfo.name}</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Application Info */}
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">
              {aboutInfo.name}
            </h3>
            <p className="text-lg text-gray-600 mb-4">Version {aboutInfo.version}</p>
            <p className="text-gray-700 max-w-lg mx-auto">
              {aboutInfo.description}
            </p>
          </div>

          {/* Developer Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <Code className="w-5 h-5 mr-2" />
              Developer
            </h4>
            <div className="space-y-2">
              <p className="text-gray-700">
                <strong>{aboutInfo.developer.name}</strong>
              </p>
              <p className="text-gray-600 text-sm">
                Offline Desktop Application Developer
              </p>
            </div>
          </div>

          {/* Company Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <Globe className="w-5 h-5 mr-2" />
              Company
            </h4>
            <div className="space-y-2">
              <p className="text-gray-700">
                <strong>{aboutInfo.company.name}</strong>
              </p>
              <p className="text-gray-600 text-sm">
                Offline Desktop Application Solutions
              </p>
            </div>
          </div>

          {/* System Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">
              System Information
            </h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Electron Version</p>
                <p className="font-mono text-gray-900">{aboutInfo.electron}</p>
              </div>
              <div>
                <p className="text-gray-600">Node.js Version</p>
                <p className="font-mono text-gray-900">{aboutInfo.node}</p>
              </div>
              <div>
                <p className="text-gray-600">Chrome Version</p>
                <p className="font-mono text-gray-900">{aboutInfo.chrome}</p>
              </div>
              <div>
                <p className="text-gray-600">License</p>
                <p className="font-mono text-gray-900">{aboutInfo.license}</p>
              </div>
            </div>
          </div>

          {/* Copyright */}
          <div className="text-center text-sm text-gray-600 border-t border-gray-200 pt-4">
            <p>{aboutInfo.copyright}</p>
            <p className="mt-1">All rights reserved.</p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={handleClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default AboutModal;
