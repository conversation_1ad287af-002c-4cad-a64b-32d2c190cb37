/**
 * Comprehensive Test Script for Card-Based Navigation System
 * Tests role selection, navigation, and security features
 */

const { execSync, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class CardNavigationTester {
  constructor() {
    this.testResults = [];
    this.passed = 0;
    this.failed = 0;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  test(description, testFn) {
    try {
      this.log(`Testing: ${description}`);
      const result = testFn();
      if (result !== false) {
        this.log(`PASS: ${description}`, 'success');
        this.passed++;
        this.testResults.push({ description, status: 'PASS', error: null });
      } else {
        throw new Error('Test returned false');
      }
    } catch (error) {
      this.log(`FAIL: ${description} - ${error.message}`, 'error');
      this.failed++;
      this.testResults.push({ description, status: 'FAIL', error: error.message });
    }
  }

  async runTests() {
    this.log('🚀 Starting Card-Based Navigation System Tests');
    this.log('================================================');

    // Test 1: Build System
    this.test('Build system works correctly', () => {
      try {
        execSync('npm run build', { stdio: 'pipe' });
        return true;
      } catch (error) {
        throw new Error(`Build failed: ${error.message}`);
      }
    });

    // Test 2: Welcome Screen Component Structure
    this.test('Welcome screen has correct role cards', () => {
      const welcomeScreenPath = path.join(__dirname, 'src/pages/WelcomeScreen.jsx');
      if (!fs.existsSync(welcomeScreenPath)) {
        throw new Error('WelcomeScreen.jsx not found');
      }
      
      const content = fs.readFileSync(welcomeScreenPath, 'utf8');
      
      // Check for merged Administrator role (should be super_admin internally)
      if (!content.includes("id: 'super_admin'") || !content.includes("title: 'Administrator'")) {
        throw new Error('Administrator role not properly configured');
      }
      
      // Check for Teacher role
      if (!content.includes("id: 'teacher'") || !content.includes("title: 'Teacher'")) {
        throw new Error('Teacher role not found');
      }
      
      // Check for Student role
      if (!content.includes("id: 'student'") || !content.includes("title: 'Student'")) {
        throw new Error('Student role not found');
      }
      
      // Ensure no separate super_admin card exists
      const adminMatches = (content.match(/title: 'Administrator'/g) || []).length;
      const superAdminMatches = (content.match(/title: 'Super Administrator'/g) || []).length;
      
      if (superAdminMatches > 0) {
        throw new Error('Found separate Super Administrator card - should be merged');
      }
      
      if (adminMatches !== 1) {
        throw new Error('Should have exactly one Administrator card');
      }
      
      return true;
    });

    // Test 3: AuthContext Configuration
    this.test('AuthContext properly handles role mapping', () => {
      const authContextPath = path.join(__dirname, 'src/contexts/AuthContext.jsx');
      if (!fs.existsSync(authContextPath)) {
        throw new Error('AuthContext.jsx not found');
      }
      
      const content = fs.readFileSync(authContextPath, 'utf8');
      
      // Check for proper role display name mapping
      if (!content.includes("case 'super_admin': return 'Administrator'")) {
        throw new Error('super_admin role not mapped to Administrator display name');
      }
      
      // Check for hardcoded default credentials
      if (!content.includes("username: 'admin'") || !content.includes("password: 'password'")) {
        throw new Error('Default credentials not properly configured');
      }
      
      return true;
    });

    // Test 4: App.jsx Routing Configuration
    this.test('App.jsx has authentication bypass', () => {
      const appPath = path.join(__dirname, 'src/App.jsx');
      if (!fs.existsSync(appPath)) {
        throw new Error('App.jsx not found');
      }
      
      const content = fs.readFileSync(appPath, 'utf8');
      
      // Check that ProtectedRoute is removed
      if (content.includes('ProtectedRoute')) {
        throw new Error('ProtectedRoute still exists - should be removed');
      }
      
      // Check that LoginPage import is removed
      if (content.includes("import LoginPage")) {
        throw new Error('LoginPage import still exists - should be removed');
      }
      
      // Check for direct Layout routing
      if (!content.includes('element={<Layout />}')) {
        throw new Error('Direct Layout routing not configured');
      }
      
      return true;
    });

    // Test 5: Database Default Admin Creation
    this.test('Database creates default admin user', () => {
      const dbPath = path.join(__dirname, 'electron/database/database.js');
      if (!fs.existsSync(dbPath)) {
        throw new Error('database.js not found');
      }
      
      const content = fs.readFileSync(dbPath, 'utf8');
      
      // Check for default admin creation
      if (!content.includes('Always create default admin for simplified card-based navigation')) {
        throw new Error('Default admin creation not configured');
      }
      
      // Check for hardcoded credentials
      if (!content.includes("'admin'") || !content.includes("'password'")) {
        throw new Error('Default admin credentials not configured');
      }
      
      return true;
    });

    // Test 6: Security Configuration
    this.test('Development mode security is properly configured', () => {
      const mainPath = path.join(__dirname, 'electron/main.js');
      if (!fs.existsSync(mainPath)) {
        throw new Error('main.js not found');
      }
      
      const content = fs.readFileSync(mainPath, 'utf8');
      
      // Check for development mode detection
      if (!content.includes("process.env.ELECTRON_DEV === 'true'")) {
        throw new Error('ELECTRON_DEV detection not configured');
      }
      
      // Check for production security measures
      if (!content.includes('devTools: isDev')) {
        throw new Error('DevTools not conditionally enabled');
      }
      
      if (!content.includes('if (isProd)')) {
        throw new Error('Production security checks not implemented');
      }
      
      return true;
    });

    // Test 7: File Structure Cleanup
    this.test('Removed authentication files are actually deleted', () => {
      const loginPagePath = path.join(__dirname, 'src/pages/LoginPage.jsx');
      if (fs.existsSync(loginPagePath)) {
        throw new Error('LoginPage.jsx still exists - should be deleted');
      }
      
      return true;
    });

    // Test 8: Grid Layout Configuration
    this.test('Welcome screen uses correct grid layout for 3 cards', () => {
      const welcomeScreenPath = path.join(__dirname, 'src/pages/WelcomeScreen.jsx');
      const content = fs.readFileSync(welcomeScreenPath, 'utf8');
      
      if (!content.includes('grid-cols-1 md:grid-cols-3')) {
        throw new Error('Grid layout not configured for 3 cards');
      }
      
      return true;
    });

    this.log('================================================');
    this.log(`🏁 Test Results: ${this.passed} passed, ${this.failed} failed`);
    
    if (this.failed > 0) {
      this.log('❌ Some tests failed. Please review the issues above.', 'error');
      process.exit(1);
    } else {
      this.log('✅ All tests passed! Card-based navigation system is ready.', 'success');
    }
  }
}

// Run tests
const tester = new CardNavigationTester();
tester.runTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
