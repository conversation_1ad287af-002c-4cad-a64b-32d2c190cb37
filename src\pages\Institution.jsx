import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useMessage } from '../contexts/MessageContext';
import {
  Building,
  ArrowLeft,
  Save,
  LogOut,

  Mail,
  Phone,
  MapPin,
  Globe,
  Calendar,
  Award,
  AlertCircle,

  Edit3
} from 'lucide-react';

const Institution = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { showMessage } = useMessage();

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [institutionData, setInstitutionData] = useState({
    name: '',
    details: {
      logoPath: '',
      description: '',
      primaryEmail: '',
      contactNumber: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      establishmentDate: '',
      accreditationDetails: '',
      websiteUrl: ''
    }
  });

  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});

  useEffect(() => {
    // Check if user has permission to access this page
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      showMessage('error', 'Access denied. You do not have permission to access this page.');
      navigate('/dashboard');
      return;
    }

    loadInstitutionData();
  }, [user, navigate]);

  const loadInstitutionData = async () => {
    try {
      setLoading(true);
      const electronAPI = window.electronAPI;
      const data = await electronAPI.institution.getDetails(user.institution_id);
      setInstitutionData(data);
      setFormData({
        name: data.name || '',
        ...data.details
      });
    } catch (error) {
      showMessage('error', `Failed to load institution data: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name?.trim()) {
      newErrors.name = 'Institution name is required';
    }

    if (formData.primaryEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.primaryEmail)) {
      newErrors.primaryEmail = 'Please enter a valid email address';
    }

    if (formData.contactNumber && !/^[\+]?[1-9][\d]{0,15}$/.test(formData.contactNumber.replace(/\s/g, ''))) {
      newErrors.contactNumber = 'Please enter a valid phone number';
    }

    if (formData.websiteUrl && !/^https?:\/\/.+\..+/.test(formData.websiteUrl)) {
      newErrors.websiteUrl = 'Please enter a valid website URL (include http:// or https://)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      showMessage('error', 'Please correct the errors before saving');
      return;
    }

    if (window.confirm('Are you sure you want to save these changes?')) {
      try {
        setSaving(true);
        const electronAPI = window.electronAPI;

        const institutionUpdate = {
          name: formData.name
        };

        const detailsUpdate = {
          logoPath: formData.logoPath,
          description: formData.description,
          primaryEmail: formData.primaryEmail,
          contactNumber: formData.contactNumber,
          addressLine1: formData.addressLine1,
          addressLine2: formData.addressLine2,
          city: formData.city,
          state: formData.state,
          postalCode: formData.postalCode,
          country: formData.country,
          establishmentDate: formData.establishmentDate,
          accreditationDetails: formData.accreditationDetails,
          websiteUrl: formData.websiteUrl
        };

        await electronAPI.institution.updateDetails(
          user.institution_id,
          institutionUpdate,
          detailsUpdate,
          user.id
        );

        showMessage('success', 'Institution details updated successfully');
        setEditing(false);
        loadInstitutionData();
      } catch (error) {
        showMessage('error', `Failed to save changes: ${error.message}`);
      } finally {
        setSaving(false);
      }
    }
  };

  const handleCancel = () => {
    setFormData({
      name: institutionData.name || '',
      ...institutionData.details
    });
    setErrors({});
    setEditing(false);
  };

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to logout?')) {
      logout();
      navigate('/login');
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading institution data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <button
                onClick={() => navigate(-1)}
                className="btn-outline mr-4 flex items-center space-x-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Go Back</span>
              </button>
              <div className="flex items-center">
                <Building className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Institution Management</h1>
                  <p className="text-sm text-gray-600">Manage your institution's information and settings</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {editing ? (
                <>
                  <button
                    onClick={handleCancel}
                    className="btn-outline"
                    disabled={saving}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="btn-primary flex items-center space-x-2"
                  >
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4" />
                        <span>Save Changes</span>
                      </>
                    )}
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setEditing(true)}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Edit3 className="h-4 w-4" />
                  <span>Edit</span>
                </button>
              )}

              <button
                onClick={handleLogout}
                className="btn-outline text-red-600 border-red-600 hover:bg-red-50 flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Institution Information</h2>
            <p className="text-sm text-gray-600">Update your institution's details and contact information</p>
          </div>

          <div className="p-6 space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-md font-medium text-gray-900 mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Institution Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    disabled={!editing}
                    className={`input ${errors.name ? 'border-red-500' : ''}`}
                    placeholder="Enter institution name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {errors.name}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description || ''}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    disabled={!editing}
                    rows={3}
                    className="input"
                    placeholder="Enter institution description"
                    maxLength={500}
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    {(formData.description || '').length}/500 characters
                  </p>
                </div>
              </div>
            </div>



            {/* Address Information */}
            <div>
              <h3 className="text-md font-medium text-gray-900 mb-4">
                <MapPin className="h-4 w-4 inline mr-1" />
                Address Information
              </h3>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Address Line 1
                  </label>
                  <input
                    type="text"
                    value={formData.addressLine1 || ''}
                    onChange={(e) => handleInputChange('addressLine1', e.target.value)}
                    disabled={!editing}
                    className="input"
                    placeholder="Enter address line 1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Address Line 2
                  </label>
                  <input
                    type="text"
                    value={formData.addressLine2 || ''}
                    onChange={(e) => handleInputChange('addressLine2', e.target.value)}
                    disabled={!editing}
                    className="input"
                    placeholder="Enter address line 2 (optional)"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      City
                    </label>
                    <input
                      type="text"
                      value={formData.city || ''}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                      disabled={!editing}
                      className="input"
                      placeholder="Enter city"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      State/Province
                    </label>
                    <input
                      type="text"
                      value={formData.state || ''}
                      onChange={(e) => handleInputChange('state', e.target.value)}
                      disabled={!editing}
                      className="input"
                      placeholder="Enter state"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Postal Code
                    </label>
                    <input
                      type="text"
                      value={formData.postalCode || ''}
                      onChange={(e) => handleInputChange('postalCode', e.target.value)}
                      disabled={!editing}
                      className="input"
                      placeholder="Enter postal code"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Country
                  </label>
                  <input
                    type="text"
                    value={formData.country || ''}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    disabled={!editing}
                    className="input"
                    placeholder="Enter country"
                  />
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div>
              <h3 className="text-md font-medium text-gray-900 mb-4">Additional Information</h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Establishment Date
                </label>
                <input
                  type="date"
                  value={formData.establishmentDate || ''}
                  onChange={(e) => handleInputChange('establishmentDate', e.target.value)}
                  disabled={!editing}
                  className="input"
                />
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <Award className="h-4 w-4 inline mr-1" />
                  Accreditation Details
                </label>
                <textarea
                  value={formData.accreditationDetails || ''}
                  onChange={(e) => handleInputChange('accreditationDetails', e.target.value)}
                  disabled={!editing}
                  rows={3}
                  className="input"
                  placeholder="Enter accreditation details, certifications, etc."
                  maxLength={1000}
                />
                <p className="mt-1 text-xs text-gray-500">
                  {(formData.accreditationDetails || '').length}/1000 characters
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Institution;
