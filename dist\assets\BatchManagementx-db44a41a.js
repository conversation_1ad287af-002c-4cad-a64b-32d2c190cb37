import{u as ee,r as l,j as e}from"./chunk-81a058b1.js";import{b as se,u as ae}from"./main-272222cd.js";import{e as le}from"./chunk-e7a6d730.js";import{U as x,y as L,R as ie,A as te,C as j,e as re,O as V,l as me,Q as ne,V as y,k as ce,D as oe,W as pe}from"./chunk-0b87a8e8.js";import"./chunk-81a949b4.js";import"./chunk-c39a4323.js";const xe=()=>{const g=ee(),{getAllBatches:M,getBatchDetails:P,getBatchStatistics:S,archiveBatch:k,activateBatch:_}=se(),{user:i}=ae(),[N,F]=l.useState([]),[w,Y]=l.useState([]),[p,$]=l.useState(null),[U,C]=l.useState(!0),[u,B]=l.useState({type:"",text:""}),[t,R]=l.useState(""),[m,q]=l.useState("all"),[n,O]=l.useState("all"),[c,G]=l.useState("all"),[o,D]=l.useState("created_at"),[v,T]=l.useState("desc"),[f,I]=l.useState(!1);l.useEffect(()=>{h()},[]),l.useEffect(()=>{Q()},[N,t,m,n,c,o,v]);const h=async()=>{try{C(!0);const s=i.role==="teacher"?{teacherId:i.id}:{},[a,E]=await Promise.all([M(s),S(i.id)]);F(a),$(E)}catch{r("error","Failed to load batch data")}finally{C(!1)}},Q=()=>{let s=[...N];t&&(s=s.filter(a=>a.name.toLowerCase().includes(t.toLowerCase())||a.teacher_name.toLowerCase().includes(t.toLowerCase())||a.description?.toLowerCase().includes(t.toLowerCase()))),m!=="all"&&(s=s.filter(a=>a.academic_year===m)),n!=="all"&&(s=s.filter(a=>a.course_type===n)),c!=="all"&&(s=s.filter(a=>c==="active"?a.is_active:!a.is_active)),s.sort((a,E)=>{let d=a[o],b=E[o];return o==="created_at"&&(d=new Date(d),b=new Date(b)),v==="asc"?d>b?1:-1:d<b?1:-1}),Y(s)},r=(s,a)=>{B({type:s,text:a}),setTimeout(()=>B({type:"",text:""}),5e3)},W=s=>{g(`/batch-details/${s}`)},z=s=>{g(`/edit-batch/${s}`)},H=async s=>{try{await k(s,i.id,i.role==="teacher"?i.id:null),r("success","Batch archived successfully"),h()}catch{r("error","Failed to archive batch")}},J=async s=>{try{await _(s,i.id,i.role==="teacher"?i.id:null),r("success","Batch activated successfully"),h()}catch{r("error","Failed to activate batch")}},K=async s=>{try{const a=await P(s.id,i.role==="teacher"?i.id:null);le(a.students,`${s.name}_students.csv`),r("success","Student list exported successfully")}catch{r("error","Failed to export student list")}},X=()=>[...new Set(N.map(a=>a.academic_year))].filter(Boolean).sort(),Z=()=>[...new Set(N.map(a=>a.course_type))].filter(Boolean).sort(),A=s=>{o===s?T(v==="asc"?"desc":"asc"):(D(s),T("asc"))};return!i||!["teacher","admin","super_admin"].includes(i.role)?e.jsxDEV("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV(x,{className:"mx-auto h-12 w-12 text-red-500"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:198,columnNumber:11},globalThis),e.jsxDEV("h2",{className:"mt-2 text-lg font-medium text-gray-900",children:"Access Denied"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:199,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"You need teacher or admin privileges to manage batches."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:200,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:197,columnNumber:9},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:196,columnNumber:7},globalThis):e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("div",{children:[e.jsxDEV("h1",{className:"text-2xl font-bold text-gray-900",children:"Batch Management"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:213,columnNumber:11},globalThis),e.jsxDEV("p",{className:"text-gray-600",children:"Manage your student batches and track progress"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:214,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:212,columnNumber:9},globalThis),e.jsxDEV("div",{className:"flex space-x-3",children:[e.jsxDEV("button",{onClick:()=>g("/create-batch"),className:"btn-primary flex items-center space-x-2",children:[e.jsxDEV(L,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:221,columnNumber:13},globalThis),e.jsxDEV("span",{children:"Create New Batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:222,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:217,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:h,className:"btn-outline flex items-center space-x-2",children:[e.jsxDEV(ie,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:228,columnNumber:13},globalThis),e.jsxDEV("span",{children:"Refresh"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:229,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:224,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:216,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:211,columnNumber:7},globalThis),u.text&&e.jsxDEV("div",{className:`rounded-md p-4 ${u.type==="error"?"bg-red-50":"bg-green-50"}`,children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:u.type==="error"?e.jsxDEV(te,{className:"h-5 w-5 text-red-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:240,columnNumber:17},globalThis):e.jsxDEV(j,{className:"h-5 w-5 text-green-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:242,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:238,columnNumber:13},globalThis),e.jsxDEV("div",{className:"ml-3",children:e.jsxDEV("p",{className:`text-sm ${u.type==="error"?"text-red-800":"text-green-800"}`,children:u.text},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:246,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:245,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:237,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:236,columnNumber:9},globalThis),p&&e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsxDEV("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsxDEV("div",{className:"p-5",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:e.jsxDEV(x,{className:"h-8 w-8 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:261,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:260,columnNumber:17},globalThis),e.jsxDEV("div",{className:"ml-5 w-0 flex-1",children:e.jsxDEV("dl",{children:[e.jsxDEV("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Batches"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:265,columnNumber:21},globalThis),e.jsxDEV("dd",{className:"text-lg font-medium text-gray-900",children:p.total_batches},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:266,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:264,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:263,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:259,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:258,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:257,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsxDEV("div",{className:"p-5",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:e.jsxDEV(j,{className:"h-8 w-8 text-green-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:277,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:276,columnNumber:17},globalThis),e.jsxDEV("div",{className:"ml-5 w-0 flex-1",children:e.jsxDEV("dl",{children:[e.jsxDEV("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Active Batches"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:281,columnNumber:21},globalThis),e.jsxDEV("dd",{className:"text-lg font-medium text-gray-900",children:p.active_batches},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:282,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:280,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:279,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:275,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:274,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:273,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsxDEV("div",{className:"p-5",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:e.jsxDEV(re,{className:"h-8 w-8 text-purple-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:293,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:292,columnNumber:17},globalThis),e.jsxDEV("div",{className:"ml-5 w-0 flex-1",children:e.jsxDEV("dl",{children:[e.jsxDEV("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Students"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:297,columnNumber:21},globalThis),e.jsxDEV("dd",{className:"text-lg font-medium text-gray-900",children:p.total_students},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:298,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:296,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:295,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:291,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:290,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:289,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsxDEV("div",{className:"p-5",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:e.jsxDEV(V,{className:"h-8 w-8 text-gray-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:309,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:308,columnNumber:17},globalThis),e.jsxDEV("div",{className:"ml-5 w-0 flex-1",children:e.jsxDEV("dl",{children:[e.jsxDEV("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Archived"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:313,columnNumber:21},globalThis),e.jsxDEV("dd",{className:"text-lg font-medium text-gray-900",children:p.archived_batches},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:314,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:312,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:311,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:307,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:306,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:305,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:256,columnNumber:9},globalThis),e.jsxDEV("div",{className:"bg-white shadow rounded-lg p-6",children:[e.jsxDEV("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[e.jsxDEV("div",{className:"flex-1 max-w-lg",children:e.jsxDEV("div",{className:"relative",children:[e.jsxDEV(me,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:329,columnNumber:15},globalThis),e.jsxDEV("input",{type:"text",placeholder:"Search batches...",className:"pl-10 input",value:t,onChange:s=>R(s.target.value)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:330,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:328,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:327,columnNumber:11},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-3",children:e.jsxDEV("button",{onClick:()=>I(!f),className:`btn-outline flex items-center space-x-2 ${f?"bg-blue-50 border-blue-300":""}`,children:[e.jsxDEV(ne,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:346,columnNumber:15},globalThis),e.jsxDEV("span",{children:"Filters"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:347,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:342,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:341,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:325,columnNumber:9},globalThis),f&&e.jsxDEV("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Academic Year"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:357,columnNumber:17},globalThis),e.jsxDEV("select",{className:"input",value:m,onChange:s=>q(s.target.value),children:[e.jsxDEV("option",{value:"all",children:"All Years"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:365,columnNumber:19},globalThis),X().map(s=>e.jsxDEV("option",{value:s,children:s},s,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:367,columnNumber:21},globalThis))]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:360,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:356,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Course Type"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:373,columnNumber:17},globalThis),e.jsxDEV("select",{className:"input",value:n,onChange:s=>O(s.target.value),children:[e.jsxDEV("option",{value:"all",children:"All Courses"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:381,columnNumber:19},globalThis),Z().map(s=>e.jsxDEV("option",{value:s,children:s},s,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:383,columnNumber:21},globalThis))]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:376,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:372,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:389,columnNumber:17},globalThis),e.jsxDEV("select",{className:"input",value:c,onChange:s=>G(s.target.value),children:[e.jsxDEV("option",{value:"all",children:"All Status"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:397,columnNumber:19},globalThis),e.jsxDEV("option",{value:"active",children:"Active"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:398,columnNumber:19},globalThis),e.jsxDEV("option",{value:"archived",children:"Archived"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:399,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:392,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:388,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort By"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:404,columnNumber:17},globalThis),e.jsxDEV("select",{className:"input",value:o,onChange:s=>D(s.target.value),children:[e.jsxDEV("option",{value:"created_at",children:"Created Date"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:412,columnNumber:19},globalThis),e.jsxDEV("option",{value:"name",children:"Batch Name"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:413,columnNumber:19},globalThis),e.jsxDEV("option",{value:"student_count",children:"Student Count"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:414,columnNumber:19},globalThis),e.jsxDEV("option",{value:"academic_year",children:"Academic Year"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:415,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:407,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:403,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:355,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:354,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:324,columnNumber:7},globalThis),U?e.jsxDEV("div",{className:"text-center py-12",children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:426,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-2 text-sm text-gray-500",children:"Loading batches..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:427,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:425,columnNumber:9},globalThis):w.length>0?e.jsxDEV("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:e.jsxDEV("div",{className:"overflow-x-auto",children:e.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxDEV("thead",{className:"bg-gray-50",children:e.jsxDEV("tr",{children:[e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>A("name"),children:e.jsxDEV("div",{className:"flex items-center space-x-1",children:[e.jsxDEV("span",{children:"Batch Name"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:440,columnNumber:23},globalThis),e.jsxDEV(y,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:441,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:439,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:435,columnNumber:19},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Academic Year"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:444,columnNumber:19},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Course Type"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:447,columnNumber:19},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>A("student_count"),children:e.jsxDEV("div",{className:"flex items-center space-x-1",children:[e.jsxDEV("span",{children:"Students"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:455,columnNumber:23},globalThis),e.jsxDEV(y,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:456,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:454,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:450,columnNumber:19},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>A("created_at"),children:e.jsxDEV("div",{className:"flex items-center space-x-1",children:[e.jsxDEV("span",{children:"Created"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:464,columnNumber:23},globalThis),e.jsxDEV(y,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:465,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:463,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:459,columnNumber:19},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:468,columnNumber:19},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:471,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:434,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:433,columnNumber:15},globalThis),e.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:w.map(s=>e.jsxDEV("tr",{className:"hover:bg-gray-50",children:[e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxDEV("div",{children:[e.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:s.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:481,columnNumber:25},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:s.description||"No description"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:482,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:480,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:479,columnNumber:21},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:s.academic_year},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:485,columnNumber:21},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:s.course_type},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:488,columnNumber:21},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(x,{className:"h-4 w-4 text-gray-400 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:493,columnNumber:25},globalThis),s.student_count]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:492,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:491,columnNumber:21},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(s.created_at).toLocaleDateString()},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:497,columnNumber:21},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxDEV("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${s.is_active?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:s.is_active?"Active":"Archived"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:501,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:500,columnNumber:21},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxDEV("div",{className:"flex items-center space-x-2",children:[e.jsxDEV("button",{onClick:()=>W(s.id),className:"text-blue-600 hover:text-blue-900",title:"View Details",children:e.jsxDEV(ce,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:516,columnNumber:27},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:511,columnNumber:25},globalThis),e.jsxDEV("button",{onClick:()=>z(s.id),className:"text-green-600 hover:text-green-900",title:"Edit Batch",children:e.jsxDEV(oe,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:523,columnNumber:27},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:518,columnNumber:25},globalThis),e.jsxDEV("button",{onClick:()=>K(s),className:"text-purple-600 hover:text-purple-900",title:"Export Students",children:e.jsxDEV(pe,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:530,columnNumber:27},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:525,columnNumber:25},globalThis),s.is_active?e.jsxDEV("button",{onClick:()=>H(s.id),className:"text-gray-600 hover:text-gray-900",title:"Archive Batch",children:e.jsxDEV(V,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:538,columnNumber:29},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:533,columnNumber:27},globalThis):e.jsxDEV("button",{onClick:()=>J(s.id),className:"text-green-600 hover:text-green-900",title:"Activate Batch",children:e.jsxDEV(j,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:546,columnNumber:29},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:541,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:510,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:509,columnNumber:21},globalThis)]},s.id,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:478,columnNumber:19},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:476,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:432,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:431,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:430,columnNumber:9},globalThis):e.jsxDEV("div",{className:"text-center py-12 bg-white shadow rounded-lg",children:[e.jsxDEV(x,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:559,columnNumber:11},globalThis),e.jsxDEV("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No batches found"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:560,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:t||m!=="all"||n!=="all"||c!=="all"?"Try adjusting your search or filters.":"Get started by creating your first batch."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:561,columnNumber:11},globalThis),!t&&m==="all"&&n==="all"&&c==="all"&&e.jsxDEV("div",{className:"mt-6",children:e.jsxDEV("button",{onClick:()=>g("/create-batch"),className:"btn-primary flex items-center space-x-2 mx-auto",children:[e.jsxDEV(L,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:573,columnNumber:17},globalThis),e.jsxDEV("span",{children:"Create First Batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:574,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:569,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:568,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:558,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",lineNumber:209,columnNumber:5},globalThis)};export{xe as default};
