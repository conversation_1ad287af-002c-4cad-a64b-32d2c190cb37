import { r as reactExports, j as jsxDevRuntimeExports } from './chunk-2ef8e52b.js';
import { b as useDatabase, u as useAuth } from './main-ef45ddde.js';
import { s as sanitizeUserData, v as validateUserForm, h as hasValidationErrors, a as validatePassword, b as validateLogoFile, c as validateInstitutionForm } from './chunk-8b627984.js';
import { X, o as EyeOff, k as Eye, R as RefreshCw, a as AlertTriangle, F as FileText, p as User, q as Clock, r as Building2, C as CheckCircle, A as AlertCircle, s as Image, t as Upload, T as Trash2, u as Mail, P as Phone, v as MapPin, i as Globe, w as Calendar, x as Save, c as Shield, y as Plus, l as Search, U as Users, z as UserCheck, D as PenSquare, J as Key, N as ShieldOff } from './chunk-0deb1b3d.js';
import './chunk-03d61bd9.js';

const UserForm = ({
  user = null,
  onSubmit,
  onCancel,
  loading = false,
  generatePassword,
  isEdit = false
}) => {
  const [formData, setFormData] = reactExports.useState({
    firstName: "",
    lastName: "",
    username: "",
    password: "",
    confirmPassword: "",
    teacherId: "",
    role: "student"
  });
  const [errors, setErrors] = reactExports.useState({});
  const [showPassword, setShowPassword] = reactExports.useState(false);
  const [passwordGenerated, setPasswordGenerated] = reactExports.useState(false);
  reactExports.useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.first_name || "",
        lastName: user.last_name || "",
        username: user.username || "",
        password: "",
        confirmPassword: "",
        teacherId: user.teacher_id || "",
        role: user.role || "student"
      });
    }
  }, [user]);
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };
  const handleGeneratePassword = async () => {
    try {
      const generatedPassword = await generatePassword();
      setFormData((prev) => ({
        ...prev,
        password: generatedPassword,
        confirmPassword: generatedPassword
      }));
      setPasswordGenerated(true);
      setShowPassword(true);
    } catch (error) {
      console.error("Failed to generate password:", error);
    }
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const sanitizedData = sanitizeUserData(formData);
    const validationErrors = validateUserForm(sanitizedData, sanitizedData.role === "teacher");
    if (!isEdit && sanitizedData.password !== sanitizedData.confirmPassword) {
      validationErrors.confirmPassword = ["Passwords do not match"];
    }
    if (!isEdit && !sanitizedData.password) {
      validationErrors.password = ["Password is required for new users"];
    }
    if (hasValidationErrors(validationErrors)) {
      setErrors(validationErrors);
      return;
    }
    const submitData = {
      ...sanitizedData,
      // Remove confirmPassword from submission
      confirmPassword: void 0
    };
    onSubmit(submitData);
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white p-6 rounded-lg shadow-lg max-w-2xl mx-auto", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-2xl font-bold mb-6 text-gray-800", children: isEdit ? "Edit User" : "Create New User" }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
      lineNumber: 108,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("form", { onSubmit: handleSubmit, className: "space-y-4", "data-testid": isEdit ? "edit-user-form" : "create-user-form", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "First Name *" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 116,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              type: "text",
              name: "firstName",
              value: formData.firstName,
              onChange: handleInputChange,
              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.firstName ? "border-red-500" : "border-gray-300"}`,
              required: true
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
              lineNumber: 119,
              columnNumber: 13
            },
            globalThis
          ),
          errors.firstName && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.firstName[0] }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 130,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
          lineNumber: 115,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Last Name *" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 135,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              type: "text",
              name: "lastName",
              value: formData.lastName,
              onChange: handleInputChange,
              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.lastName ? "border-red-500" : "border-gray-300"}`,
              required: true
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
              lineNumber: 138,
              columnNumber: 13
            },
            globalThis
          ),
          errors.lastName && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.lastName[0] }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 149,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
          lineNumber: 134,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
        lineNumber: 114,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Username *" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
          lineNumber: 156,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            type: "text",
            name: "username",
            value: formData.username,
            onChange: handleInputChange,
            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.username ? "border-red-500" : "border-gray-300"}`,
            required: true,
            disabled: isEdit
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 159,
            columnNumber: 11
          },
          globalThis
        ),
        errors.username && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.username[0] }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
          lineNumber: 171,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
        lineNumber: 155,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Role *" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 178,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              name: "role",
              value: formData.role,
              onChange: handleInputChange,
              className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
              required: true,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "student", children: "Student" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
                  lineNumber: 188,
                  columnNumber: 15
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "teacher", children: "Teacher" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
                  lineNumber: 189,
                  columnNumber: 15
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "admin", children: "Admin" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
                  lineNumber: 190,
                  columnNumber: 15
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "super_admin", children: "Super Admin" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
                  lineNumber: 191,
                  columnNumber: 15
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
              lineNumber: 181,
              columnNumber: 13
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
          lineNumber: 177,
          columnNumber: 11
        }, globalThis),
        formData.role === "teacher" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Teacher ID *" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 197,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              type: "text",
              name: "teacherId",
              value: formData.teacherId,
              onChange: handleInputChange,
              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.teacherId ? "border-red-500" : "border-gray-300"}`,
              required: true,
              placeholder: "e.g., TCH001"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
              lineNumber: 200,
              columnNumber: 15
            },
            globalThis
          ),
          errors.teacherId && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.teacherId[0] }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 212,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
          lineNumber: 196,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
        lineNumber: 176,
        columnNumber: 9
      }, globalThis),
      !isEdit && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Password *" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 224,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: showPassword ? "text" : "password",
                name: "password",
                value: formData.password,
                onChange: handleInputChange,
                className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 pr-20 ${errors.password ? "border-red-500" : "border-gray-300"}`,
                required: true
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
                lineNumber: 228,
                columnNumber: 17
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                type: "button",
                onClick: () => setShowPassword(!showPassword),
                className: "absolute right-2 top-2 text-sm text-blue-600 hover:text-blue-800",
                children: showPassword ? "Hide" : "Show"
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
                lineNumber: 238,
                columnNumber: 17
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 227,
            columnNumber: 15
          }, globalThis),
          errors.password && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.password[0] }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 247,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              type: "button",
              onClick: handleGeneratePassword,
              className: "mt-2 text-sm text-blue-600 hover:text-blue-800",
              children: "Generate Secure Password"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
              lineNumber: 249,
              columnNumber: 15
            },
            globalThis
          ),
          passwordGenerated && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-green-600 text-sm mt-1", children: "Secure password generated! Make sure to save it." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 257,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
          lineNumber: 223,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Confirm Password *" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 264,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              type: showPassword ? "text" : "password",
              name: "confirmPassword",
              value: formData.confirmPassword,
              onChange: handleInputChange,
              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.confirmPassword ? "border-red-500" : "border-gray-300"}`,
              required: true
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
              lineNumber: 267,
              columnNumber: 15
            },
            globalThis
          ),
          errors.confirmPassword && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.confirmPassword[0] }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 278,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
          lineNumber: 263,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
        lineNumber: 222,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end space-x-4 pt-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            type: "button",
            onClick: onCancel,
            className: "px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500",
            disabled: loading,
            children: "Cancel"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 286,
            columnNumber: 11
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            type: "submit",
            className: "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50",
            disabled: loading,
            children: loading ? "Saving..." : isEdit ? "Update User" : "Create User"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
            lineNumber: 294,
            columnNumber: 11
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
        lineNumber: 285,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
      lineNumber: 112,
      columnNumber: 7
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/UserForm.jsx",
    lineNumber: 107,
    columnNumber: 5
  }, globalThis);
};

const PasswordResetModal = ({
  user,
  isOpen,
  onClose,
  onSubmit,
  onGeneratePassword,
  loading = false
}) => {
  const [password, setPassword] = reactExports.useState("");
  const [confirmPassword, setConfirmPassword] = reactExports.useState("");
  const [showPassword, setShowPassword] = reactExports.useState(false);
  const [errors, setErrors] = reactExports.useState([]);
  const [isGenerated, setIsGenerated] = reactExports.useState(false);
  if (!isOpen || !user) {
    return null;
  }
  const handleGeneratePassword = async () => {
    try {
      const generatedPassword = await onGeneratePassword();
      if (generatedPassword) {
        setPassword(generatedPassword);
        setConfirmPassword(generatedPassword);
        setShowPassword(true);
        setIsGenerated(true);
        setErrors([]);
      }
    } catch {
      setErrors(["Failed to generate password"]);
    }
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const passwordErrors = validatePassword(password);
    if (password !== confirmPassword) {
      passwordErrors.push("Passwords do not match");
    }
    if (passwordErrors.length > 0) {
      setErrors(passwordErrors);
      return;
    }
    setErrors([]);
    onSubmit(password);
  };
  const handleClose = () => {
    setPassword("");
    setConfirmPassword("");
    setShowPassword(false);
    setErrors([]);
    setIsGenerated(false);
    onClose();
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mb-4", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900", children: "Reset Password" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
        lineNumber: 68,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: handleClose,
          className: "text-gray-400 hover:text-gray-600",
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-5 w-5" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
            lineNumber: 75,
            columnNumber: 13
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
          lineNumber: 71,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
      lineNumber: 67,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4 p-3 bg-blue-50 rounded-md", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-blue-800", children: [
        "Resetting password for: ",
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: [
          user.first_name,
          " ",
          user.last_name
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
          lineNumber: 81,
          columnNumber: 37
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
        lineNumber: 80,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-blue-600 mt-1", children: "The user will be required to change their password on next login." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
        lineNumber: 83,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
      lineNumber: 79,
      columnNumber: 9
    }, globalThis),
    errors.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4 p-3 bg-red-50 rounded-md", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-red-800 space-y-1", children: errors.map((error, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
      "• ",
      error
    ] }, index, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
      lineNumber: 92,
      columnNumber: 17
    }, globalThis)) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
      lineNumber: 90,
      columnNumber: 13
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
      lineNumber: 89,
      columnNumber: 11
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("form", { onSubmit: handleSubmit, className: "space-y-4", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "New Password *" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
          lineNumber: 100,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              type: showPassword ? "text" : "password",
              value: password,
              onChange: (e) => {
                setPassword(e.target.value);
                setIsGenerated(false);
              },
              className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 pr-20",
              required: true,
              placeholder: "Enter new password"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
              lineNumber: 104,
              columnNumber: 15
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              type: "button",
              onClick: () => setShowPassword(!showPassword),
              className: "absolute right-10 top-2 text-sm text-blue-600 hover:text-blue-800",
              children: showPassword ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(EyeOff, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
                lineNumber: 120,
                columnNumber: 33
              }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Eye, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
                lineNumber: 120,
                columnNumber: 66
              }, globalThis)
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
              lineNumber: 115,
              columnNumber: 15
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
          lineNumber: 103,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            type: "button",
            onClick: handleGeneratePassword,
            className: "mt-2 flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800",
            disabled: loading,
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RefreshCw, { className: "h-3 w-3" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
                lineNumber: 129,
                columnNumber: 15
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Generate Secure Password" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
                lineNumber: 130,
                columnNumber: 15
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
            lineNumber: 123,
            columnNumber: 13
          },
          globalThis
        ),
        isGenerated && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-green-600 text-sm mt-1", children: "✓ Secure password generated! Make sure to save it." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
          lineNumber: 133,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
        lineNumber: 99,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Confirm Password *" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
          lineNumber: 140,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            type: showPassword ? "text" : "password",
            value: confirmPassword,
            onChange: (e) => setConfirmPassword(e.target.value),
            className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
            required: true,
            placeholder: "Confirm new password"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
            lineNumber: 143,
            columnNumber: 13
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
        lineNumber: 139,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-yellow-50 p-3 rounded-md", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-yellow-800", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Password Requirements:" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
          lineNumber: 155,
          columnNumber: 15
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
          lineNumber: 154,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-xs text-yellow-700 mt-1 space-y-1", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• At least 8 characters long" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
            lineNumber: 158,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Contains uppercase and lowercase letters" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
            lineNumber: 159,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Contains at least one number" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
            lineNumber: 160,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• May contain special characters (@$!%*?&)" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
            lineNumber: 161,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
          lineNumber: 157,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
        lineNumber: 153,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end space-x-3 pt-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            type: "button",
            onClick: handleClose,
            className: "px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500",
            disabled: loading,
            children: "Cancel"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
            lineNumber: 166,
            columnNumber: 13
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            type: "submit",
            className: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50",
            disabled: loading,
            children: loading ? "Resetting..." : "Reset Password"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
            lineNumber: 174,
            columnNumber: 13
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
        lineNumber: 165,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
      lineNumber: 98,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
    lineNumber: 66,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/PasswordResetModal.jsx",
    lineNumber: 65,
    columnNumber: 5
  }, globalThis);
};

const ConfirmModal = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  type = "warning",
  // 'warning', 'danger', 'info'
  loading = false
}) => {
  if (!isOpen) {
    return null;
  }
  const getTypeStyles = () => {
    switch (type) {
      case "danger":
        return {
          icon: "text-red-600",
          bg: "bg-red-50",
          button: "bg-red-600 hover:bg-red-700 focus:ring-red-500"
        };
      case "warning":
        return {
          icon: "text-yellow-600",
          bg: "bg-yellow-50",
          button: "bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500"
        };
      case "info":
        return {
          icon: "text-blue-600",
          bg: "bg-blue-50",
          button: "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500"
        };
      default:
        return {
          icon: "text-yellow-600",
          bg: "bg-yellow-50",
          button: "bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500"
        };
    }
  };
  const styles = getTypeStyles();
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mb-4", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900", children: title }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
        lineNumber: 52,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: onClose,
          className: "text-gray-400 hover:text-gray-600",
          disabled: loading,
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-5 w-5" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
            lineNumber: 60,
            columnNumber: 13
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
          lineNumber: 55,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
      lineNumber: 51,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `p-4 rounded-md ${styles.bg} mb-4`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertTriangle, { className: `h-5 w-5 ${styles.icon}` }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
        lineNumber: 67,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
        lineNumber: 66,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-800", children: message }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
        lineNumber: 70,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
        lineNumber: 69,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
      lineNumber: 65,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
      lineNumber: 64,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end space-x-3", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          type: "button",
          onClick: onClose,
          className: "px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500",
          disabled: loading,
          children: cancelText
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
          lineNumber: 78,
          columnNumber: 11
        },
        globalThis
      ),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          type: "button",
          onClick: onConfirm,
          className: `px-4 py-2 text-white rounded-md focus:outline-none focus:ring-2 disabled:opacity-50 ${styles.button}`,
          disabled: loading,
          children: loading ? "Processing..." : confirmText
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
          lineNumber: 86,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
      lineNumber: 77,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
    lineNumber: 50,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/ConfirmModal.jsx",
    lineNumber: 49,
    columnNumber: 5
  }, globalThis);
};

const AuditLogModal = ({
  isOpen,
  onClose,
  auditLog = [],
  loading = false
}) => {
  if (!isOpen) {
    return null;
  }
  const formatAction = (action) => {
    return action.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase());
  };
  const getActionColor = (action) => {
    const colors = {
      "CREATE_USER": "bg-green-100 text-green-800",
      "UPDATE_USER": "bg-blue-100 text-blue-800",
      "RESET_PASSWORD": "bg-yellow-100 text-yellow-800",
      "DEACTIVATE_USER": "bg-red-100 text-red-800",
      "ACTIVATE_USER": "bg-green-100 text-green-800",
      "DELETE_USER": "bg-red-100 text-red-800"
    };
    return colors[action] || "bg-gray-100 text-gray-800";
  };
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };
  const formatChanges = (oldValues, newValues) => {
    if (!oldValues && !newValues) {
      return "No details available";
    }
    const changes = [];
    if (newValues && typeof newValues === "object") {
      Object.keys(newValues).forEach((key) => {
        if (key !== "password" && key !== "passwordReset") {
          const oldValue = oldValues?.[key] || "N/A";
          const newValue = newValues[key];
          if (oldValue !== newValue) {
            changes.push(`${key}: ${oldValue} → ${newValue}`);
          }
        }
      });
    }
    if (newValues?.passwordReset) {
      changes.push("Password was reset");
    }
    return changes.length > 0 ? changes.join(", ") : "No changes recorded";
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mb-4", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "h-5 w-5 mr-2" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
          lineNumber: 61,
          columnNumber: 13
        }, globalThis),
        "Audit Log"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 60,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: onClose,
          className: "text-gray-400 hover:text-gray-600",
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-5 w-5" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
            lineNumber: 68,
            columnNumber: 13
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
          lineNumber: 64,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 59,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Recent administrative actions performed in the system (last 50 entries)" }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 73,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 72,
      columnNumber: 9
    }, globalThis),
    loading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 80,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-gray-500", children: "Loading audit log..." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 81,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 79,
      columnNumber: 11
    }, globalThis) : auditLog.length > 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-h-96 overflow-y-auto", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-3", children: auditLog.map((entry, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "border border-gray-200 rounded-lg p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-start justify-between", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2 mb-2", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActionColor(entry.action)}`, children: formatAction(entry.action) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
          lineNumber: 91,
          columnNumber: 25
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm text-gray-500", children: [
          "on ",
          entry.table_name
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
          lineNumber: 94,
          columnNumber: 25
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 90,
        columnNumber: 23
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-4 text-sm text-gray-600 mb-2", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-1", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(User, { className: "h-4 w-4" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
            lineNumber: 101,
            columnNumber: 27
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: entry.user_name || "Unknown User" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
            lineNumber: 102,
            columnNumber: 27
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
          lineNumber: 100,
          columnNumber: 25
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-1", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Clock, { className: "h-4 w-4" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
            lineNumber: 105,
            columnNumber: 27
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: formatTimestamp(entry.timestamp) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
            lineNumber: 106,
            columnNumber: 27
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
          lineNumber: 104,
          columnNumber: 25
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 99,
        columnNumber: 23
      }, globalThis),
      (entry.old_values || entry.new_values) && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-700", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Changes:" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
          lineNumber: 112,
          columnNumber: 27
        }, globalThis),
        " ",
        formatChanges(
          entry.old_values ? JSON.parse(entry.old_values) : null,
          entry.new_values ? JSON.parse(entry.new_values) : null
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 111,
        columnNumber: 25
      }, globalThis),
      entry.record_id && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-xs text-gray-500 mt-1", children: [
        "Record ID: ",
        entry.record_id
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 120,
        columnNumber: 25
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 89,
      columnNumber: 21
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 88,
      columnNumber: 19
    }, globalThis) }, entry.id || index, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 87,
      columnNumber: 17
    }, globalThis)) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 85,
      columnNumber: 13
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 84,
      columnNumber: 11
    }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 132,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No audit entries" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 133,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "No administrative actions have been recorded yet." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 134,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 131,
      columnNumber: 11
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end pt-4 border-t border-gray-200 mt-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      "button",
      {
        onClick: onClose,
        className: "px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500",
        children: "Close"
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
        lineNumber: 141,
        columnNumber: 11
      },
      globalThis
    ) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
      lineNumber: 140,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
    lineNumber: 58,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/AuditLogModal.jsx",
    lineNumber: 57,
    columnNumber: 5
  }, globalThis);
};

const InstituteSettings = () => {
  const {
    getInstitution,
    updateInstitution,
    uploadInstitutionLogo,
    removeInstitutionLogo,
    getInstitutionLogoPath
  } = useDatabase();
  const { user } = useAuth();
  const [institutionData, setInstitutionData] = reactExports.useState({
    name: "",
    description: "",
    email: "",
    phone: "",
    address: "",
    websiteUrl: "",
    establishedYear: "",
    instituteType: "",
    logoPath: ""
  });
  const [errors, setErrors] = reactExports.useState({});
  const [loading, setLoading] = reactExports.useState(false);
  const [message, setMessage] = reactExports.useState({ type: "", text: "" });
  const [logoPreview, setLogoPreview] = reactExports.useState(null);
  const [logoFile, setLogoFile] = reactExports.useState(null);
  const [showRemoveConfirm, setShowRemoveConfirm] = reactExports.useState(false);
  const fileInputRef = reactExports.useRef(null);
  reactExports.useEffect(() => {
    loadInstitutionData();
  }, []);
  const loadInstitutionData = async () => {
    try {
      setLoading(true);
      const institution = await getInstitution();
      if (institution) {
        setInstitutionData({
          name: institution.name || "",
          description: institution.description || "",
          email: institution.email || "",
          phone: institution.phone || "",
          address: institution.address || "",
          websiteUrl: institution.website_url || "",
          establishedYear: institution.established_year || "",
          instituteType: institution.institute_type || "",
          logoPath: institution.logo_path || ""
        });
        if (institution.logo_path) {
          loadLogoPreview(institution.logo_path);
        }
      }
    } catch {
      showMessage("error", "Failed to load institution data");
    } finally {
      setLoading(false);
    }
  };
  const loadLogoPreview = async (logoFileName) => {
    try {
      const logoPath = await getInstitutionLogoPath(logoFileName);
      if (logoPath) {
        setLogoPreview(`file://${logoPath}`);
      }
    } catch (error) {
      console.error("Failed to load logo preview:", error);
    }
  };
  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: "", text: "" }), 5e3);
  };
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setInstitutionData((prev) => ({
      ...prev,
      [name]: value
    }));
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };
  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (!file) {
      return;
    }
    const fileErrors = validateLogoFile(file);
    if (fileErrors.length > 0) {
      showMessage("error", fileErrors[0]);
      return;
    }
    setLogoFile(file);
    const reader = new FileReader();
    reader.onload = (e2) => {
      setLogoPreview(e2.target.result);
    };
    reader.readAsDataURL(file);
  };
  const handleLogoUpload = async () => {
    if (!logoFile) {
      return;
    }
    try {
      setLoading(true);
      const arrayBuffer = await logoFile.arrayBuffer();
      const buffer = new Uint8Array(arrayBuffer);
      const logoData = {
        fileName: logoFile.name,
        fileData: buffer,
        mimeType: logoFile.type
      };
      const result = await uploadInstitutionLogo(logoData, user.id);
      setInstitutionData((prev) => ({
        ...prev,
        logoPath: result.logoPath
      }));
      setLogoFile(null);
      showMessage("success", "Logo uploaded successfully");
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setLoading(false);
    }
  };
  const handleLogoRemove = async () => {
    try {
      setLoading(true);
      await removeInstitutionLogo(user.id);
      setInstitutionData((prev) => ({
        ...prev,
        logoPath: ""
      }));
      setLogoPreview(null);
      setLogoFile(null);
      setShowRemoveConfirm(false);
      showMessage("success", "Logo removed successfully");
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setLoading(false);
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    const sanitizedData = sanitizeUserData(institutionData);
    const validationErrors = validateInstitutionForm(sanitizedData);
    if (hasValidationErrors(validationErrors)) {
      setErrors(validationErrors);
      showMessage("error", "Please fix the validation errors");
      return;
    }
    try {
      setLoading(true);
      if (logoFile) {
        await handleLogoUpload();
      }
      await updateInstitution(sanitizedData, user.id);
      showMessage("success", "Institution information updated successfully");
      setErrors({});
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setLoading(false);
    }
  };
  const instituteTypes = [
    "University",
    "College",
    "School",
    "Training Center",
    "Other"
  ];
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Building2, { className: "h-6 w-6 text-blue-600" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 244,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-xl font-semibold text-gray-900", children: "Institute Settings" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
          lineNumber: 246,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Manage your institution's information and branding" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
          lineNumber: 247,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 245,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
      lineNumber: 243,
      columnNumber: 7
    }, globalThis),
    message.text && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `p-4 rounded-md flex items-center space-x-2 ${message.type === "success" ? "bg-green-50 text-green-800 border border-green-200" : "bg-red-50 text-red-800 border border-red-200"}`, children: [
      message.type === "success" ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 259,
        columnNumber: 13
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 261,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: message.text }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 263,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
      lineNumber: 253,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 lg:grid-cols-3 gap-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "lg:col-span-1", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 mb-4 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Image, { className: "h-5 w-5 mr-2" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 272,
            columnNumber: 15
          }, globalThis),
          "Institution Logo"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
          lineNumber: 271,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50", children: logoPreview ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "img",
            {
              src: logoPreview,
              alt: "Institution Logo",
              className: "w-full h-full object-contain rounded-lg"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 281,
              columnNumber: 21
            },
            globalThis
          ) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Image, { className: "mx-auto h-8 w-8 text-gray-400" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 288,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-xs text-gray-500", children: "No logo" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 289,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 287,
            columnNumber: 21
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 279,
            columnNumber: 17
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 278,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              ref: fileInputRef,
              type: "file",
              accept: "image/png,image/jpeg,image/jpg,image/svg+xml",
              onChange: handleFileSelect,
              className: "hidden"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 296,
              columnNumber: 15
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              type: "button",
              onClick: () => fileInputRef.current?.click(),
              className: "w-full btn-outline flex items-center justify-center space-x-2",
              disabled: loading,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Upload, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 311,
                  columnNumber: 17
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Choose Logo" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 312,
                  columnNumber: 17
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 305,
              columnNumber: 15
            },
            globalThis
          ),
          logoFile && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              type: "button",
              onClick: handleLogoUpload,
              className: "w-full btn-primary",
              disabled: loading,
              children: "Upload Logo"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 317,
              columnNumber: 17
            },
            globalThis
          ),
          logoPreview && !logoFile && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              type: "button",
              onClick: () => setShowRemoveConfirm(true),
              className: "w-full btn-outline text-red-600 border-red-300 hover:bg-red-50 flex items-center justify-center space-x-2",
              disabled: loading,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Trash2, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 334,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Remove Logo" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 335,
                  columnNumber: 19
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 328,
              columnNumber: 17
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-xs text-gray-500", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Supported formats: PNG, JPG, JPEG, SVG" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 340,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Maximum size: 2MB" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 341,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Recommended: Square aspect ratio" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 342,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 339,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
          lineNumber: 276,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 270,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 269,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "lg:col-span-2", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 mb-6", children: "Institution Information" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
          lineNumber: 351,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("form", { onSubmit: handleSubmit, className: "space-y-6", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Institution Name *" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 357,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "text",
                  name: "name",
                  value: institutionData.name,
                  onChange: handleInputChange,
                  className: `input ${errors.name ? "border-red-500" : ""}`,
                  placeholder: "Enter institution name",
                  required: true
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 360,
                  columnNumber: 19
                },
                globalThis
              ),
              errors.name && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.name[0] }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 370,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 356,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Institute Type" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 375,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "select",
                {
                  name: "instituteType",
                  value: institutionData.instituteType,
                  onChange: handleInputChange,
                  className: `input ${errors.instituteType ? "border-red-500" : ""}`,
                  children: [
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "", children: "Select type" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                      lineNumber: 384,
                      columnNumber: 21
                    }, globalThis),
                    instituteTypes.map((type) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: type, children: type }, type, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                      lineNumber: 386,
                      columnNumber: 23
                    }, globalThis))
                  ]
                },
                void 0,
                true,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 378,
                  columnNumber: 19
                },
                globalThis
              ),
              errors.instituteType && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.instituteType[0] }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 390,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 374,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 355,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Description" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 397,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "textarea",
              {
                name: "description",
                value: institutionData.description,
                onChange: handleInputChange,
                rows: 3,
                className: `input ${errors.description ? "border-red-500" : ""}`,
                placeholder: "Brief description of your institution (optional)",
                maxLength: 500
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 400,
                columnNumber: 17
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mt-1", children: [
              errors.description && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm", children: errors.description[0] }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 411,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-500 ml-auto", children: [
                institutionData.description.length,
                "/500 characters"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 413,
                columnNumber: 19
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 409,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 396,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1 flex items-center", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Mail, { className: "h-4 w-4 mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 423,
                  columnNumber: 21
                }, globalThis),
                "Official Email *"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 422,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "email",
                  name: "email",
                  value: institutionData.email,
                  onChange: handleInputChange,
                  className: `input ${errors.email ? "border-red-500" : ""}`,
                  placeholder: "<EMAIL>",
                  required: true
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 426,
                  columnNumber: 19
                },
                globalThis
              ),
              errors.email && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.email[0] }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 436,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 421,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1 flex items-center", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Phone, { className: "h-4 w-4 mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 442,
                  columnNumber: 21
                }, globalThis),
                "Primary Contact Phone *"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 441,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "tel",
                  name: "phone",
                  value: institutionData.phone,
                  onChange: handleInputChange,
                  className: `input ${errors.phone ? "border-red-500" : ""}`,
                  placeholder: "+1234567890",
                  required: true
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 445,
                  columnNumber: 19
                },
                globalThis
              ),
              errors.phone && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.phone[0] }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 455,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 440,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 420,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1 flex items-center", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(MapPin, { className: "h-4 w-4 mr-1" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 463,
                columnNumber: 19
              }, globalThis),
              "Physical Address *"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 462,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "textarea",
              {
                name: "address",
                value: institutionData.address,
                onChange: handleInputChange,
                rows: 3,
                className: `input ${errors.address ? "border-red-500" : ""}`,
                placeholder: "Enter complete address including city, state, and postal code",
                required: true
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 466,
                columnNumber: 17
              },
              globalThis
            ),
            errors.address && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.address[0] }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 476,
              columnNumber: 19
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 461,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1 flex items-center", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Globe, { className: "h-4 w-4 mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 484,
                  columnNumber: 21
                }, globalThis),
                "Website URL"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 483,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "url",
                  name: "websiteUrl",
                  value: institutionData.websiteUrl,
                  onChange: handleInputChange,
                  className: `input ${errors.websiteUrl ? "border-red-500" : ""}`,
                  placeholder: "https://www.institution.edu"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 487,
                  columnNumber: 19
                },
                globalThis
              ),
              errors.websiteUrl && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.websiteUrl[0] }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 496,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 482,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1 flex items-center", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Calendar, { className: "h-4 w-4 mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 502,
                  columnNumber: 21
                }, globalThis),
                "Established Year"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 501,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "number",
                  name: "establishedYear",
                  value: institutionData.establishedYear,
                  onChange: handleInputChange,
                  className: `input ${errors.establishedYear ? "border-red-500" : ""}`,
                  placeholder: "1990",
                  min: "1800",
                  max: (/* @__PURE__ */ new Date()).getFullYear()
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 505,
                  columnNumber: 19
                },
                globalThis
              ),
              errors.establishedYear && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.establishedYear[0] }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                lineNumber: 516,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 500,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 481,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end pt-6 border-t border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              type: "submit",
              className: "btn-primary flex items-center space-x-2",
              disabled: loading,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Save, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 528,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: loading ? "Saving..." : "Save Changes" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
                  lineNumber: 529,
                  columnNumber: 19
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 523,
              columnNumber: 17
            },
            globalThis
          ) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 522,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
          lineNumber: 353,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 350,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 349,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
      lineNumber: 267,
      columnNumber: 7
    }, globalThis),
    showRemoveConfirm && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mb-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900", children: "Remove Logo" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
          lineNumber: 542,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setShowRemoveConfirm(false),
            className: "text-gray-400 hover:text-gray-600",
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-5 w-5" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
              lineNumber: 547,
              columnNumber: 17
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 543,
            columnNumber: 15
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 541,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Are you sure you want to remove the institution logo? This action cannot be undone." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 552,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 551,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setShowRemoveConfirm(false),
            className: "px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",
            disabled: loading,
            children: "Cancel"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 558,
            columnNumber: 15
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: handleLogoRemove,
            className: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50",
            disabled: loading,
            children: loading ? "Removing..." : "Remove Logo"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
            lineNumber: 565,
            columnNumber: 15
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
        lineNumber: 557,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
      lineNumber: 540,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
      lineNumber: 539,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/admin/InstituteSettings.jsx",
    lineNumber: 241,
    columnNumber: 5
  }, globalThis);
};

const AdminPanel = () => {
  const {
    getAllUsers,
    createUser,
    updateUser,
    resetUserPassword,
    deactivateUser,
    activateUser,
    generateSecurePassword,
    getAuditLog
  } = useDatabase();
  const { user, isSuperAdmin } = useAuth();
  const [activeTab, setActiveTab] = reactExports.useState("users");
  const [users, setUsers] = reactExports.useState([]);
  const [filteredUsers, setFilteredUsers] = reactExports.useState([]);
  const [loading, setLoading] = reactExports.useState(true);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [selectedRole, setSelectedRole] = reactExports.useState("all");
  const [selectedStatus, setSelectedStatus] = reactExports.useState("all");
  const [message, setMessage] = reactExports.useState({ type: "", text: "" });
  const [showCreateModal, setShowCreateModal] = reactExports.useState(false);
  const [showEditModal, setShowEditModal] = reactExports.useState(false);
  const [showPasswordModal, setShowPasswordModal] = reactExports.useState(false);
  const [showAuditModal, setShowAuditModal] = reactExports.useState(false);
  const [showConfirmModal, setShowConfirmModal] = reactExports.useState(false);
  const [selectedUser, setSelectedUser] = reactExports.useState(null);
  const [confirmAction, setConfirmAction] = reactExports.useState(null);
  const [newPassword, setNewPassword] = reactExports.useState("");
  const [auditLog, setAuditLog] = reactExports.useState([]);
  const [auditLoading, setAuditLoading] = reactExports.useState(false);
  const tabs = [
    {
      id: "users",
      name: "User Management",
      icon: Users,
      description: "Manage teachers, admins, and students"
    },
    {
      id: "institute",
      name: "Institute Settings",
      icon: Building2,
      description: "Manage institution information and branding"
    }
  ];
  reactExports.useEffect(() => {
    if (activeTab === "users") {
      loadUsers();
    }
  }, [activeTab]);
  reactExports.useEffect(() => {
    filterUsers();
  }, [users, searchTerm, selectedRole, selectedStatus]);
  const loadUsers = async () => {
    try {
      setLoading(true);
      const userData = await getAllUsers();
      setUsers(userData);
    } catch {
      setMessage({ type: "error", text: "Failed to load users" });
    } finally {
      setLoading(false);
    }
  };
  const filterUsers = () => {
    let filtered = users;
    if (searchTerm) {
      filtered = filtered.filter(
        (user2) => user2.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) || user2.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) || user2.username?.toLowerCase().includes(searchTerm.toLowerCase()) || user2.email?.toLowerCase().includes(searchTerm.toLowerCase()) || user2.teacher_id?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    if (selectedRole !== "all") {
      filtered = filtered.filter((user2) => user2.role === selectedRole);
    }
    if (selectedStatus !== "all") {
      filtered = filtered.filter(
        (user2) => selectedStatus === "active" ? user2.is_active : !user2.is_active
      );
    }
    setFilteredUsers(filtered);
  };
  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: "", text: "" }), 5e3);
  };
  const handleCreateUser = async (userData) => {
    try {
      setLoading(true);
      await createUser({
        ...userData,
        createdBy: user.id
      });
      showMessage("success", "User created successfully");
      setShowCreateModal(false);
      loadUsers();
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setLoading(false);
    }
  };
  const handleUpdateUser = async (userData) => {
    try {
      setLoading(true);
      await updateUser(selectedUser.id, userData, user.id);
      showMessage("success", "User updated successfully");
      setShowEditModal(false);
      setSelectedUser(null);
      loadUsers();
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setLoading(false);
    }
  };
  const handleResetPassword = async () => {
    try {
      setLoading(true);
      await resetUserPassword(selectedUser.id, newPassword, user.id);
      showMessage("success", "Password reset successfully");
      setShowPasswordModal(false);
      setSelectedUser(null);
      setNewPassword("");
    } catch {
      showMessage("error", "Failed to reset password");
    } finally {
      setLoading(false);
    }
  };
  const handleGeneratePassword = async () => {
    try {
      const generatedPassword = await generateSecurePassword();
      setNewPassword(generatedPassword);
      return generatedPassword;
    } catch {
      showMessage("error", "Failed to generate password");
      return null;
    }
  };
  const handleToggleUserStatus = async (targetUser) => {
    try {
      setLoading(true);
      if (targetUser.is_active) {
        await deactivateUser(targetUser.id, user.id);
        showMessage("success", "User deactivated successfully");
      } else {
        await activateUser(targetUser.id, user.id);
        showMessage("success", "User activated successfully");
      }
      loadUsers();
    } catch {
      showMessage("error", "Failed to update user status");
    } finally {
      setLoading(false);
    }
  };
  const openConfirmModal = (action, targetUser) => {
    setConfirmAction(action);
    setSelectedUser(targetUser);
    setShowConfirmModal(true);
  };
  const handleConfirmAction = async () => {
    if (confirmAction && selectedUser) {
      if (confirmAction === "toggleStatus") {
        await handleToggleUserStatus(selectedUser);
      }
    }
    setShowConfirmModal(false);
    setConfirmAction(null);
    setSelectedUser(null);
  };
  const loadAuditLog = async () => {
    try {
      setAuditLoading(true);
      const logs = await getAuditLog({ limit: 50 });
      setAuditLog(logs);
    } catch {
      showMessage("error", "Failed to load audit log");
    } finally {
      setAuditLoading(false);
    }
  };
  if (!isSuperAdmin) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-center min-h-screen", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Shield, { className: "mx-auto h-12 w-12 text-red-500" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 243,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "mt-2 text-lg font-medium text-gray-900", children: "Access Denied" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 244,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "You need Super Admin privileges to access this page." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 245,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
      lineNumber: 242,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
      lineNumber: 241,
      columnNumber: 7
    }, globalThis);
  }
  const getRoleBadgeColor = (role) => {
    const colors = {
      super_admin: "bg-red-100 text-red-800",
      admin: "bg-blue-100 text-blue-800",
      teacher: "bg-green-100 text-green-800",
      student: "bg-gray-100 text-gray-800"
    };
    return colors[role] || "bg-gray-100 text-gray-800";
  };
  const formatRole = (role) => {
    return role.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase());
  };
  const canEditUser = (targetUser) => {
    if (user.role === "super_admin") {
      return targetUser.id !== user.id;
    }
    return false;
  };
  const canResetPassword = (targetUser) => {
    if (user.role === "super_admin") {
      return targetUser.id !== user.id;
    }
    return false;
  };
  const canToggleStatus = (targetUser) => {
    if (user.role === "super_admin") {
      return targetUser.id !== user.id;
    }
    return false;
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-2xl font-bold text-gray-900", children: "Admin Panel" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
          lineNumber: 296,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: "Comprehensive administrative management" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
          lineNumber: 297,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 295,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => {
              setShowAuditModal(true);
              loadAuditLog();
            },
            className: "btn-outline flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 307,
                columnNumber: 13
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Audit Log" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 308,
                columnNumber: 13
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
            lineNumber: 300,
            columnNumber: 11
          },
          globalThis
        ),
        activeTab === "users" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setShowCreateModal(true),
            className: "btn-primary flex items-center space-x-2",
            "data-testid": "create-user-button",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Plus, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 316,
                columnNumber: 15
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Add User" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 317,
                columnNumber: 15
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
            lineNumber: 311,
            columnNumber: 13
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 299,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
      lineNumber: 294,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "border-b border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("nav", { className: "-mb-px flex space-x-8 px-6", children: tabs.map((tab) => {
        const Icon = tab.icon;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setActiveTab(tab.id),
            className: `${activeTab === tab.id ? "border-blue-500 text-blue-600" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`,
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Icon, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 339,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: tab.name }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 340,
                columnNumber: 19
              }, globalThis)
            ]
          },
          tab.id,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
            lineNumber: 330,
            columnNumber: 17
          },
          globalThis
        );
      }) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 326,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 325,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
        activeTab === "users" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", "data-testid": "user-management", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-lg font-medium text-gray-900 mb-2", children: "User Management" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 352,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Manage teachers, admins, and students in the system" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 353,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
            lineNumber: 351,
            columnNumber: 15
          }, globalThis),
          message.text && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `rounded-md p-4 ${message.type === "error" ? "bg-red-50" : "bg-green-50"}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: message.type === "error" ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5 text-red-400" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 362,
              columnNumber: 25
            }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-green-400" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 364,
              columnNumber: 25
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 360,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: `text-sm ${message.type === "error" ? "text-red-800" : "text-green-800"}`, children: message.text }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 368,
              columnNumber: 23
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 367,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
            lineNumber: 359,
            columnNumber: 19
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
            lineNumber: 358,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg p-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Search Users" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 380,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Search, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 384,
                  columnNumber: 23
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "input",
                  {
                    type: "text",
                    placeholder: "Search by name, username, email, or teacher ID...",
                    className: "input pl-10",
                    value: searchTerm,
                    onChange: (e) => setSearchTerm(e.target.value),
                    "data-testid": "search-input"
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                    lineNumber: 385,
                    columnNumber: 23
                  },
                  globalThis
                )
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 383,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 379,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Filter by Role" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 396,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "select",
                {
                  className: "input",
                  value: selectedRole,
                  onChange: (e) => setSelectedRole(e.target.value),
                  children: [
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "all", children: "All Roles" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 404,
                      columnNumber: 23
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "super_admin", children: "Super Admin" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 405,
                      columnNumber: 23
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "admin", children: "Admin" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 406,
                      columnNumber: 23
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "teacher", children: "Teacher" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 407,
                      columnNumber: 23
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "student", children: "Student" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 408,
                      columnNumber: 23
                    }, globalThis)
                  ]
                },
                void 0,
                true,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 399,
                  columnNumber: 21
                },
                globalThis
              )
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 395,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Filter by Status" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 412,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "select",
                {
                  className: "input",
                  value: selectedStatus,
                  onChange: (e) => setSelectedStatus(e.target.value),
                  children: [
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "all", children: "All Status" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 420,
                      columnNumber: 23
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "active", children: "Active" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 421,
                      columnNumber: 23
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "inactive", children: "Inactive" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 422,
                      columnNumber: 23
                    }, globalThis)
                  ]
                },
                void 0,
                true,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 415,
                  columnNumber: 21
                },
                globalThis
              )
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 411,
              columnNumber: 19
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
            lineNumber: 378,
            columnNumber: 17
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
            lineNumber: 377,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg overflow-hidden", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-6 py-4 border-b border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-5 w-5 mr-2" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 432,
                columnNumber: 21
              }, globalThis),
              "Users (",
              filteredUsers.length,
              ")"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 431,
              columnNumber: 19
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 430,
              columnNumber: 17
            }, globalThis),
            loading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 439,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-gray-500", children: "Loading users..." }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 440,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 438,
              columnNumber: 19
            }, globalThis) : filteredUsers.length > 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("table", { className: "min-w-full divide-y divide-gray-200", "data-testid": "users-table", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("thead", { className: "bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "User" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 447,
                  columnNumber: 27
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Role" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 450,
                  columnNumber: 27
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Teacher ID" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 453,
                  columnNumber: 27
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Status" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 456,
                  columnNumber: 27
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Created" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 459,
                  columnNumber: 27
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Actions" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 462,
                  columnNumber: 27
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 446,
                columnNumber: 25
              }, globalThis) }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 445,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tbody", { className: "bg-white divide-y divide-gray-200", children: filteredUsers.map((user2) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { className: "hover:bg-gray-50", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0 h-10 w-10", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-white font-medium text-sm", children: [
                    user2.first_name[0],
                    user2.last_name[0]
                  ] }, void 0, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                    lineNumber: 474,
                    columnNumber: 37
                  }, globalThis) }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                    lineNumber: 473,
                    columnNumber: 35
                  }, globalThis) }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                    lineNumber: 472,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-4", children: [
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-gray-900", children: [
                      user2.first_name,
                      " ",
                      user2.last_name
                    ] }, void 0, true, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 480,
                      columnNumber: 35
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: user2.email }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 483,
                      columnNumber: 35
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-xs text-gray-400", children: [
                      "@",
                      user2.username
                    ] }, void 0, true, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 486,
                      columnNumber: 35
                    }, globalThis)
                  ] }, void 0, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                    lineNumber: 479,
                    columnNumber: 33
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 471,
                  columnNumber: 31
                }, globalThis) }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 470,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(user2.role)}`, children: formatRole(user2.role) }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 493,
                  columnNumber: 31
                }, globalThis) }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 492,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: user2.teacher_id || (user2.role === "teacher" ? "Not Set" : "-") }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 497,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${user2.is_active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`, children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(UserCheck, { className: "h-3 w-3 mr-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                    lineNumber: 502,
                    columnNumber: 33
                  }, globalThis),
                  user2.is_active ? "Active" : "Inactive"
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 501,
                  columnNumber: 31
                }, globalThis) }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 500,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500", children: new Date(user2.created_at).toLocaleDateString() }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 506,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-right text-sm font-medium", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end space-x-1", children: [
                  canEditUser(user2) && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                    "button",
                    {
                      onClick: () => {
                        setSelectedUser(user2);
                        setShowEditModal(true);
                      },
                      className: "p-1 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded",
                      title: "Edit User",
                      children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(PenSquare, { className: "h-4 w-4" }, void 0, false, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                        lineNumber: 520,
                        columnNumber: 37
                      }, globalThis)
                    },
                    void 0,
                    false,
                    {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 512,
                      columnNumber: 35
                    },
                    globalThis
                  ),
                  canResetPassword(user2) && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                    "button",
                    {
                      onClick: () => {
                        setSelectedUser(user2);
                        setShowPasswordModal(true);
                      },
                      className: "p-1 text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50 rounded",
                      title: "Reset Password",
                      children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Key, { className: "h-4 w-4" }, void 0, false, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                        lineNumber: 532,
                        columnNumber: 37
                      }, globalThis)
                    },
                    void 0,
                    false,
                    {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 524,
                      columnNumber: 35
                    },
                    globalThis
                  ),
                  canToggleStatus(user2) && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                    "button",
                    {
                      onClick: () => openConfirmModal("toggleStatus", user2),
                      className: `p-1 hover:bg-gray-50 rounded ${user2.is_active ? "text-red-600 hover:text-red-900" : "text-green-600 hover:text-green-900"}`,
                      title: user2.is_active ? "Deactivate User" : "Activate User",
                      children: user2.is_active ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ShieldOff, { className: "h-4 w-4" }, void 0, false, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                        lineNumber: 545,
                        columnNumber: 55
                      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Shield, { className: "h-4 w-4" }, void 0, false, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                        lineNumber: 545,
                        columnNumber: 91
                      }, globalThis)
                    },
                    void 0,
                    false,
                    {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                      lineNumber: 536,
                      columnNumber: 35
                    },
                    globalThis
                  )
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 510,
                  columnNumber: 31
                }, globalThis) }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                  lineNumber: 509,
                  columnNumber: 29
                }, globalThis)
              ] }, user2.id, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 469,
                columnNumber: 27
              }, globalThis)) }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 467,
                columnNumber: 23
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 444,
              columnNumber: 21
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 443,
              columnNumber: 19
            }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 557,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No users found" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 558,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: searchTerm || selectedRole !== "all" ? "Try adjusting your search or filter criteria." : "Get started by creating a new user." }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
                lineNumber: 559,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
              lineNumber: 556,
              columnNumber: 19
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
            lineNumber: 429,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
          lineNumber: 350,
          columnNumber: 13
        }, globalThis),
        activeTab === "institute" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(InstituteSettings, {}, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
          lineNumber: 572,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 348,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
      lineNumber: 324,
      columnNumber: 7
    }, globalThis),
    showCreateModal && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      UserForm,
      {
        onSubmit: handleCreateUser,
        onCancel: () => setShowCreateModal(false),
        loading,
        generatePassword: handleGeneratePassword,
        isEdit: false
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 579,
        columnNumber: 9
      },
      globalThis
    ),
    showEditModal && selectedUser && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      UserForm,
      {
        user: selectedUser,
        onSubmit: handleUpdateUser,
        onCancel: () => {
          setShowEditModal(false);
          setSelectedUser(null);
        },
        loading,
        generatePassword: handleGeneratePassword,
        isEdit: true
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 590,
        columnNumber: 9
      },
      globalThis
    ),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      PasswordResetModal,
      {
        user: selectedUser,
        isOpen: showPasswordModal,
        onClose: () => {
          setShowPasswordModal(false);
          setSelectedUser(null);
          setNewPassword("");
          setShowGeneratedPassword(false);
        },
        onSubmit: handleResetPassword,
        onGeneratePassword: handleGeneratePassword,
        loading
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 604,
        columnNumber: 7
      },
      globalThis
    ),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      ConfirmModal,
      {
        isOpen: showConfirmModal,
        onClose: () => {
          setShowConfirmModal(false);
          setConfirmAction(null);
          setSelectedUser(null);
        },
        onConfirm: handleConfirmAction,
        title: confirmAction === "toggleStatus" && selectedUser ? `${selectedUser.is_active ? "Deactivate" : "Activate"} User` : "Confirm Action",
        message: confirmAction === "toggleStatus" && selectedUser ? `Are you sure you want to ${selectedUser.is_active ? "deactivate" : "activate"} ${selectedUser.first_name} ${selectedUser.last_name}? ${selectedUser.is_active ? "They will no longer be able to access the system." : "They will regain access to the system."}` : "Are you sure you want to perform this action?",
        confirmText: confirmAction === "toggleStatus" && selectedUser ? selectedUser.is_active ? "Deactivate" : "Activate" : "Confirm",
        type: confirmAction === "toggleStatus" && selectedUser?.is_active ? "warning" : "info",
        loading
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 619,
        columnNumber: 7
      },
      globalThis
    ),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      AuditLogModal,
      {
        isOpen: showAuditModal,
        onClose: () => setShowAuditModal(false),
        auditLog,
        loading: auditLoading
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
        lineNumber: 651,
        columnNumber: 7
      },
      globalThis
    )
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AdminPanel.jsx",
    lineNumber: 292,
    columnNumber: 5
  }, globalThis);
};

export { AdminPanel as default };
