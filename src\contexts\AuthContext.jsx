import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

const AuthContext = createContext();

// Simplified AuthContext without session management

// eslint-disable-next-line react-refresh/only-export-components
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  // Default user with hardcoded credentials (hidden from UI)
  const defaultUser = {
    id: 1,
    username: 'admin',
    password: 'password', // Hidden from UI
    first_name: 'System',
    last_name: 'Administrator',
    email: '<EMAIL>',
    role: 'super_admin',
    institution_id: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Initialize authentication state - automatically set user based on selected role
    const initializeAuth = async () => {
      try {
        // Get selected role from localStorage (set by welcome screen)
        const selectedRole = localStorage.getItem('selectedRole');

        if (selectedRole) {
          // Create user object based on selected role
          const roleUser = {
            ...defaultUser,
            role: selectedRole,
            first_name: getRoleDisplayName(selectedRole),
            last_name: 'User'
          };

          setUser(roleUser);

          // Store in localStorage for persistence
          localStorage.setItem('peer_review_user', JSON.stringify(roleUser));
        } else {
          // Check for stored user
          const storedUser = localStorage.getItem('peer_review_user');
          if (storedUser) {
            try {
              const parsedUser = JSON.parse(storedUser);
              setUser(parsedUser);
            } catch {
              // Clear corrupted data
              localStorage.removeItem('peer_review_user');
            }
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Helper function to get display name for role
  const getRoleDisplayName = (role) => {
    switch (role) {
      case 'super_admin': return 'Administrator'; // Merged display name
      case 'admin': return 'Administrator';
      case 'teacher': return 'Teacher';
      case 'student': return 'Student';
      default: return 'User';
    }
  };

  const login = async (credentials) => {
    try {
      setLoading(true);

      // Simplified login - use hardcoded credentials (hidden from UI)
      if (credentials && (credentials.username === 'admin' || credentials.password === 'password')) {
        // Get current user or create default
        const currentUser = user || defaultUser;
        setUser(currentUser);

        // Store in localStorage
        localStorage.setItem('peer_review_user', JSON.stringify(currentUser));

        return { success: true, user: currentUser };
      }

      // If no credentials provided, just use current user or default
      const currentUser = user || defaultUser;
      setUser(currentUser);

      // Store in localStorage
      localStorage.setItem('peer_review_user', JSON.stringify(currentUser));

      return { success: true, user: currentUser };
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const logout = useCallback(() => {
    setUser(null);
    // Clear stored data
    localStorage.removeItem('peer_review_user');
    localStorage.removeItem('selectedRole');
  }, []);

  const updateUser = (updatedUser) => {
    setUser(updatedUser);
    localStorage.setItem('peer_review_user', JSON.stringify(updatedUser));
  };

  const value = {
    user,
    login,
    logout,
    updateUser,
    loading,
    isAuthenticated: !!user,
    isAdmin: user?.role === 'admin' || user?.role === 'super_admin',
    isSuperAdmin: user?.role === 'super_admin',
    isTeacher: user?.role === 'teacher',
    isStudent: user?.role === 'student'
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
