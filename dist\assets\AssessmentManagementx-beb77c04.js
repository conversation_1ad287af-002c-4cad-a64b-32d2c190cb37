import{r as m,j as e}from"./chunk-81a058b1.js";import{b as S,u as k}from"./main-272222cd.js";import{y as F,A as _,C as B,F as x,U as I,g as W,w as q,T as A}from"./chunk-0b87a8e8.js";import"./chunk-81a949b4.js";const G=()=>{const{getAllAssessments:v,createAssessment:h,getAllBatches:f}=S(),{user:j}=k(),[c,E]=m.useState([]),[y,w]=m.useState([]),[C,N]=m.useState(!0),[D,o]=m.useState(!1),[n,p]=m.useState({type:"",text:""}),[i,l]=m.useState({title:"",description:"",batchId:"",criteria:[{name:"Communication Skills",weight:25,maxScore:10},{name:"Technical Knowledge",weight:25,maxScore:10},{name:"Teamwork",weight:25,maxScore:10},{name:"Problem Solving",weight:25,maxScore:10}],scoringSystem:{type:"points",maxScore:100,passingScore:60},startDate:"",endDate:""});m.useEffect(()=>{b()},[]);const b=async()=>{try{N(!0);const[s,a]=await Promise.all([v(),f()]);E(s),w(a)}catch{p({type:"error",text:"Failed to load data"})}finally{N(!1)}},T=async s=>{s.preventDefault();try{await h({...i,createdBy:j.id,maxScore:i.scoringSystem.maxScore}),p({type:"success",text:"Assessment created successfully"}),o(!1),M(),b()}catch(a){p({type:"error",text:a.message})}},M=()=>{l({title:"",description:"",batchId:"",criteria:[{name:"Communication Skills",weight:25,maxScore:10},{name:"Technical Knowledge",weight:25,maxScore:10},{name:"Teamwork",weight:25,maxScore:10},{name:"Problem Solving",weight:25,maxScore:10}],scoringSystem:{type:"points",maxScore:100,passingScore:60},startDate:"",endDate:""})},P=()=>{l(s=>({...s,criteria:[...s.criteria,{name:"",weight:0,maxScore:10}]}))},u=(s,a,t)=>{l(r=>({...r,criteria:r.criteria.map((d,V)=>V===s?{...d,[a]:t}:d)}))},L=s=>{l(a=>({...a,criteria:a.criteria.filter((t,r)=>r!==s)}))},g=()=>i.criteria.reduce((s,a)=>s+(parseFloat(a.weight)||0),0);return e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("div",{children:[e.jsxDEV("h1",{className:"text-2xl font-bold text-gray-900",children:"Assessment Management"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:136,columnNumber:11},globalThis),e.jsxDEV("p",{className:"text-gray-600",children:"Create and manage peer review assessments"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:137,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:135,columnNumber:9},globalThis),e.jsxDEV("button",{onClick:()=>o(!0),className:"btn-primary flex items-center space-x-2",children:[e.jsxDEV(F,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:143,columnNumber:11},globalThis),e.jsxDEV("span",{children:"Create Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:144,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:139,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:134,columnNumber:7},globalThis),n.text&&e.jsxDEV("div",{className:`rounded-md p-4 ${n.type==="error"?"bg-red-50":"bg-green-50"}`,children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:n.type==="error"?e.jsxDEV(_,{className:"h-5 w-5 text-red-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:154,columnNumber:17},globalThis):e.jsxDEV(B,{className:"h-5 w-5 text-green-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:156,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:152,columnNumber:13},globalThis),e.jsxDEV("div",{className:"ml-3",children:e.jsxDEV("p",{className:`text-sm ${n.type==="error"?"text-red-800":"text-green-800"}`,children:n.text},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:160,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:159,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:151,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:150,columnNumber:9},globalThis),C?e.jsxDEV("div",{className:"text-center py-8",children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:171,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-2 text-sm text-gray-500",children:"Loading assessments..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:172,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:170,columnNumber:9},globalThis):c.length>0?e.jsxDEV("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[e.jsxDEV("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(x,{className:"h-5 w-5 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:178,columnNumber:15},globalThis),"Assessments (",c.length,")"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:177,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:176,columnNumber:11},globalThis),e.jsxDEV("div",{className:"divide-y divide-gray-200",children:c.map(s=>e.jsxDEV("div",{className:"p-6 hover:bg-gray-50",children:e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV("h4",{className:"text-lg font-medium text-gray-900",children:s.title},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:188,columnNumber:21},globalThis),e.jsxDEV("p",{className:"text-gray-600 mt-1",children:s.description},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:189,columnNumber:21},globalThis),e.jsxDEV("div",{className:"mt-3 flex items-center space-x-6 text-sm text-gray-500",children:[e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(I,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:193,columnNumber:25},globalThis),e.jsxDEV("span",{children:["Batch: ",s.batch_name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:194,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:192,columnNumber:23},globalThis),e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(W,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:197,columnNumber:25},globalThis),e.jsxDEV("span",{children:["Max Score: ",s.max_score]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:198,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:196,columnNumber:23},globalThis),e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(q,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:201,columnNumber:25},globalThis),e.jsxDEV("span",{children:["Created: ",new Date(s.created_at).toLocaleDateString()]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:202,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:200,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:191,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:187,columnNumber:19},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-2",children:[e.jsxDEV("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${s.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.is_active?"Active":"Inactive"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:208,columnNumber:21},globalThis),e.jsxDEV("button",{className:"text-red-600 hover:text-red-900",children:e.jsxDEV(A,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:214,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:213,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:207,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:186,columnNumber:17},globalThis)},s.id,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:185,columnNumber:15},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:183,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:175,columnNumber:9},globalThis):e.jsxDEV("div",{className:"text-center py-12",children:[e.jsxDEV(x,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:224,columnNumber:11},globalThis),e.jsxDEV("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No assessments found"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:225,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by creating your first assessment."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:226,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:223,columnNumber:9},globalThis),D&&e.jsxDEV("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxDEV("div",{className:"relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white",children:e.jsxDEV("div",{className:"mt-3",children:[e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:237,columnNumber:15},globalThis),e.jsxDEV("form",{onSubmit:T,className:"space-y-6",children:[e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Assessment Title *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:243,columnNumber:21},globalThis),e.jsxDEV("input",{type:"text",required:!0,className:"input",value:i.title,onChange:s=>l(a=>({...a,title:s.target.value})),placeholder:"Enter assessment title"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:246,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:242,columnNumber:19},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Batch *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:257,columnNumber:21},globalThis),e.jsxDEV("select",{required:!0,className:"input",value:i.batchId,onChange:s=>l(a=>({...a,batchId:s.target.value})),children:[e.jsxDEV("option",{value:"",children:"Select a batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:266,columnNumber:23},globalThis),y.map(s=>e.jsxDEV("option",{value:s.id,children:[s.name," (",s.student_count," students)"]},s.id,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:268,columnNumber:25},globalThis))]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:260,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:256,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:241,columnNumber:17},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:277,columnNumber:19},globalThis),e.jsxDEV("textarea",{className:"input",rows:3,value:i.description,onChange:s=>l(a=>({...a,description:s.target.value})),placeholder:"Enter assessment description"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:280,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:276,columnNumber:17},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("div",{className:"flex justify-between items-center mb-3",children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700",children:["Assessment Criteria (Total Weight: ",g(),"%)"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:292,columnNumber:21},globalThis),e.jsxDEV("button",{type:"button",onClick:P,className:"btn-outline text-sm",children:"Add Criterion"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:295,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:291,columnNumber:19},globalThis),e.jsxDEV("div",{className:"space-y-3",children:i.criteria.map((s,a)=>e.jsxDEV("div",{className:"flex items-center space-x-3 p-3 border rounded-md",children:[e.jsxDEV("input",{type:"text",placeholder:"Criterion name",className:"input flex-1",value:s.name,onChange:t=>u(a,"name",t.target.value)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:307,columnNumber:25},globalThis),e.jsxDEV("input",{type:"number",placeholder:"Weight %",className:"input w-24",value:s.weight,onChange:t=>u(a,"weight",parseFloat(t.target.value)||0)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:314,columnNumber:25},globalThis),e.jsxDEV("input",{type:"number",placeholder:"Max Score",className:"input w-24",value:s.maxScore,onChange:t=>u(a,"maxScore",parseFloat(t.target.value)||0)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:321,columnNumber:25},globalThis),e.jsxDEV("button",{type:"button",onClick:()=>L(a),className:"text-red-600 hover:text-red-900",children:e.jsxDEV(A,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:333,columnNumber:27},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:328,columnNumber:25},globalThis)]},a,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:306,columnNumber:23},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:304,columnNumber:19},globalThis),g()!==100&&e.jsxDEV("p",{className:"text-sm text-orange-600 mt-2",children:"Warning: Total weight should equal 100%"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:340,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:290,columnNumber:17},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:349,columnNumber:21},globalThis),e.jsxDEV("input",{type:"datetime-local",className:"input",value:i.startDate,onChange:s=>l(a=>({...a,startDate:s.target.value}))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:352,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:348,columnNumber:19},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:361,columnNumber:21},globalThis),e.jsxDEV("input",{type:"datetime-local",className:"input",value:i.endDate,onChange:s=>l(a=>({...a,endDate:s.target.value}))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:364,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:360,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:347,columnNumber:17},globalThis),e.jsxDEV("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsxDEV("button",{type:"button",onClick:()=>o(!1),className:"btn-outline",children:"Cancel"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:374,columnNumber:19},globalThis),e.jsxDEV("button",{type:"submit",className:"btn-primary",disabled:g()!==100,children:"Create Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:381,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:373,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:239,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:236,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:235,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:234,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",lineNumber:132,columnNumber:5},globalThis)};export{G as default};
