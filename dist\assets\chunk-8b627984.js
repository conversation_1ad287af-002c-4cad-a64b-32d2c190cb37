// Validation utility functions for the admin panel


const validatePassword = (password) => {
  const errors = [];

  if (!password) {
    errors.push('Password is required');
    return errors;
  }

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (password.length > 128) {
    errors.push('Password is too long (maximum 128 characters)');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  // Check for common weak patterns
  const commonPatterns = [
    /(.)\1{2,}/, // Three or more consecutive identical characters
    /123456|654321|abcdef|qwerty|password/i, // Common sequences
  ];

  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      errors.push('Password contains common patterns and is not secure');
      break;
    }
  }

  return errors;
};

const validateEmail = (email) => {
  const errors = [];

  if (!email) {
    errors.push('Email is required');
    return errors;
  }

  // RFC 5322 compliant email regex
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  if (!emailRegex.test(email)) {
    errors.push('Please enter a valid email address');
  }

  if (email.length > 254) {
    errors.push('Email address is too long (maximum 254 characters)');
  }

  return errors;
};

const validatePhone = (phone) => {
  const errors = [];

  if (phone) { // Phone is optional
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');

    if (!phoneRegex.test(cleanPhone)) {
      errors.push('Please enter a valid phone number');
    }
  }

  return errors;
};

const validateName = (name, fieldName = 'Name') => {
  const errors = [];

  if (!name) {
    errors.push(`${fieldName} is required`);
    return errors;
  }

  if (name.length < 2) {
    errors.push(`${fieldName} must be at least 2 characters long`);
  }

  if (name.length > 50) {
    errors.push(`${fieldName} must be less than 50 characters long`);
  }

  if (!/^[a-zA-Z\s\-'\.]+$/.test(name)) {
    errors.push(`${fieldName} can only contain letters, spaces, hyphens, apostrophes, and periods`);
  }

  return errors;
};

const validateTeacherId = (teacherId) => {
  const errors = [];

  if (!teacherId) {
    errors.push('Teacher ID is required');
    return errors;
  }

  if (teacherId.length < 3) {
    errors.push('Teacher ID must be at least 3 characters long');
  }

  if (teacherId.length > 20) {
    errors.push('Teacher ID must be less than 20 characters long');
  }

  if (!/^[a-zA-Z0-9\-_]+$/.test(teacherId)) {
    errors.push('Teacher ID can only contain letters, numbers, hyphens, and underscores');
  }

  return errors;
};

const validateUserForm = (userData, isTeacher = false) => {
  const errors = {};

  // Validate first name
  const firstNameErrors = validateName(userData.firstName, 'First name');
  if (firstNameErrors.length > 0) {
    errors.firstName = firstNameErrors;
  }

  // Validate last name
  const lastNameErrors = validateName(userData.lastName, 'Last name');
  if (lastNameErrors.length > 0) {
    errors.lastName = lastNameErrors;
  }

  // Validate email
  const emailErrors = validateEmail(userData.email);
  if (emailErrors.length > 0) {
    errors.email = emailErrors;
  }

  // Validate phone (optional)
  const phoneErrors = validatePhone(userData.phone);
  if (phoneErrors.length > 0) {
    errors.phone = phoneErrors;
  }

  // Validate teacher ID if this is a teacher
  if (isTeacher && userData.teacherId) {
    const teacherIdErrors = validateTeacherId(userData.teacherId);
    if (teacherIdErrors.length > 0) {
      errors.teacherId = teacherIdErrors;
    }
  }

  // Validate password if provided
  if (userData.password) {
    const passwordErrors = validatePassword(userData.password);
    if (passwordErrors.length > 0) {
      errors.password = passwordErrors;
    }
  }

  return errors;
};

const hasValidationErrors = (errors) => {
  return Object.keys(errors).length > 0;
};

// Sanitization functions
const sanitizeInput = (input) => {
  if (typeof input !== 'string') {return input;}

  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes that could cause issues
    .substring(0, 255); // Limit length
};

const sanitizeUserData = (userData) => {
  const sanitized = {};

  Object.keys(userData).forEach(key => {
    if (key === 'password') {
      // Don't sanitize password, just trim
      sanitized[key] = userData[key]?.trim();
    } else if (typeof userData[key] === 'string') {
      sanitized[key] = sanitizeInput(userData[key]);
    } else {
      sanitized[key] = userData[key];
    }
  });

  return sanitized;
};

// Institution validation functions
const validateInstituteName = (name) => {
  const errors = [];

  if (!name) {
    errors.push('Institution name is required');
    return errors;
  }

  if (name.length < 2) {
    errors.push('Institution name must be at least 2 characters long');
  }

  if (name.length > 100) {
    errors.push('Institution name must be less than 100 characters long');
  }

  return errors;
};

const validateInstituteDescription = (description) => {
  const errors = [];

  if (description && description.length > 500) {
    errors.push('Description must be less than 500 characters long');
  }

  return errors;
};

const validateWebsiteURL = (url) => {
  const errors = [];

  if (url) {
    try {
      new URL(url);
    } catch {
      errors.push('Please enter a valid website URL (e.g., https://example.com)');
    }
  }

  return errors;
};

const validateEstablishedYear = (year) => {
  const errors = [];

  if (year) {
    // Handle edge cases for input validation
    const yearStr = String(year).trim();
    if (yearStr === '' || yearStr === 'null' || yearStr === 'undefined') {
      return errors; // Treat as empty input
    }

    const yearNum = parseInt(yearStr, 10);
    const currentYear = new Date().getFullYear();

    if (isNaN(yearNum) || !Number.isInteger(yearNum)) {
      errors.push('Please enter a valid year');
    } else if (yearNum < 1800) {
      errors.push('Established year cannot be before 1800');
    } else if (yearNum > currentYear) {
      errors.push('Established year cannot be in the future');
    }
  }

  return errors;
};

const validateInstituteType = (type) => {
  const errors = [];
  const validTypes = ['University', 'College', 'School', 'Training Center', 'Other'];

  if (type && !validTypes.includes(type)) {
    errors.push('Please select a valid institute type');
  }

  return errors;
};

const validateInstitutionForm = (institutionData) => {
  const errors = {};

  // Validate name
  const nameErrors = validateInstituteName(institutionData.name);
  if (nameErrors.length > 0) {
    errors.name = nameErrors;
  }

  // Validate description
  const descriptionErrors = validateInstituteDescription(institutionData.description);
  if (descriptionErrors.length > 0) {
    errors.description = descriptionErrors;
  }

  // Validate email
  const emailErrors = validateEmail(institutionData.email);
  if (emailErrors.length > 0) {
    errors.email = emailErrors;
  }

  // Validate phone
  const phoneErrors = validatePhone(institutionData.phone);
  if (phoneErrors.length > 0) {
    errors.phone = phoneErrors;
  }

  // Validate address
  if (!institutionData.address) {
    errors.address = ['Address is required'];
  } else if (institutionData.address.length < 10) {
    errors.address = ['Address must be at least 10 characters long'];
  }

  // Validate website URL
  const urlErrors = validateWebsiteURL(institutionData.websiteUrl);
  if (urlErrors.length > 0) {
    errors.websiteUrl = urlErrors;
  }

  // Validate established year
  const yearErrors = validateEstablishedYear(institutionData.establishedYear);
  if (yearErrors.length > 0) {
    errors.establishedYear = yearErrors;
  }

  // Validate institute type
  const typeErrors = validateInstituteType(institutionData.instituteType);
  if (typeErrors.length > 0) {
    errors.instituteType = typeErrors;
  }

  return errors;
};

// File validation for logo uploads
const validateLogoFile = (file) => {
  const errors = [];

  if (!file) {
    errors.push('Please select a file');
    return errors;
  }

  // Check file type
  const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];
  if (!allowedTypes.includes(file.type)) {
    errors.push('Only PNG, JPG, JPEG, and SVG files are allowed');
  }

  // Check file size (2MB limit)
  const maxSize = 2 * 1024 * 1024; // 2MB
  if (file.size > maxSize) {
    errors.push('File size must be less than 2MB');
  }

  return errors;
};

// Batch validation functions
const validateBatchName = (name) => {
  const errors = [];

  if (!name) {
    errors.push('Batch name is required');
    return errors;
  }

  if (name.length < 3) {
    errors.push('Batch name must be at least 3 characters long');
  }

  if (name.length > 50) {
    errors.push('Batch name must be less than 50 characters long');
  }

  if (!/^[a-zA-Z0-9\s]+$/.test(name)) {
    errors.push('Batch name can only contain letters, numbers, and spaces');
  }

  return errors;
};

const validateAcademicYear = (year) => {
  const errors = [];

  if (!year) {
    errors.push('Academic year is required');
    return errors;
  }

  // Format: YYYY-YYYY (e.g., 2024-2025)
  const yearPattern = /^\d{4}-\d{4}$/;
  if (!yearPattern.test(year)) {
    errors.push('Academic year must be in format YYYY-YYYY (e.g., 2024-2025)');
    return errors;
  }

  const yearParts = year.split('-');
  if (yearParts.length !== 2) {
    errors.push('Academic year must be in format YYYY-YYYY (e.g., 2024-2025)');
    return errors;
  }

  const startYearStr = yearParts[0].trim();
  const endYearStr = yearParts[1].trim();

  const startYear = parseInt(startYearStr, 10);
  const endYear = parseInt(endYearStr, 10);
  const currentYear = new Date().getFullYear();

  if (isNaN(startYear) || !Number.isInteger(startYear)) {
    errors.push('Start year must be a valid integer');
    return errors;
  }

  if (isNaN(endYear) || !Number.isInteger(endYear)) {
    errors.push('End year must be a valid integer');
    return errors;
  }

  if (startYear < 2000 || startYear > currentYear + 5) {
    errors.push('Start year must be between 2000 and ' + (currentYear + 5));
  }

  if (endYear !== startYear + 1) {
    errors.push('End year must be exactly one year after start year');
  }

  return errors;
};

const validateCourseType = (courseType) => {
  const errors = [];
  const validCourseTypes = ['Course 01', 'Course 02', 'Course 03', 'Course 04'];

  if (!courseType) {
    errors.push('Course type is required');
    return errors;
  }

  if (!validCourseTypes.includes(courseType)) {
    errors.push('Please select a valid course type');
  }

  return errors;
};

const validateBatchDescription = (description) => {
  const errors = [];

  if (description && description.length > 200) {
    errors.push('Description must be less than 200 characters long');
  }

  return errors;
};

// Student validation functions
const validateStudentFullName = (fullName) => {
  const errors = [];

  if (!fullName) {
    errors.push('Full name is required');
    return errors;
  }

  if (fullName.length < 2) {
    errors.push('Full name must be at least 2 characters long');
  }

  if (fullName.length > 50) {
    errors.push('Full name must be less than 50 characters long');
  }

  if (!/^[a-zA-Z\s]+$/.test(fullName)) {
    errors.push('Full name can only contain letters and spaces');
  }

  // Check if it has at least first and last name
  const nameParts = fullName.trim().split(' ').filter(part => part.length > 0);
  if (nameParts.length < 1) {
    errors.push('Please provide at least a first name');
  }

  return errors;
};

const validateStudentId = (studentId) => {
  const errors = [];

  if (!studentId) {
    errors.push('Student ID is required');
    return errors;
  }

  if (studentId.length < 6) {
    errors.push('Student ID must be at least 6 characters long');
  }

  if (studentId.length > 20) {
    errors.push('Student ID must be less than 20 characters long');
  }

  if (!/^[a-zA-Z0-9]+$/.test(studentId)) {
    errors.push('Student ID can only contain letters and numbers');
  }

  return errors;
};

const validateYearOfBirth = (year) => {
  const errors = [];

  if (!year) {
    errors.push('Year of birth is required');
    return errors;
  }

  // Handle edge cases for input validation
  const yearStr = String(year).trim();
  if (yearStr === '' || yearStr === 'null' || yearStr === 'undefined') {
    errors.push('Year of birth is required');
    return errors;
  }

  const yearNum = parseInt(yearStr, 10);
  const currentYear = new Date().getFullYear();

  if (isNaN(yearNum) || !Number.isInteger(yearNum)) {
    errors.push('Please enter a valid year');
  } else if (yearNum < currentYear - 100) {
    errors.push('Year of birth cannot be more than 100 years ago');
  } else if (yearNum > currentYear - 10) {
    errors.push('Year of birth cannot be less than 10 years ago');
  } else if (yearNum < 1900) {
    errors.push('Year of birth must be after 1900');
  } else if (yearNum > currentYear) {
    errors.push('Year of birth cannot be in the future');
  }

  return errors;
};

// Batch form validation
const validateBatchForm = (batchData) => {
  const errors = {};

  // Validate batch name
  const nameErrors = validateBatchName(batchData.name);
  if (nameErrors.length > 0) {
    errors.name = nameErrors;
  }

  // Validate academic year
  const yearErrors = validateAcademicYear(batchData.academicYear);
  if (yearErrors.length > 0) {
    errors.academicYear = yearErrors;
  }

  // Validate course type
  const courseErrors = validateCourseType(batchData.courseType);
  if (courseErrors.length > 0) {
    errors.courseType = courseErrors;
  }

  // Validate description
  const descriptionErrors = validateBatchDescription(batchData.description);
  if (descriptionErrors.length > 0) {
    errors.description = descriptionErrors;
  }

  return errors;
};

// Student form validation
const validateStudentForm = (studentData) => {
  const errors = {};

  // Validate full name
  const nameErrors = validateStudentFullName(studentData.fullName);
  if (nameErrors.length > 0) {
    errors.fullName = nameErrors;
  }

  // Validate student ID
  const idErrors = validateStudentId(studentData.studentId);
  if (idErrors.length > 0) {
    errors.studentId = idErrors;
  }

  // Validate password
  const passwordErrors = validatePassword(studentData.password);
  if (passwordErrors.length > 0) {
    errors.password = passwordErrors;
  }

  // Validate year of birth
  const yearErrors = validateYearOfBirth(studentData.yearOfBirth);
  if (yearErrors.length > 0) {
    errors.yearOfBirth = yearErrors;
  }

  // Validate email if provided
  if (studentData.email) {
    const emailErrors = validateEmail(studentData.email);
    if (emailErrors.length > 0) {
      errors.email = emailErrors;
    }
  }

  return errors;
};

// Validate entire batch with students
const validateBatchWithStudents = (batchData, studentsData) => {
  const errors = {
    batch: {},
    students: []
  };

  // Validate batch data
  errors.batch = validateBatchForm(batchData);

  // Validate students data
  if (!studentsData || studentsData.length === 0) {
    errors.batch.students = ['At least one student is required'];
  } else if (studentsData.length > 50) {
    errors.batch.students = ['Maximum 50 students allowed per batch'];
  } else {
    // Validate each student
    studentsData.forEach((student, index) => {
      const studentErrors = validateStudentForm(student);
      if (Object.keys(studentErrors).length > 0) {
        errors.students[index] = studentErrors;
      }
    });

    // Check for duplicate student IDs within the batch
    const studentIds = studentsData.map(s => s.studentId).filter(id => id);
    const duplicateIds = studentIds.filter((id, index) => studentIds.indexOf(id) !== index);

    if (duplicateIds.length > 0) {
      errors.batch.duplicateStudentIds = [`Duplicate student IDs found: ${[...new Set(duplicateIds)].join(', ')}`];
    }
  }

  return errors;
};

export { validatePassword as a, validateLogoFile as b, validateInstitutionForm as c, validateBatchWithStudents as d, validateStudentForm as e, hasValidationErrors as h, sanitizeUserData as s, validateUserForm as v };
