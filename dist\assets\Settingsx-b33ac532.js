import { r as reactExports, j as jsxDevRuntimeExports, R as React, u as useNavigate } from './chunk-2ef8e52b.js';
import { a as useMessage, g as getElectronAPI, u as useAuth } from './main-ef45ddde.js';
import './chunk-1466ddc5.js';
import { c as Shield, ab as Zap, I as Info, ac as Check, X, o as EyeOff, k as Eye, a as AlertTriangle, a4 as Lock, A as AlertCircle, C as CheckCircle, ad as Loader2, R as RefreshCw, W as Download, ae as HardDrive, af as Unlock, w as Calendar, F as FileText, t as Upload, T as Trash2, Z as ArrowLeft, g as Settings$1, L as LogOut, ag as Book, U as Users, a5 as BarChart3, G as GraduationCap, p as User, m as HelpCircle, ah as Palette, ai as Bell, x as Save } from './chunk-0deb1b3d.js';
import './chunk-03d61bd9.js';

// Enhanced password security configuration
const PASSWORD_CONFIG = {
  MIN_LENGTH: 8,
  MAX_LENGTH: 128,
  MIN_COST_FACTOR: 8, // Optimized for performance while maintaining security
  HISTORY_LIMIT: 5,
  COMPLEXITY_REQUIREMENTS: {
    uppercase: /[A-Z]/,
    lowercase: /[a-z]/,
    numbers: /\d/,
    specialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/
  },
  STRENGTH_LEVELS: {
    VERY_WEAK: 0,
    WEAK: 1,
    FAIR: 2,
    GOOD: 3,
    STRONG: 4,
    VERY_STRONG: 5
  },
  STRENGTH_COLORS: {
    0: { bg: 'bg-red-500', text: 'text-red-700', label: 'Very Weak' },
    1: { bg: 'bg-red-400', text: 'text-red-600', label: 'Weak' },
    2: { bg: 'bg-yellow-500', text: 'text-yellow-700', label: 'Fair' },
    3: { bg: 'bg-yellow-400', text: 'text-yellow-600', label: 'Good' },
    4: { bg: 'bg-green-500', text: 'text-green-700', label: 'Strong' },
    5: { bg: 'bg-green-600', text: 'text-green-800', label: 'Very Strong' }
  }
};

/**
 * Calculate password entropy for strength assessment
 */
const calculatePasswordEntropy = (password) => {
  if (!password) {return 0;}

  const charSets = {
    lowercase: /[a-z]/.test(password) ? 26 : 0,
    uppercase: /[A-Z]/.test(password) ? 26 : 0,
    numbers: /\d/.test(password) ? 10 : 0,
    specialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password) ? 32 : 0,
    extendedSpecial: /[~`¡¢£¤¥¦§¨©ª«¬®¯°±²³´µ¶·¸¹º»¼½¾¿]/.test(password) ? 20 : 0
  };

  const charsetSize = Object.values(charSets).reduce((sum, size) => sum + size, 0);
  return password.length * Math.log2(charsetSize || 1);
};

/**
 * Check for common password patterns and weaknesses
 */
const analyzePasswordPatterns = (password) => {
  const patterns = {
    repeating: /(.)\1{2,}/.test(password),
    sequential: /(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)/i.test(password),
    keyboard: /(qwe|wer|ert|rty|tyu|yui|uio|iop|asd|sdf|dfg|fgh|ghj|hjk|jkl|zxc|xcv|cvb|vbn|bnm)/i.test(password),
    common: /(password|admin|user|login|welcome|123456|qwerty|letmein|monkey|dragon)/i.test(password),
    dates: /\b(19|20)\d{2}\b|\b(0?[1-9]|1[0-2])[\/\-](0?[1-9]|[12]\d|3[01])[\/\-](\d{2}|\d{4})\b/.test(password),
    personal: /(name|birth|phone|address|email)/i.test(password)
  };

  return patterns;
};

/**
 * Generate password improvement suggestions
 */
const generatePasswordSuggestions = (password, requirements, patterns) => {
  const suggestions = [];

  if (!requirements.length) {
    suggestions.push(`Make it at least ${PASSWORD_CONFIG.MIN_LENGTH} characters long`);
  }

  if (!requirements.uppercase) {
    suggestions.push('Add uppercase letters (A-Z)');
  }

  if (!requirements.lowercase) {
    suggestions.push('Add lowercase letters (a-z)');
  }

  if (!requirements.numbers) {
    suggestions.push('Add numbers (0-9)');
  }

  if (!requirements.specialChars) {
    suggestions.push('Add special characters (!@#$%^&*)');
  }

  if (password.length < 12) {
    suggestions.push('Consider making it longer for better security');
  }

  if (patterns.repeating) {
    suggestions.push('Avoid repeating the same character multiple times');
  }

  if (patterns.sequential) {
    suggestions.push('Avoid sequential characters (123, abc)');
  }

  if (patterns.keyboard) {
    suggestions.push('Avoid keyboard patterns (qwerty, asdf)');
  }

  if (patterns.common) {
    suggestions.push('Avoid common words and phrases');
  }

  if (patterns.dates) {
    suggestions.push('Avoid using dates in your password');
  }

  if (suggestions.length === 0) {
    const entropy = calculatePasswordEntropy(password);
    if (entropy < 50) {
      suggestions.push('Consider adding more character variety for stronger security');
    } else if (entropy < 70) {
      suggestions.push('Good password! Consider making it even longer for maximum security');
    } else {
      suggestions.push('Excellent password strength!');
    }
  }

  return suggestions;
};

/**
 * Enhanced password strength analysis with detailed feedback
 */
const checkPasswordStrength = (password) => {
  if (!password) {
    return {
      strength: PASSWORD_CONFIG.STRENGTH_LEVELS.VERY_WEAK,
      strengthLabel: PASSWORD_CONFIG.STRENGTH_COLORS[0].label,
      score: 0,
      percentage: 0,
      entropy: 0,
      feedback: ['Password is required'],
      suggestions: ['Enter a password to see strength analysis'],
      requirements: {
        length: false,
        uppercase: false,
        lowercase: false,
        numbers: false,
        specialChars: false
      },
      patterns: {},
      isValid: false,
      color: PASSWORD_CONFIG.STRENGTH_COLORS[0],
      meetsMinimum: false
    };
  }

  let score = 0;
  const requirements = {
    length: password.length >= PASSWORD_CONFIG.MIN_LENGTH,
    uppercase: PASSWORD_CONFIG.COMPLEXITY_REQUIREMENTS.uppercase.test(password),
    lowercase: PASSWORD_CONFIG.COMPLEXITY_REQUIREMENTS.lowercase.test(password),
    numbers: PASSWORD_CONFIG.COMPLEXITY_REQUIREMENTS.numbers.test(password),
    specialChars: PASSWORD_CONFIG.COMPLEXITY_REQUIREMENTS.specialChars.test(password)
  };

  // Base score from requirements
  Object.values(requirements).forEach(met => {
    if (met) {score += 1;}
  });

  // Calculate entropy for additional scoring
  const entropy = calculatePasswordEntropy(password);
  const patterns = analyzePasswordPatterns(password);

  // Bonus points for length
  if (password.length >= 12) {score += 0.5;}
  if (password.length >= 16) {score += 0.5;}
  if (password.length >= 20) {score += 0.5;}

  // Entropy bonus
  if (entropy >= 50) {score += 0.5;}
  if (entropy >= 70) {score += 0.5;}

  // Pattern penalties
  if (patterns.repeating) {score -= 0.5;}
  if (patterns.sequential) {score -= 0.5;}
  if (patterns.keyboard) {score -= 0.5;}
  if (patterns.common) {score -= 1;}
  if (patterns.dates) {score -= 0.5;}

  // Ensure score is within bounds
  score = Math.max(0, Math.min(5, score));
  const strengthLevel = Math.floor(score);
  const percentage = Math.round((score / 5) * 100);

  const suggestions = generatePasswordSuggestions(password, requirements, patterns);
  const meetsMinimum = Object.values(requirements).every(Boolean);
  const isValid = strengthLevel >= 3 && meetsMinimum;

  return {
    strength: strengthLevel,
    strengthLabel: PASSWORD_CONFIG.STRENGTH_COLORS[strengthLevel].label,
    score: Math.round(score * 10) / 10, // Round to 1 decimal place
    percentage,
    entropy: Math.round(entropy),
    feedback: suggestions,
    suggestions,
    requirements,
    patterns,
    isValid,
    color: PASSWORD_CONFIG.STRENGTH_COLORS[strengthLevel],
    meetsMinimum
  };
};

const PasswordStrengthMeter = ({
  password = "",
  showRequirements = true,
  showSuggestions = true,
  showEntropy = false,
  compact = false,
  className = ""
}) => {
  const strengthData = reactExports.useMemo(() => {
    return checkPasswordStrength(password);
  }, [password]);
  const {
    strength,
    strengthLabel,
    percentage,
    entropy,
    requirements,
    suggestions,
    color,
    meetsMinimum
  } = strengthData;
  if (!password && compact) {
    return null;
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `space-y-3 ${className}`, children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between text-sm", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-700", children: "Password Strength" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
          lineNumber: 50,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `font-semibold ${color.text}`, children: [
          strengthLabel,
          " ",
          showEntropy && entropy > 0 && `(${entropy} bits)`
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
          lineNumber: 51,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 49,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "w-full bg-gray-200 rounded-full h-2", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "div",
          {
            className: `h-2 rounded-full transition-all duration-300 ease-in-out ${color.bg}`,
            style: { width: `${percentage}%` }
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
            lineNumber: 58,
            columnNumber: 13
          },
          globalThis
        ) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
          lineNumber: 57,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between mt-1", children: Array.from({ length: 6 }, (_, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "div",
          {
            className: `w-2 h-1 rounded-full transition-colors duration-300 ${index <= strength ? PASSWORD_CONFIG.STRENGTH_COLORS[Math.min(index, 5)].bg : "bg-gray-200"}`
          },
          index,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
            lineNumber: 67,
            columnNumber: 15
          },
          globalThis
        )) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
          lineNumber: 65,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 56,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 48,
      columnNumber: 7
    }, globalThis),
    showRequirements && !compact && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-gray-700 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Shield, { className: "h-4 w-4 mr-1" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
          lineNumber: 84,
          columnNumber: 13
        }, globalThis),
        "Requirements"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 83,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-2", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          RequirementItem,
          {
            met: requirements.length,
            text: `At least ${PASSWORD_CONFIG.MIN_LENGTH} characters`
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
            lineNumber: 88,
            columnNumber: 13
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          RequirementItem,
          {
            met: requirements.uppercase,
            text: "Uppercase letter (A-Z)"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
            lineNumber: 92,
            columnNumber: 13
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          RequirementItem,
          {
            met: requirements.lowercase,
            text: "Lowercase letter (a-z)"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
            lineNumber: 96,
            columnNumber: 13
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          RequirementItem,
          {
            met: requirements.numbers,
            text: "Number (0-9)"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
            lineNumber: 100,
            columnNumber: 13
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          RequirementItem,
          {
            met: requirements.specialChars,
            text: "Special character (!@#$%)"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
            lineNumber: 104,
            columnNumber: 13
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          RequirementItem,
          {
            met: meetsMinimum,
            text: "All requirements met",
            highlight: true
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
            lineNumber: 108,
            columnNumber: 13
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 87,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 82,
      columnNumber: 9
    }, globalThis),
    showRequirements && compact && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex flex-wrap gap-1", children: Object.entries(requirements).map(([key, met]) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RequirementBadge, { type: key, met }, key, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 121,
      columnNumber: 13
    }, globalThis)) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 119,
      columnNumber: 9
    }, globalThis),
    showSuggestions && suggestions.length > 0 && !compact && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-gray-700 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Zap, { className: "h-4 w-4 mr-1" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
          lineNumber: 130,
          columnNumber: 13
        }, globalThis),
        strength < 3 ? "Suggestions" : "Tips"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 129,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "space-y-1", children: suggestions.slice(0, 3).map((suggestion, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: "text-sm text-gray-600 flex items-start", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Info, { className: "h-3 w-3 mr-2 mt-0.5 text-blue-500 flex-shrink-0" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
          lineNumber: 136,
          columnNumber: 17
        }, globalThis),
        suggestion
      ] }, index, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 135,
        columnNumber: 15
      }, globalThis)) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 133,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 128,
      columnNumber: 9
    }, globalThis),
    !compact && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-100", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: [
        "Security Score: ",
        percentage,
        "%"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 147,
        columnNumber: 11
      }, globalThis),
      entropy > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: [
        "Entropy: ",
        entropy,
        " bits"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 148,
        columnNumber: 27
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 146,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
    lineNumber: 46,
    columnNumber: 5
  }, globalThis);
};
const RequirementItem = ({ met, text, highlight = false }) => {
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `flex items-center text-sm ${highlight ? met ? "text-green-700 font-medium" : "text-gray-500" : met ? "text-green-600" : "text-gray-500"}`, children: [
    met ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Check, { className: "h-4 w-4 mr-2 text-green-500" }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 170,
      columnNumber: 9
    }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-4 w-4 mr-2 text-gray-400" }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 172,
      columnNumber: 9
    }, globalThis),
    text
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
    lineNumber: 160,
    columnNumber: 5
  }, globalThis);
};
const RequirementBadge = ({ type, met }) => {
  const badges = {
    length: { label: "Len", icon: Lock },
    uppercase: { label: "A-Z", icon: null },
    lowercase: { label: "a-z", icon: null },
    numbers: { label: "0-9", icon: null },
    specialChars: { label: "!@#", icon: null }
  };
  const badge = badges[type];
  if (!badge) {
    return null;
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${met ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-500"}`, children: [
    badge.icon && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(badge.icon, { className: "h-3 w-3 mr-1" }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 200,
      columnNumber: 22
    }, globalThis),
    badge.label
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
    lineNumber: 195,
    columnNumber: 5
  }, globalThis);
};
const PasswordInputWithStrength = ({
  value = "",
  onChange,
  onBlur,
  placeholder = "Enter password",
  label = "Password",
  name = "password",
  required = false,
  disabled = false,
  error = "",
  showStrengthMeter = true,
  showRequirements = true,
  showSuggestions = true,
  compact = false,
  className = "",
  ...props
}) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const strengthData = reactExports.useMemo(() => checkPasswordStrength(value), [value]);
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `space-y-2 ${className}`, children: [
    label && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700", children: [
      label,
      required && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-red-500 ml-1", children: "*" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 239,
        columnNumber: 24
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 237,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "input",
        {
          type: showPassword ? "text" : "password",
          name,
          value,
          onChange,
          onBlur,
          placeholder,
          disabled,
          className: `
            block w-full px-3 py-2 pr-10 border rounded-md shadow-sm
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
            disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
            ${error ? "border-red-300 focus:ring-red-500 focus:border-red-500" : strengthData.meetsMinimum && value ? "border-green-300 focus:ring-green-500 focus:border-green-500" : "border-gray-300"}
          `,
          ...props
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
          lineNumber: 245,
          columnNumber: 9
        },
        globalThis
      ),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          type: "button",
          onClick: togglePasswordVisibility,
          className: "absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",
          tabIndex: -1,
          children: showPassword ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(EyeOff, { className: "h-4 w-4" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
            lineNumber: 275,
            columnNumber: 13
          }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Eye, { className: "h-4 w-4" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
            lineNumber: 277,
            columnNumber: 13
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
          lineNumber: 268,
          columnNumber: 9
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 244,
      columnNumber: 7
    }, globalThis),
    error && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-red-600 flex items-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertTriangle, { className: "h-4 w-4 mr-1" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 285,
        columnNumber: 11
      }, globalThis),
      error
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
      lineNumber: 284,
      columnNumber: 9
    }, globalThis),
    showStrengthMeter && value && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      PasswordStrengthMeter,
      {
        password: value,
        showRequirements,
        showSuggestions,
        compact
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
        lineNumber: 292,
        columnNumber: 9
      },
      globalThis
    )
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/PasswordStrengthMeter.jsx",
    lineNumber: 234,
    columnNumber: 5
  }, globalThis);
};

const FormInput = reactExports.forwardRef(({
  label,
  name,
  type = "text",
  placeholder,
  value = "",
  error,
  touched,
  isValidating = false,
  isValid = false,
  required = false,
  disabled = false,
  className = "",
  inputClassName = "",
  labelClassName = "",
  errorClassName = "",
  successClassName = "",
  helpText,
  showValidationIcons = true,
  showPasswordToggle = false,
  // Password strength meter props
  showPasswordStrength = false,
  showPasswordRequirements = true,
  showPasswordSuggestions = true,
  compactPasswordMeter = false,
  autoComplete,
  maxLength,
  minLength,
  pattern,
  step,
  min,
  max,
  rows = 3,
  cols,
  accept,
  multiple = false,
  onChange,
  onBlur,
  onFocus,
  onClick,
  onKeyDown,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = reactExports.useState(false);
  const [isFocused, setIsFocused] = reactExports.useState(false);
  const inputType = type === "password" && showPassword ? "text" : type;
  const hasError = touched && error;
  const hasSuccess = touched && !error && isValid && value;
  const isLoading = isValidating;
  const baseInputClasses = `
    w-full px-3 py-2 border rounded-md shadow-sm
    focus:outline-none focus:ring-2 focus:ring-offset-0
    transition-colors duration-200
    ${disabled ? "bg-gray-50 cursor-not-allowed" : "bg-white"}
  `;
  const validationClasses = hasError ? "border-red-500 focus:border-red-500 focus:ring-red-500" : hasSuccess ? "border-green-500 focus:border-green-500 focus:ring-green-500" : isFocused ? "border-blue-500 focus:border-blue-500 focus:ring-blue-500" : "border-gray-300 focus:border-blue-500 focus:ring-blue-500";
  const inputClasses = `${baseInputClasses} ${validationClasses} ${inputClassName}`;
  const handleFocus = (e) => {
    setIsFocused(true);
    onFocus?.(e);
  };
  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur?.(e);
  };
  const renderValidationIcon = () => {
    if (!showValidationIcons) {
      return null;
    }
    if (isLoading) {
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Loader2, { className: "h-4 w-4 text-gray-400 animate-spin" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 99,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 98,
        columnNumber: 9
      }, globalThis);
    }
    if (hasError) {
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 text-red-500" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 107,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 106,
        columnNumber: 9
      }, globalThis);
    }
    if (hasSuccess) {
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4 text-green-500" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 115,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 114,
        columnNumber: 9
      }, globalThis);
    }
    return null;
  };
  const renderPasswordToggle = () => {
    if (!showPasswordToggle || type !== "password") {
      return null;
    }
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      "button",
      {
        type: "button",
        className: "absolute inset-y-0 right-0 pr-3 flex items-center",
        onClick: () => setShowPassword(!showPassword),
        tabIndex: -1,
        "data-testid": "password-toggle",
        children: showPassword ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(EyeOff, { className: "h-4 w-4 text-gray-400 hover:text-gray-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
          lineNumber: 136,
          columnNumber: 11
        }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Eye, { className: "h-4 w-4 text-gray-400 hover:text-gray-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
          lineNumber: 138,
          columnNumber: 11
        }, globalThis)
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 128,
        columnNumber: 7
      },
      globalThis
    );
  };
  const renderInput = () => {
    const commonProps = {
      ref,
      id: name,
      name,
      value,
      placeholder,
      disabled,
      required,
      autoComplete,
      maxLength,
      minLength,
      pattern,
      onChange,
      onFocus: handleFocus,
      onBlur: handleBlur,
      onClick,
      onKeyDown,
      "aria-invalid": hasError,
      "aria-describedby": `${name}-help ${hasError ? `${name}-error` : ""}`.trim(),
      "data-testid": `${name}-input`,
      ...props
    };
    switch (type) {
      case "textarea":
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "textarea",
          {
            ...commonProps,
            rows,
            cols,
            className: inputClasses
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
            lineNumber: 172,
            columnNumber: 11
          },
          globalThis
        );
      case "select":
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "select",
          {
            ...commonProps,
            className: inputClasses,
            children: props.children
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
            lineNumber: 182,
            columnNumber: 11
          },
          globalThis
        );
      case "file":
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            ...commonProps,
            type: "file",
            accept,
            multiple,
            className: `${inputClasses} file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100`
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
            lineNumber: 192,
            columnNumber: 11
          },
          globalThis
        );
      case "number":
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            ...commonProps,
            type: "number",
            step,
            min,
            max,
            className: inputClasses
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
            lineNumber: 203,
            columnNumber: 11
          },
          globalThis
        );
      case "date":
      case "datetime-local":
      case "time":
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            ...commonProps,
            type,
            min,
            max,
            className: inputClasses
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
            lineNumber: 217,
            columnNumber: 11
          },
          globalThis
        );
      case "checkbox":
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              ...commonProps,
              type: "checkbox",
              checked: value,
              className: `h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${hasError ? "border-red-500" : ""}`
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
              lineNumber: 229,
              columnNumber: 13
            },
            globalThis
          ),
          label && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: name, className: `ml-2 block text-sm ${labelClassName}`, children: [
            label,
            required && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-red-500 ml-1", children: "*" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
              lineNumber: 240,
              columnNumber: 30
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
            lineNumber: 238,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
          lineNumber: 228,
          columnNumber: 11
        }, globalThis);
      case "radio":
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              ...commonProps,
              type: "radio",
              className: `h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 ${hasError ? "border-red-500" : ""}`
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
              lineNumber: 249,
              columnNumber: 13
            },
            globalThis
          ),
          label && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: name, className: `ml-2 block text-sm ${labelClassName}`, children: [
            label,
            required && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-red-500 ml-1", children: "*" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
              lineNumber: 259,
              columnNumber: 30
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
            lineNumber: 257,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
          lineNumber: 248,
          columnNumber: 11
        }, globalThis);
      default:
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            ...commonProps,
            type: inputType,
            className: inputClasses
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
            lineNumber: 267,
            columnNumber: 11
          },
          globalThis
        );
    }
  };
  const shouldRenderLabel = type !== "checkbox" && type !== "radio" && label;
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `space-y-1 ${className}`, children: [
    shouldRenderLabel && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: name, className: `block text-sm font-medium text-gray-700 ${labelClassName}`, children: [
      label,
      required && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-red-500 ml-1", children: "*" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 284,
        columnNumber: 24
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
      lineNumber: 282,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
      renderInput(),
      showPasswordToggle && type === "password" ? renderPasswordToggle() : renderValidationIcon()
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
      lineNumber: 288,
      columnNumber: 7
    }, globalThis),
    helpText && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { id: `${name}-help`, className: "text-sm text-gray-500", children: helpText }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
      lineNumber: 297,
      columnNumber: 9
    }, globalThis),
    hasError && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      "p",
      {
        id: `${name}-error`,
        className: `text-sm text-red-600 flex items-center ${errorClassName}`,
        role: "alert",
        children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1 flex-shrink-0" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
            lineNumber: 309,
            columnNumber: 11
          }, globalThis),
          error
        ]
      },
      void 0,
      true,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 304,
        columnNumber: 9
      },
      globalThis
    ),
    hasSuccess && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: `text-sm text-green-600 flex items-center ${successClassName}`, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4 mr-1 flex-shrink-0" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 317,
        columnNumber: 11
      }, globalThis),
      "Valid"
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
      lineNumber: 316,
      columnNumber: 9
    }, globalThis),
    showPasswordStrength && type === "password" && value && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      PasswordStrengthMeter,
      {
        password: value,
        showRequirements: showPasswordRequirements,
        showSuggestions: showPasswordSuggestions,
        compact: compactPasswordMeter
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
        lineNumber: 325,
        columnNumber: 11
      },
      globalThis
    ) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
      lineNumber: 324,
      columnNumber: 9
    }, globalThis),
    maxLength && (type === "text" || type === "textarea") && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-500 text-right", children: [
      value.length,
      "/",
      maxLength
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
      lineNumber: 336,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/FormInput.jsx",
    lineNumber: 280,
    columnNumber: 5
  }, globalThis);
});
FormInput.displayName = "FormInput";

const ConfirmationDialog = ({
  isOpen = false,
  onClose,
  onConfirm,
  title = "Confirm Action",
  message = "Are you sure you want to proceed?",
  confirmText = "Confirm",
  cancelText = "Cancel",
  type = "warning",
  // 'warning', 'danger', 'info'
  requireTyping = false,
  typingText = "",
  consequences = [],
  loading = false,
  disabled = false,
  autoFocus = true,
  className = ""
}) => {
  const [typedText, setTypedText] = reactExports.useState("");
  const [isTypingValid, setIsTypingValid] = reactExports.useState(false);
  const inputRef = reactExports.useRef(null);
  const confirmButtonRef = reactExports.useRef(null);
  reactExports.useEffect(() => {
    if (isOpen) {
      setTypedText("");
      setIsTypingValid(false);
      if (autoFocus) {
        setTimeout(() => {
          if (requireTyping && inputRef.current) {
            inputRef.current.focus();
          } else if (confirmButtonRef.current) {
            confirmButtonRef.current.focus();
          }
        }, 100);
      }
    }
  }, [isOpen, requireTyping, autoFocus]);
  reactExports.useEffect(() => {
    if (requireTyping && typingText) {
      setIsTypingValid(typedText.trim().toLowerCase() === typingText.toLowerCase());
    } else {
      setIsTypingValid(true);
    }
  }, [typedText, typingText, requireTyping]);
  reactExports.useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isOpen) {
        return;
      }
      if (event.key === "Escape") {
        onClose();
      } else if (event.key === "Enter" && !requireTyping && isTypingValid && !loading && !disabled) {
        onConfirm();
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, onClose, onConfirm, requireTyping, isTypingValid, loading, disabled]);
  if (!isOpen) {
    return null;
  }
  const typeConfig = {
    warning: {
      icon: AlertTriangle,
      iconColor: "text-yellow-600",
      bgColor: "bg-yellow-50",
      borderColor: "border-yellow-200",
      buttonColor: "bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500"
    },
    danger: {
      icon: AlertTriangle,
      iconColor: "text-red-600",
      bgColor: "bg-red-50",
      borderColor: "border-red-200",
      buttonColor: "bg-red-600 hover:bg-red-700 focus:ring-red-500"
    },
    info: {
      icon: Shield,
      iconColor: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      buttonColor: "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500"
    }
  };
  const config = typeConfig[type] || typeConfig.warning;
  const Icon = config.icon;
  const canConfirm = isTypingValid && !loading && !disabled;
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `bg-white rounded-lg shadow-xl max-w-md w-full ${className}`, children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between p-6 border-b border-gray-200", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `p-2 rounded-full ${config.bgColor} ${config.borderColor} border`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Icon, { className: `h-6 w-6 ${config.iconColor}` }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 110,
          columnNumber: 15
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 109,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900", children: title }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 112,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
        lineNumber: 108,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: onClose,
          className: "text-gray-400 hover:text-gray-600 transition-colors",
          disabled: loading,
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-5 w-5" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
            lineNumber: 119,
            columnNumber: 13
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 114,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
      lineNumber: 107,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-700 mb-4", children: message }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
        lineNumber: 125,
        columnNumber: 11
      }, globalThis),
      consequences.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `p-4 rounded-md ${config.bgColor} ${config.borderColor} border mb-4`, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-gray-900 mb-2", children: "This action will:" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 130,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-gray-700 space-y-1", children: consequences.map((consequence, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: "flex items-start", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-400 mr-2", children: "•" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
            lineNumber: 134,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: consequence }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
            lineNumber: 135,
            columnNumber: 21
          }, globalThis)
        ] }, index, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 133,
          columnNumber: 19
        }, globalThis)) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 131,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
        lineNumber: 129,
        columnNumber: 13
      }, globalThis),
      requireTyping && typingText && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: [
          "Type ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-mono bg-gray-100 px-1 rounded", children: typingText }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
            lineNumber: 146,
            columnNumber: 22
          }, globalThis),
          " to confirm:"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 145,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            ref: inputRef,
            type: "text",
            value: typedText,
            onChange: (e) => setTypedText(e.target.value),
            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${typedText && !isTypingValid ? "border-red-300 focus:ring-red-500" : "border-gray-300 focus:ring-blue-500"}`,
            placeholder: `Type "${typingText}" here`,
            disabled: loading
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
            lineNumber: 148,
            columnNumber: 15
          },
          globalThis
        ),
        typedText && !isTypingValid && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600", children: [
          "Text doesn't match. Please type exactly: ",
          typingText
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 162,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
        lineNumber: 144,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
      lineNumber: 124,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50 rounded-b-lg", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: onClose,
          className: "px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",
          disabled: loading,
          children: cancelText
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 172,
          columnNumber: 11
        },
        globalThis
      ),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          ref: confirmButtonRef,
          onClick: onConfirm,
          disabled: !canConfirm,
          className: `px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors inline-flex items-center ${canConfirm ? config.buttonColor : "bg-gray-300 cursor-not-allowed"}`,
          children: [
            loading && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RefreshCw, { className: "h-4 w-4 mr-2 animate-spin" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
              lineNumber: 189,
              columnNumber: 25
            }, globalThis),
            confirmText
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
          lineNumber: 179,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
      lineNumber: 171,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
    lineNumber: 105,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/ConfirmationDialog.jsx",
    lineNumber: 104,
    columnNumber: 5
  }, globalThis);
};

const LoadingSpinner = ({
  size = "md",
  color = "blue",
  text = null,
  className = ""
}) => {
  const sizes = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
    xl: "h-12 w-12"
  };
  const colors = {
    blue: "text-blue-600",
    green: "text-green-600",
    yellow: "text-yellow-600",
    red: "text-red-600",
    purple: "text-purple-600",
    gray: "text-gray-600",
    white: "text-white"
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `flex items-center justify-center ${className}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Loader2, { className: `${sizes[size]} ${colors[color]} animate-spin` }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/LoadingStates.jsx",
      lineNumber: 33,
      columnNumber: 9
    }, globalThis),
    text && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm font-medium text-gray-700", children: text }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/LoadingStates.jsx",
      lineNumber: 34,
      columnNumber: 18
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/LoadingStates.jsx",
    lineNumber: 32,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/LoadingStates.jsx",
    lineNumber: 31,
    columnNumber: 5
  }, globalThis);
};

const BackupManager = () => {
  const { showMessage } = useMessage();
  const electronAPI = getElectronAPI();
  const [backups, setBackups] = reactExports.useState([]);
  const [loading, setLoading] = reactExports.useState(false);
  const [creating, setCreating] = reactExports.useState(false);
  const [restoring, setRestoring] = reactExports.useState(false);
  const [createForm, setCreateForm] = reactExports.useState({
    name: "",
    description: "",
    password: "",
    location: ""
  });
  const [restoreDialog, setRestoreDialog] = reactExports.useState({
    open: false,
    backup: null,
    password: "",
    mergeMode: false
  });
  const [deleteDialog, setDeleteDialog] = reactExports.useState({
    open: false,
    backup: null
  });
  reactExports.useEffect(() => {
    loadBackups();
  }, []);
  const loadBackups = async () => {
    try {
      setLoading(true);
      const result = await electronAPI.backup.list();
      if (result.success) {
        setBackups(result.data);
      } else {
        showMessage("error", `Failed to load backups: ${result.error}`);
      }
    } catch (error) {
      console.error("Failed to load backups:", error);
      showMessage("error", "Failed to load backup list");
    } finally {
      setLoading(false);
    }
  };
  const handleCreateBackup = async () => {
    try {
      setCreating(true);
      const backupOptions = {
        name: createForm.name || `Backup_${( new Date()).toISOString().split("T")[0]}`,
        description: createForm.description,
        password: createForm.password,
        location: createForm.location
      };
      const result = await electronAPI.backup.create(backupOptions);
      if (result.success) {
        showMessage("success", "Backup created successfully");
        setCreateForm({ name: "", description: "", password: "", location: "" });
        await loadBackups();
      } else {
        showMessage("error", `Failed to create backup: ${result.error}`);
      }
    } catch (error) {
      console.error("Failed to create backup:", error);
      showMessage("error", "Failed to create backup");
    } finally {
      setCreating(false);
    }
  };
  const handleRestoreBackup = async () => {
    try {
      setRestoring(true);
      const restoreOptions = {
        backupPath: restoreDialog.backup.path,
        password: restoreDialog.password,
        mergeMode: restoreDialog.mergeMode
      };
      const result = await electronAPI.backup.restore(restoreOptions);
      if (result.success) {
        showMessage("success", "Backup restored successfully. Application will restart.");
        setRestoreDialog({ open: false, backup: null, password: "", mergeMode: false });
        setTimeout(() => {
          window.location.reload();
        }, 2e3);
      } else {
        showMessage("error", `Failed to restore backup: ${result.error}`);
      }
    } catch (error) {
      console.error("Failed to restore backup:", error);
      showMessage("error", "Failed to restore backup");
    } finally {
      setRestoring(false);
    }
  };
  const handleDeleteBackup = async () => {
    try {
      const result = await electronAPI.backup.delete(deleteDialog.backup.path);
      if (result.success) {
        showMessage("success", "Backup deleted successfully");
        setDeleteDialog({ open: false, backup: null });
        await loadBackups();
      } else {
        showMessage("error", `Failed to delete backup: ${result.error}`);
      }
    } catch (error) {
      console.error("Failed to delete backup:", error);
      showMessage("error", "Failed to delete backup");
    }
  };
  const handleVerifyBackup = async (backup) => {
    try {
      const result = await electronAPI.backup.verify(backup.path);
      if (result.success) {
        if (result.data.valid) {
          showMessage("success", "Backup file is valid and intact");
        } else {
          showMessage("error", "Backup file is corrupted or invalid");
        }
      } else {
        showMessage("error", `Failed to verify backup: ${result.error}`);
      }
    } catch (error) {
      console.error("Failed to verify backup:", error);
      showMessage("error", "Failed to verify backup");
    }
  };
  const formatFileSize = (bytes) => {
    if (bytes === 0) {
      return "0 Bytes";
    }
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };
  const formatDate = (date) => {
    return new Date(date).toLocaleString();
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-2xl font-bold text-gray-900", children: "Backup Management" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 191,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600 mt-1", children: "Create, restore, and manage your data backups" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 192,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 190,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: loadBackups,
          disabled: loading,
          className: "flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RefreshCw, { className: `h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}` }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 201,
              columnNumber: 11
            }, globalThis),
            "Refresh"
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 196,
          columnNumber: 9
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
      lineNumber: 189,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white rounded-lg border p-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-semibold text-gray-900 mb-4 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Download, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 209,
          columnNumber: 11
        }, globalThis),
        "Create New Backup"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 208,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4 mb-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          FormInput,
          {
            label: "Backup Name",
            name: "name",
            type: "text",
            placeholder: "Enter backup name (optional)",
            value: createForm.name,
            onChange: (e) => setCreateForm((prev) => ({ ...prev, name: e.target.value })),
            helpText: "Leave empty to use automatic naming"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 214,
            columnNumber: 11
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          FormInput,
          {
            label: "Description",
            name: "description",
            type: "text",
            placeholder: "Brief description (optional)",
            value: createForm.description,
            onChange: (e) => setCreateForm((prev) => ({ ...prev, description: e.target.value }))
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 224,
            columnNumber: 11
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 213,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4 mb-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          PasswordInputWithStrength,
          {
            label: "Encryption Password (Optional)",
            name: "password",
            value: createForm.password,
            onChange: (e) => setCreateForm((prev) => ({ ...prev, password: e.target.value })),
            placeholder: "Leave empty for unencrypted backup",
            showStrengthMeter: !!createForm.password,
            showRequirements: false,
            showSuggestions: false,
            compact: true
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 235,
            columnNumber: 11
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          FormInput,
          {
            label: "Backup Location",
            name: "location",
            type: "text",
            placeholder: "Default: Documents/PeerReviewBackups",
            value: createForm.location,
            onChange: (e) => setCreateForm((prev) => ({ ...prev, location: e.target.value })),
            helpText: "Leave empty to use default location"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 247,
            columnNumber: 11
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 234,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: handleCreateBackup,
          disabled: creating,
          className: "flex items-center px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50",
          children: creating ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, { size: "sm", color: "white", className: "mr-2" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 265,
              columnNumber: 15
            }, globalThis),
            "Creating Backup..."
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 264,
            columnNumber: 13
          }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Download, { className: "h-4 w-4 mr-2" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 270,
              columnNumber: 15
            }, globalThis),
            "Create Backup"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 269,
            columnNumber: 13
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 258,
          columnNumber: 9
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
      lineNumber: 207,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white rounded-lg border", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6 border-b border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-semibold text-gray-900 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(HardDrive, { className: "h-5 w-5 mr-2 text-gray-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 281,
          columnNumber: 13
        }, globalThis),
        "Available Backups (",
        backups.length,
        ")"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 280,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 279,
        columnNumber: 9
      }, globalThis),
      loading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-8 text-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, { size: "lg", text: "Loading backups..." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 288,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 287,
        columnNumber: 11
      }, globalThis) : backups.length === 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-8 text-center text-gray-500", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(HardDrive, { className: "h-12 w-12 mx-auto mb-4 text-gray-300" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 292,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "No backups found" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 293,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm", children: "Create your first backup to get started" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 294,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 291,
        columnNumber: 11
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "divide-y divide-gray-200", children: backups.map((backup, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        BackupItem,
        {
          backup,
          onRestore: (backup2) => setRestoreDialog({
            open: true,
            backup: backup2,
            password: "",
            mergeMode: false
          }),
          onDelete: (backup2) => setDeleteDialog({ open: true, backup: backup2 }),
          onVerify: handleVerifyBackup,
          formatFileSize,
          formatDate
        },
        index,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 299,
          columnNumber: 15
        },
        globalThis
      )) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 297,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
      lineNumber: 278,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      ConfirmationDialog,
      {
        isOpen: restoreDialog.open,
        onClose: () => setRestoreDialog({ open: false, backup: null, password: "", mergeMode: false }),
        onConfirm: handleRestoreBackup,
        title: "Restore Backup",
        message: "This will replace all current data with the backup data. This action cannot be undone.",
        confirmText: "Restore",
        cancelText: "Cancel",
        variant: "warning",
        loading: restoring,
        destructive: true,
        showDetails: true,
        details: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-yellow-50 border border-yellow-200 rounded-lg p-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "font-medium text-yellow-800 mb-2", children: "Backup Information" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 334,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-yellow-700 space-y-1", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Name:" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                  lineNumber: 336,
                  columnNumber: 20
                }, globalThis),
                " ",
                restoreDialog.backup?.metadata?.name
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                lineNumber: 336,
                columnNumber: 17
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Created:" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                  lineNumber: 337,
                  columnNumber: 20
                }, globalThis),
                " ",
                restoreDialog.backup && formatDate(restoreDialog.backup.created)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                lineNumber: 337,
                columnNumber: 17
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Size:" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                  lineNumber: 338,
                  columnNumber: 20
                }, globalThis),
                " ",
                restoreDialog.backup && formatFileSize(restoreDialog.backup.size)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                lineNumber: 338,
                columnNumber: 17
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Encrypted:" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                  lineNumber: 339,
                  columnNumber: 20
                }, globalThis),
                " ",
                restoreDialog.backup?.encrypted ? "Yes" : "No"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                lineNumber: 339,
                columnNumber: 17
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 335,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 333,
            columnNumber: 13
          }, globalThis),
          restoreDialog.backup?.encrypted && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            PasswordInputWithStrength,
            {
              label: "Backup Password",
              name: "restorePassword",
              value: restoreDialog.password,
              onChange: (e) => setRestoreDialog((prev) => ({ ...prev, password: e.target.value })),
              placeholder: "Enter backup password",
              required: true,
              showStrengthMeter: false
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 344,
              columnNumber: 15
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "checkbox",
                id: "mergeMode",
                checked: restoreDialog.mergeMode,
                onChange: (e) => setRestoreDialog((prev) => ({ ...prev, mergeMode: e.target.checked })),
                className: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                disabled: true
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                lineNumber: 356,
                columnNumber: 15
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "mergeMode", className: "ml-2 text-sm text-gray-600", children: "Merge with existing data (coming soon)" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 364,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 355,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 332,
          columnNumber: 11
        }, globalThis)
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 319,
        columnNumber: 7
      },
      globalThis
    ),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      ConfirmationDialog,
      {
        isOpen: deleteDialog.open,
        onClose: () => setDeleteDialog({ open: false, backup: null }),
        onConfirm: handleDeleteBackup,
        title: "Delete Backup",
        message: `Are you sure you want to delete the backup "${deleteDialog.backup?.metadata?.name}"? This action cannot be undone.`,
        confirmText: "Delete",
        cancelText: "Cancel",
        variant: "danger",
        destructive: true,
        requireConfirmation: true,
        confirmationText: "DELETE"
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 373,
        columnNumber: 7
      },
      globalThis
    )
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
    lineNumber: 187,
    columnNumber: 5
  }, globalThis);
};
const BackupItem = ({
  backup,
  onRestore,
  onDelete,
  onVerify,
  formatFileSize,
  formatDate
}) => {
  const [expanded, setExpanded] = reactExports.useState(false);
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `w-10 h-10 rounded-full flex items-center justify-center ${backup.encrypted ? "bg-purple-100" : "bg-blue-100"}`, children: backup.encrypted ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Lock, { className: "h-5 w-5 text-purple-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 412,
          columnNumber: 17
        }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Unlock, { className: "h-5 w-5 text-blue-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 414,
          columnNumber: 17
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 408,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "font-medium text-gray-900", children: backup.metadata?.name || backup.name }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 419,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-4 text-sm text-gray-500 mt-1", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "flex items-center", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Calendar, { className: "h-4 w-4 mr-1" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                lineNumber: 424,
                columnNumber: 19
              }, globalThis),
              formatDate(backup.created)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 423,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "flex items-center", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(HardDrive, { className: "h-4 w-4 mr-1" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                lineNumber: 428,
                columnNumber: 19
              }, globalThis),
              formatFileSize(backup.size)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 427,
              columnNumber: 17
            }, globalThis),
            backup.encrypted && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "flex items-center text-purple-600", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Shield, { className: "h-4 w-4 mr-1" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                lineNumber: 433,
                columnNumber: 21
              }, globalThis),
              "Encrypted"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 432,
              columnNumber: 19
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 422,
            columnNumber: 15
          }, globalThis),
          backup.metadata?.description && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-1", children: backup.metadata.description }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 439,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 418,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 407,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 406,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setExpanded(!expanded),
            className: "p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100",
            title: "View details",
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 453,
              columnNumber: 13
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 448,
            columnNumber: 11
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => onVerify(backup),
            className: "p-2 text-blue-600 hover:text-blue-700 rounded-md hover:bg-blue-50",
            title: "Verify backup",
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 461,
              columnNumber: 13
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 456,
            columnNumber: 11
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => onRestore(backup),
            className: "px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Upload, { className: "h-4 w-4 mr-1 inline" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
                lineNumber: 468,
                columnNumber: 13
              }, globalThis),
              "Restore"
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 464,
            columnNumber: 11
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => onDelete(backup),
            className: "p-2 text-red-600 hover:text-red-700 rounded-md hover:bg-red-50",
            title: "Delete backup",
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Trash2, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
              lineNumber: 477,
              columnNumber: 13
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 472,
            columnNumber: 11
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 447,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
      lineNumber: 405,
      columnNumber: 7
    }, globalThis),
    expanded && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 pt-4 border-t border-gray-200", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-700", children: "File Path:" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 486,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600 break-all", children: backup.path }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 487,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 485,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-700", children: "App Version:" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 491,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: backup.metadata?.version || "Unknown" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 492,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 490,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-700", children: "Backup Time:" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 496,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: formatDate(backup.metadata?.timestamp || backup.created) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 497,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 495,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-700", children: "Original Size:" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 501,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: formatFileSize(backup.metadata?.size || 0) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 502,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 500,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-700", children: "Compressed Size:" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 506,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: formatFileSize(backup.size) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 507,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 505,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-700", children: "Compression Ratio:" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 511,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: backup.metadata?.size ? `${Math.round((1 - backup.size / backup.metadata.size) * 100)}%` : "N/A" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
            lineNumber: 512,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 510,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 484,
        columnNumber: 11
      }, globalThis),
      backup.metadata?.checksum && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-700", children: "Checksum (SHA-256):" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 523,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-600 font-mono break-all mt-1", children: backup.metadata.checksum }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
          lineNumber: 524,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
        lineNumber: 522,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
      lineNumber: 483,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/BackupManager.jsx",
    lineNumber: 404,
    columnNumber: 5
  }, globalThis);
};

const Settings = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { showMessage } = useMessage();
  const [activeTab, setActiveTab] = reactExports.useState("preferences");
  const [userSettings, setUserSettings] = reactExports.useState({});
  const [loading, setLoading] = reactExports.useState(false);
  const [saving, setSaving] = reactExports.useState(false);
  reactExports.useEffect(() => {
    loadUserSettings();
  }, []);
  const loadUserSettings = async () => {
    try {
      setLoading(true);
      const electronAPI = window.electronAPI;
      const settings = await electronAPI.settings.getUserSettings(user.id);
      setUserSettings({
        theme: settings.theme || "light",
        notifications: settings.notifications || "enabled",
        language: settings.language || "en",
        autoSave: settings.autoSave || "enabled",
        ...settings
      });
    } catch (error) {
      showMessage("error", `Failed to load settings: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  const saveUserSetting = async (key, value) => {
    try {
      setSaving(true);
      const electronAPI = window.electronAPI;
      await electronAPI.settings.updateUserSetting(user.id, key, value);
      setUserSettings((prev) => ({ ...prev, [key]: value }));
      showMessage("success", "Setting saved successfully");
    } catch (error) {
      showMessage("error", `Failed to save setting: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };
  const handleLogout = () => {
    if (window.confirm("Are you sure you want to logout?")) {
      logout();
      navigate("/login");
    }
  };
  const tabs = [
    { id: "preferences", label: "Preferences", icon: User },
    { id: "backup", label: "Data Backup", icon: HardDrive },
    { id: "help", label: "Help & Guide", icon: HelpCircle },
    { id: "about", label: "About", icon: Info }
  ];
  const roleBasedContent = {
    super_admin: {
      title: "Super Administrator",
      description: "Complete system access with assessment creation and management capabilities",
      features: [
        "Create and manage assessments",
        "Manage all users and institutions",
        "Access comprehensive analytics",
        "System configuration and settings"
      ]
    },
    admin: {
      title: "Administrator",
      description: "Institution-wide management and oversight capabilities",
      features: [
        "Manage institution users",
        "Oversee assessment activities",
        "Access institution analytics",
        "Configure institution settings"
      ]
    },
    teacher: {
      title: "Teacher",
      description: "Assessment and student management capabilities",
      features: [
        "Create and manage assessments",
        "Manage student batches",
        "Upload student responses",
        "View detailed statistics and analytics"
      ]
    },
    student: {
      title: "Student",
      description: "Assessment participation and progress tracking",
      features: [
        "Take assigned assessments",
        "View assessment history",
        "Track progress and scores",
        "Access peer review results"
      ]
    }
  };
  const renderPreferences = () => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "User Preferences" }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 135,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4 mb-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Palette, { className: "h-5 w-5 text-gray-400 mr-3" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 141,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-gray-900", children: "Theme" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 143,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-500", children: "Choose your preferred theme" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 144,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 142,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
        lineNumber: 140,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "select",
        {
          value: userSettings.theme || "light",
          onChange: (e) => saveUserSetting("theme", e.target.value),
          className: "form-select",
          disabled: saving,
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "light", children: "Light" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 153,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "dark", children: "Dark" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 154,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "auto", children: "Auto" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 155,
              columnNumber: 15
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 147,
          columnNumber: 13
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 139,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 138,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4 mb-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Bell, { className: "h-5 w-5 text-gray-400 mr-3" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 164,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-gray-900", children: "Notifications" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 166,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-500", children: "Manage notification preferences" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 167,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 165,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
        lineNumber: 163,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "select",
        {
          value: userSettings.notifications || "enabled",
          onChange: (e) => saveUserSetting("notifications", e.target.value),
          className: "form-select",
          disabled: saving,
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "enabled", children: "Enabled" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 176,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "disabled", children: "Disabled" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 177,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "important", children: "Important Only" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 178,
              columnNumber: 15
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 170,
          columnNumber: 13
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 162,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 161,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Save, { className: "h-5 w-5 text-gray-400 mr-3" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 187,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-gray-900", children: "Auto-save" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 189,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-500", children: "Automatically save your work" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 190,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 188,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
        lineNumber: 186,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "select",
        {
          value: userSettings.autoSave || "enabled",
          onChange: (e) => saveUserSetting("autoSave", e.target.value),
          className: "form-select",
          disabled: saving,
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "enabled", children: "Enabled" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 199,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "disabled", children: "Disabled" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 200,
              columnNumber: 15
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 193,
          columnNumber: 13
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 185,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 184,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
    lineNumber: 134,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
    lineNumber: 133,
    columnNumber: 5
  }, globalThis);
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-h-screen bg-gray-50", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center py-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => navigate(-1),
            className: "btn-outline mr-4 flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowLeft, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 219,
                columnNumber: 17
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Go Back" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 220,
                columnNumber: 17
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 215,
            columnNumber: 15
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Settings$1, { className: "h-8 w-8 text-blue-600 mr-3" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 223,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-2xl font-bold text-gray-900", children: "Settings" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 225,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Manage your preferences and view help documentation" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 226,
              columnNumber: 19
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 224,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 222,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
        lineNumber: 214,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: handleLogout,
          className: "btn-outline text-red-600 border-red-600 hover:bg-red-50 flex items-center space-x-2",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LogOut, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 235,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Logout" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 236,
              columnNumber: 15
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 231,
          columnNumber: 13
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 213,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 212,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 211,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex flex-col lg:flex-row gap-8", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "lg:w-64", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("nav", { className: "space-y-1", children: tabs.map((tab) => {
        const Icon = tab.icon;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setActiveTab(tab.id),
            className: `w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${activeTab === tab.id ? "bg-blue-100 text-blue-700" : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Icon, { className: "h-5 w-5 mr-3" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 260,
                columnNumber: 21
              }, globalThis),
              tab.label
            ]
          },
          tab.id,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 251,
            columnNumber: 19
          },
          globalThis
        );
      }) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
        lineNumber: 247,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
        lineNumber: 246,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: loading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-12", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 272,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-4 text-gray-600", children: "Loading settings..." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 273,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
        lineNumber: 271,
        columnNumber: 15
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
        activeTab === "preferences" && renderPreferences(),
        activeTab === "backup" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BackupManager, {}, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 278,
          columnNumber: 44
        }, globalThis),
        activeTab === "help" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "Help & Documentation" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 282,
            columnNumber: 23
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-6 mb-6", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center mb-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Shield, { className: "h-6 w-6 text-blue-600 mr-3" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 287,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900", children: roleBasedContent[user.role]?.title }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 289,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: roleBasedContent[user.role]?.description }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 290,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 288,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 286,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900 mb-2", children: "Available Features:" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 295,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "space-y-1", children: roleBasedContent[user.role]?.features.map((feature, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: "flex items-center text-sm text-gray-600", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4 text-green-500 mr-2" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 299,
                  columnNumber: 33
                }, globalThis),
                feature
              ] }, index, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 298,
                columnNumber: 31
              }, globalThis)) }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 296,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 294,
              columnNumber: 25
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 285,
            columnNumber: 23
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4 mb-6", children: [
            user.role === "teacher" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center mb-3", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Book, { className: "h-5 w-5 text-blue-600 mr-2" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 313,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "font-medium text-gray-900", children: "Assessment Creation" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 314,
                    columnNumber: 33
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 312,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mb-3", children: "Learn how to create and manage assessments" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 316,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-xs text-gray-500 space-y-1", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Create new assessments with questions" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 318,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Publish and assign to batches" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 319,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Monitor student progress" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 320,
                    columnNumber: 33
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 317,
                  columnNumber: 31
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 311,
                columnNumber: 29
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center mb-3", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-5 w-5 text-green-600 mr-2" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 326,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "font-medium text-gray-900", children: "Student Management" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 327,
                    columnNumber: 33
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 325,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mb-3", children: "Manage students and track their progress" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 329,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-xs text-gray-500 space-y-1", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Upload student responses" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 331,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Download assessment forms" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 332,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• View detailed statistics" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 333,
                    columnNumber: 33
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 330,
                  columnNumber: 31
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 324,
                columnNumber: 29
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 310,
              columnNumber: 27
            }, globalThis),
            (user.role === "admin" || user.role === "super_admin") && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center mb-3", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Shield, { className: "h-5 w-5 text-purple-600 mr-2" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 343,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "font-medium text-gray-900", children: "User Management" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 344,
                    columnNumber: 33
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 342,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mb-3", children: "Manage users and permissions" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 346,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-xs text-gray-500 space-y-1", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Create and manage user accounts" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 348,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Assign roles and permissions" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 349,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Monitor user activity" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 350,
                    columnNumber: 33
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 347,
                  columnNumber: 31
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 341,
                columnNumber: 29
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center mb-3", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BarChart3, { className: "h-5 w-5 text-orange-600 mr-2" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 356,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "font-medium text-gray-900", children: "Analytics & Reports" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 357,
                    columnNumber: 33
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 355,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mb-3", children: "Access comprehensive analytics" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 359,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-xs text-gray-500 space-y-1", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• View institution-wide statistics" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 361,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Generate detailed reports" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 362,
                    columnNumber: 33
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Track system usage" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 363,
                    columnNumber: 33
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 360,
                  columnNumber: 31
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 354,
                columnNumber: 29
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 340,
              columnNumber: 27
            }, globalThis),
            user.role === "student" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center mb-3", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(GraduationCap, { className: "h-5 w-5 text-blue-600 mr-2" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 372,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "font-medium text-gray-900", children: "Taking Assessments" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 373,
                  columnNumber: 31
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 371,
                columnNumber: 29
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mb-3", children: "How to participate in assessments" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 375,
                columnNumber: 29
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-xs text-gray-500 space-y-1", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Access assigned assessments" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 377,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Submit responses" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 378,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• View results and feedback" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 379,
                  columnNumber: 31
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 376,
                columnNumber: 29
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 370,
              columnNumber: 27
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 308,
            columnNumber: 23
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-6", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center mb-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertTriangle, { className: "h-5 w-5 text-yellow-600 mr-2" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 388,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "font-medium text-gray-900", children: "Common Issues & Solutions" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 389,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 387,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900", children: "Cannot access assessments" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 394,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Ensure you are assigned to the correct batch and the assessment is published." }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 395,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 393,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900", children: "Upload issues" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 399,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Check your internet connection and ensure all required fields are completed." }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 400,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 398,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900", children: "Statistics not loading" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 404,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Refresh the page or contact your administrator if the issue persists." }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 405,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 403,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 392,
              columnNumber: 25
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 386,
            columnNumber: 23
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 281,
          columnNumber: 21
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 280,
          columnNumber: 19
        }, globalThis),
        activeTab === "about" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "Application Information" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 415,
            columnNumber: 23
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-6 mb-6", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center mb-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Info, { className: "h-6 w-6 text-blue-600 mr-3" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 419,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900", children: "Peer Review Assessment System" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 421,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Version 1.0.0" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 422,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-500 mt-1", children: "© 2024 Ajinkyacreatiion PVT. LTD." }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 423,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 420,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 418,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4 mt-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900 mb-2", children: "System Information" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 429,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-gray-600 space-y-1", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
                    "Build Date: ",
                    (/* @__PURE__ */ new Date()).toLocaleDateString()
                  ] }, void 0, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 431,
                    columnNumber: 31
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "Platform: Electron + React" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 432,
                    columnNumber: 31
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "Database: SQLite" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 433,
                    columnNumber: 31
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "Charts: Chart.js" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 434,
                    columnNumber: 31
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 430,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 428,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900 mb-2", children: "System Requirements" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 439,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-gray-600 space-y-1", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "Windows 10 or later" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 441,
                    columnNumber: 31
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "4GB RAM minimum" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 442,
                    columnNumber: 31
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "500MB disk space" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 443,
                    columnNumber: 31
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "Internet connection (optional)" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 444,
                    columnNumber: 31
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 440,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 438,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 427,
              columnNumber: 25
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 417,
            columnNumber: 23
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-6 mb-6", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-4", children: "Author" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 452,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "border-l-4 border-blue-500 pl-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900", children: "Maj. Sachin Kumar Singh" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 454,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Project Author & System Architect" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 455,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 453,
              columnNumber: 25
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 451,
            columnNumber: 23
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-6 mb-6", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-4", children: "Developer" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 461,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "border-l-4 border-green-500 pl-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900", children: "Hrishikesh Mohite" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 463,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Lead Developer & Implementation Specialist" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 464,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-600 space-y-1", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("a", { href: "mailto:<EMAIL>", className: "text-blue-600 hover:text-blue-800", children: "<EMAIL>" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 467,
                  columnNumber: 31
                }, globalThis) }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 466,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "a",
                  {
                    href: "https://www.hrishikeshmohite.com",
                    target: "_blank",
                    rel: "noopener noreferrer",
                    className: "text-blue-600 hover:text-blue-800",
                    children: "www.hrishikeshmohite.com"
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 470,
                    columnNumber: 31
                  },
                  globalThis
                ) }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 469,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 465,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 462,
              columnNumber: 25
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 460,
            columnNumber: 23
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-6 mb-6", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-4", children: "Company" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 485,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-3", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900", children: "Ajinkyacreatiion PVT. LTD." }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 488,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Software Development & Technology Solutions" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 489,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "a",
                  {
                    href: "https://www.ajinkyacreatiion.com",
                    target: "_blank",
                    rel: "noopener noreferrer",
                    className: "text-sm text-blue-600 hover:text-blue-800",
                    children: "www.ajinkyacreatiion.com"
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                    lineNumber: 490,
                    columnNumber: 29
                  },
                  globalThis
                )
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 487,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: [
                "Support: ",
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("a", { href: "mailto:<EMAIL>", className: "text-blue-600 hover:text-blue-800", children: "<EMAIL>" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 501,
                  columnNumber: 40
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 500,
                columnNumber: 29
              }, globalThis) }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 499,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 486,
              columnNumber: 25
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 484,
            columnNumber: 23
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border rounded-lg p-6", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between mb-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Download, { className: "h-5 w-5 text-green-600 mr-2" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 511,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "font-medium text-gray-900", children: "Updates" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 512,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 510,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: "btn-outline flex items-center space-x-2", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RefreshCw, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 515,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Check for Updates" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                  lineNumber: 516,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 514,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 509,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center text-sm text-green-600", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4 mr-2" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 521,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "You are running the latest version" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
                lineNumber: 522,
                columnNumber: 27
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
              lineNumber: 520,
              columnNumber: 25
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
            lineNumber: 508,
            columnNumber: 23
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 414,
          columnNumber: 21
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
          lineNumber: 413,
          columnNumber: 19
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
        lineNumber: 276,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
        lineNumber: 269,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 244,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
      lineNumber: 243,
      columnNumber: 7
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Settings.jsx",
    lineNumber: 209,
    columnNumber: 5
  }, globalThis);
};

export { Settings as default };
