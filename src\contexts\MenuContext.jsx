/**
 * Menu Context for Peer Review System
 *
 * Handles menu interactions, keyboard shortcuts, and menu-triggered actions
 * Integrates with Electron's native menu system
 *
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <hrishi<PERSON><EMAIL>>
 * Company: Ajinkyacreatiion PVT. LTD.
 */

import React, { createContext, useContext, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from './AuthContext';
import { useMessage } from './MessageContext';

const MenuContext = createContext();

// eslint-disable-next-line react-refresh/only-export-components
export const useMenu = () => {
  const context = useContext(MenuContext);
  if (!context) {
    throw new Error('useMenu must be used within a MenuProvider');
  }
  return context;
};

export const MenuProvider = ({ children }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showMessage } = useMessage();

  // Update menu context when user changes
  useEffect(() => {
    if (window.electronAPI && user) {
      window.electronAPI.invoke('menu:setUserContext', user);
    }
  }, [user]);

  // Handle menu navigation
  const handleMenuNavigation = useCallback((path) => {
    navigate(path);
  }, [navigate]);

  // Handle menu actions
  const handleMenuAction = useCallback((action) => {
    switch (action) {
      case 'save-assessment':
        // Trigger save action in current component
        window.dispatchEvent(new CustomEvent('menu:save-assessment'));
        break;
      case 'show-search':
        // Show search dialog
        window.dispatchEvent(new CustomEvent('menu:show-search'));
        break;
      default:
        console.log('Unhandled menu action:', action);
    }
  }, []);

  // Handle export data
  const handleExportData = useCallback(async ({ type, filePath }) => {
    try {
      showMessage('Exporting data...', 'info');

      // Implement export logic based on type
      switch (type) {
        case 'assessment-results':
          // Export assessment results
          await exportAssessmentResults(filePath);
          break;
        case 'student-data':
          // Export student data
          await exportStudentData(filePath);
          break;
        case 'reports':
          // Export reports
          await exportReports(filePath);
          break;
        default:
          throw new Error(`Unknown export type: ${type}`);
      }

      showMessage('Data exported successfully!', 'success');
    } catch (error) {
      showMessage(`Export failed: ${error.message}`, 'error');
    }
  }, [showMessage]);

  // Handle import data
  const handleImportData = useCallback(async ({ type, filePath }) => {
    try {
      showMessage('Importing data...', 'info');

      // Implement import logic based on type
      switch (type) {
        case 'student-data':
          // Import student data
          await importStudentData(filePath);
          break;
        case 'assessment-data':
          // Import assessment data
          await importAssessmentData(filePath);
          break;
        default:
          throw new Error(`Unknown import type: ${type}`);
      }

      showMessage('Data imported successfully!', 'success');
    } catch (error) {
      showMessage(`Import failed: ${error.message}`, 'error');
    }
  }, [showMessage]);

  // Handle backup operations
  const handleCreateBackup = useCallback(async ({ filePath }) => {
    try {
      showMessage('Creating backup...', 'info');

      // Create backup
      const result = await window.electronAPI.invoke('backup:create', { filePath });

      if (result.success) {
        showMessage('Backup created successfully!', 'success');
      } else {
        throw new Error(result.error || 'Backup creation failed');
      }
    } catch (error) {
      showMessage(`Backup failed: ${error.message}`, 'error');
    }
  }, [showMessage]);

  const handleRestoreBackup = useCallback(async ({ filePath }) => {
    try {
      showMessage('Restoring backup...', 'info');

      // Restore backup
      const result = await window.electronAPI.invoke('backup:restore', { filePath });

      if (result.success) {
        showMessage('Backup restored successfully! Please restart the application.', 'success');
      } else {
        throw new Error(result.error || 'Backup restoration failed');
      }
    } catch (error) {
      showMessage(`Restore failed: ${error.message}`, 'error');
    }
  }, [showMessage]);

  // Handle tutorial display
  const handleShowTutorial = useCallback(() => {
    navigate('/tutorial');
  }, [navigate]);

  // Handle keyboard shortcuts display
  const handleShowShortcuts = useCallback((shortcuts) => {
    // Show shortcuts modal
    window.dispatchEvent(new CustomEvent('menu:show-shortcuts', { detail: shortcuts }));
  }, []);

  // Handle check for updates (offline application - no updates)
  const handleCheckUpdates = useCallback(async () => {
    try {
      const version = await window.electronAPI.updater.getVersion();
      showMessage(`Current version: ${version}. This is an offline application with no automatic updates.`, 'info');
    } catch (error) {
      showMessage(`Version check failed: ${error.message}`, 'error');
    }
  }, [showMessage]);

  // Handle about dialog
  const handleShowAbout = useCallback((aboutInfo) => {
    // Show about modal
    window.dispatchEvent(new CustomEvent('menu:show-about', { detail: aboutInfo }));
  }, []);

  // Set up IPC listeners for menu events
  useEffect(() => {
    if (!window.electronAPI) {return;}

    const handleMenuNavigate = (_, path) => handleMenuNavigation(path);
    const handleMenuActionEvent = (_, action) => handleMenuAction(action);
    const handleExportDataEvent = (_, data) => handleExportData(data);
    const handleImportDataEvent = (_, data) => handleImportData(data);
    const handleCreateBackupEvent = (_, data) => handleCreateBackup(data);
    const handleRestoreBackupEvent = (_, data) => handleRestoreBackup(data);
    const handleShowTutorialEvent = () => handleShowTutorial();
    const handleShowShortcutsEvent = (_, shortcuts) => handleShowShortcuts(shortcuts);
    const handleCheckUpdatesEvent = () => handleCheckUpdates();
    const handleShowAboutEvent = (_, aboutInfo) => handleShowAbout(aboutInfo);

    // Register IPC listeners
    window.electronAPI.on('menu:navigate', handleMenuNavigate);
    window.electronAPI.on('menu:action', handleMenuActionEvent);
    window.electronAPI.on('menu:export-data', handleExportDataEvent);
    window.electronAPI.on('menu:import-data', handleImportDataEvent);
    window.electronAPI.on('menu:create-backup', handleCreateBackupEvent);
    window.electronAPI.on('menu:restore-backup', handleRestoreBackupEvent);
    window.electronAPI.on('menu:show-tutorial', handleShowTutorialEvent);
    window.electronAPI.on('menu:show-shortcuts', handleShowShortcutsEvent);
    window.electronAPI.on('menu:check-updates', handleCheckUpdatesEvent);
    window.electronAPI.on('menu:show-about', handleShowAboutEvent);

    // Cleanup listeners on unmount
    return () => {
      window.electronAPI.removeAllListeners('menu:navigate');
      window.electronAPI.removeAllListeners('menu:action');
      window.electronAPI.removeAllListeners('menu:export-data');
      window.electronAPI.removeAllListeners('menu:import-data');
      window.electronAPI.removeAllListeners('menu:create-backup');
      window.electronAPI.removeAllListeners('menu:restore-backup');
      window.electronAPI.removeAllListeners('menu:show-tutorial');
      window.electronAPI.removeAllListeners('menu:show-shortcuts');
      window.electronAPI.removeAllListeners('menu:check-updates');
      window.electronAPI.removeAllListeners('menu:show-about');
    };
  }, [
    handleMenuNavigation,
    handleMenuAction,
    handleExportData,
    handleImportData,
    handleCreateBackup,
    handleRestoreBackup,
    handleShowTutorial,
    handleShowShortcuts,
    handleCheckUpdates,
    handleShowAbout
  ]);

  const value = {
    handleMenuNavigation,
    handleMenuAction,
    handleExportData,
    handleImportData,
    handleCreateBackup,
    handleRestoreBackup,
    handleShowTutorial,
    handleShowShortcuts,
    handleCheckUpdates,
    handleShowAbout
  };

  return (
    <MenuContext.Provider value={value}>
      {children}
    </MenuContext.Provider>
  );
};

// Helper functions for data operations
async function exportAssessmentResults(filePath) {
  // Implementation for exporting assessment results
  console.log('Exporting assessment results to:', filePath);
}

async function exportStudentData(filePath) {
  // Implementation for exporting student data
  console.log('Exporting student data to:', filePath);
}

async function exportReports(filePath) {
  // Implementation for exporting reports
  console.log('Exporting reports to:', filePath);
}

async function importStudentData(filePath) {
  // Implementation for importing student data
  console.log('Importing student data from:', filePath);
}

async function importAssessmentData(filePath) {
  // Implementation for importing assessment data
  console.log('Importing assessment data from:', filePath);
}
