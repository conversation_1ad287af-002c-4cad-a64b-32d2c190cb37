import{j as e,r as j,u as oe}from"./chunk-81a058b1.js";import{b as ce,u as ue}from"./main-272222cd.js";import{F as Y,A as y,w as J,I as G,m as k,y as F,a2 as pe,$ as de,o as B,k as S,T as q,a3 as U,C as Q,c as O,p as be,a4 as Ne,q as Ae,Z as z,x as xe,b as ve,U as he}from"./chunk-0b87a8e8.js";import"./chunk-81a949b4.js";const Z=s=>{const t={};if(!s.name||s.name.trim().length===0?t.name=["Assessment name is required"]:s.name.trim().length<3?t.name=["Assessment name must be at least 3 characters long"]:s.name.trim().length>100?t.name=["Assessment name must not exceed 100 characters"]:/^[a-zA-Z0-9\s\-_.,()]+$/.test(s.name.trim())||(t.name=["Assessment name contains invalid characters"]),!s.shortDescription||s.shortDescription.trim().length===0?t.shortDescription=["Short description is required"]:s.shortDescription.trim().length<10?t.shortDescription=["Short description must be at least 10 characters long"]:s.shortDescription.trim().length>500&&(t.shortDescription=["Short description must not exceed 500 characters"]),!s.academicYear)t.academicYear=["Academic year is required"];else if(!/^\d{4}-\d{4}$/.test(s.academicYear))t.academicYear=["Academic year must be in YYYY-YYYY format"];else{const i=s.academicYear.split("-");if(i.length!==2)t.academicYear=["Academic year must be in YYYY-YYYY format"];else{const d=i[0].trim(),a=i[1].trim(),o=parseInt(d,10),A=parseInt(a,10);if(isNaN(o)||!Number.isInteger(o))t.academicYear=["Start year must be a valid integer"];else if(isNaN(A)||!Number.isInteger(A))t.academicYear=["End year must be a valid integer"];else if(A!==o+1)t.academicYear=["Academic year must be consecutive years (e.g., 2024-2025)"];else{const p=new Date().getFullYear();(o<p-10||o>p+5)&&(t.academicYear=["Academic year must be within reasonable range"])}}}if(s.courseType?["Course 01","Course 02","Course 03","Course 04"].includes(s.courseType)||(t.courseType=["Invalid course type selected"]):t.courseType=["Course type is required"],s.assessmentType?["Formative","Summative","Peer Review"].includes(s.assessmentType)||(t.assessmentType=["Invalid assessment type selected"]):t.assessmentType=["Assessment type is required"],!s.dueDate)t.dueDate=["Due date is required"];else{const i=new Date(s.dueDate),d=new Date;if(d.setHours(0,0,0,0),isNaN(i.getTime()))t.dueDate=["Invalid due date format"];else if(i<=d)t.dueDate=["Due date must be in the future"];else{const a=new Date;a.setFullYear(a.getFullYear()+2),i>a&&(t.dueDate=["Due date cannot be more than 2 years in the future"])}}return s.instructions&&s.instructions.trim().length>2e3&&(t.instructions=["Instructions must not exceed 2000 characters"]),t},fe=(s,t)=>{const i={};if(!s.questionText||s.questionText.trim().length===0?i.questionText=["Question text is required"]:s.questionText.trim().length<10?i.questionText=["Question text must be at least 10 characters long"]:s.questionText.trim().length>1e3&&(i.questionText=["Question text must not exceed 1000 characters"]),s.questionType?s.questionType!=="MCQ"&&(i.questionType=["Only Multiple Choice Questions (MCQ) are currently supported"]):i.questionType=["Question type is required"],s.questionType==="MCQ")if(!s.options||!Array.isArray(s.options))i.options=["MCQ questions must have options"];else if(s.options.length<2)i.options=["MCQ questions must have at least 2 options"];else if(s.options.length>8)i.options=["MCQ questions cannot have more than 8 options"];else{const d=[];let a=!1;s.options.forEach((p,E)=>{const m={};if(!p.text||p.text.trim().length===0?m.text="Option text is required":p.text.trim().length>200&&(m.text="Option text must not exceed 200 characters"),p.marks===void 0||p.marks===null||p.marks==="")m.marks="Option marks are required";else{const f=String(p.marks).trim();if(f===""||f==="null"||f==="undefined")m.marks="Option marks are required";else{const u=parseInt(f,10);isNaN(u)||!Number.isInteger(u)?m.marks="Option marks must be a whole number":u<0?m.marks="Option marks cannot be negative":u>100?m.marks="Option marks cannot exceed 100":a=!0}}Object.keys(m).length>0&&(d[E]=m)}),d.length>0&&(i.optionErrors=d),a||(i.options=["At least one option must have valid marks"]);const o=s.options.map(p=>p.text?.trim().toLowerCase()).filter(p=>p);o.filter((p,E)=>o.indexOf(p)!==E).length>0&&(i.options=["Option texts must be unique within the question"])}return i},W=s=>{const t={};if(!s||!Array.isArray(s))return{general:["Questions data must be an array"]};if(s.length===0)return{general:["At least one question is required"]};if(s.length>50)return{general:["Assessment cannot have more than 50 questions"]};s.forEach((a,o)=>{const A=fe(a);Object.keys(A).length>0&&(t[o]=A)});const i=s.map(a=>a.questionText?.trim().toLowerCase()).filter(a=>a);return i.filter((a,o)=>i.indexOf(a)!==o).length>0&&(t.general=["Question texts must be unique within the assessment"]),t},H=(s,t)=>{const i=Z(s),d=W(t);return{assessment:i,questions:d,isValid:Object.keys(i).length===0&&Object.keys(d).length===0}},ge=()=>{const s=new Date().getFullYear(),t=[];for(let i=s-2;i<=s+5;i++)t.push({value:`${i}-${i+1}`,label:`${i}-${i+1}`});return t},je=[{value:"Course 01",label:"Course 01"},{value:"Course 02",label:"Course 02"},{value:"Course 03",label:"Course 03"},{value:"Course 04",label:"Course 04"}],Ee=[{value:"Formative",label:"Formative Assessment"},{value:"Summative",label:"Summative Assessment"},{value:"Peer Review",label:"Peer Review Assessment"}],we=s=>({name:s.name?.trim(),shortDescription:s.shortDescription?.trim(),academicYear:s.academicYear,courseType:s.courseType,assessmentType:s.assessmentType,dueDate:s.dueDate,instructions:s.instructions?.trim()||null}),ye=s=>s.map(t=>({questionText:t.questionText?.trim(),questionType:t.questionType||"MCQ",options:t.options?.map(i=>({text:i.text?.trim(),marks:(()=>{const d=String(i.marks||0).trim(),a=parseInt(d,10);return isNaN(a)?0:a})()}))||[]})),K=s=>{const t=[];return s.assessment&&Object.entries(s.assessment).forEach(([i,d])=>{d.forEach(a=>{t.push(`Assessment ${i}: ${a}`)})}),s.questions&&(s.questions.general&&s.questions.general.forEach(i=>{t.push(`Questions: ${i}`)}),Object.entries(s.questions).forEach(([i,d])=>{if(i!=="general"){const a=parseInt(i,10)+1;Object.entries(d).forEach(([o,A])=>{Array.isArray(A)?A.forEach(p=>{t.push(`Question ${a} ${o}: ${p}`)}):o==="optionErrors"&&Object.entries(A).forEach(([p,E])=>{const m=parseInt(p,10)+1;Object.entries(E).forEach(([f,u])=>{t.push(`Question ${a} Option ${m} ${f}: ${u}`)})})})}})),t},Ce=({assessmentData:s,onChange:t,validationErrors:i,academicYearOptions:d,courseTypeOptions:a,assessmentTypeOptions:o})=>{const A=(m,f)=>{t({[m]:f})},p=m=>m?new Date(m).toISOString().split("T")[0]:"",E=m=>{A("dueDate",m)};return e.jsxDEV("div",{className:"p-6",children:[e.jsxDEV("div",{className:"mb-6",children:[e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(Y,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:36,columnNumber:11},globalThis),"Assessment Information"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:35,columnNumber:9},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Provide basic information about your assessment. All fields marked with * are required."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:39,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:34,columnNumber:7},globalThis),e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{htmlFor:"assessmentName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Assessment Name *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:47,columnNumber:11},globalThis),e.jsxDEV("input",{type:"text",id:"assessmentName",value:s.name,onChange:m=>A("name",m.target.value),className:`input ${i.name?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,placeholder:"Enter assessment name (3-100 characters)",maxLength:100},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:50,columnNumber:11},globalThis),i.name&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[e.jsxDEV(y,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:61,columnNumber:15},globalThis),i.name[0]]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:60,columnNumber:13},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:[s.name.length,"/100 characters"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:65,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:46,columnNumber:9},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{htmlFor:"shortDescription",className:"block text-sm font-medium text-gray-700 mb-1",children:"Short Description *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:72,columnNumber:11},globalThis),e.jsxDEV("textarea",{id:"shortDescription",rows:3,value:s.shortDescription,onChange:m=>A("shortDescription",m.target.value),className:`input ${i.shortDescription?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,placeholder:"Provide a brief description of the assessment (10-500 characters)",maxLength:500},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:75,columnNumber:11},globalThis),i.shortDescription&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[e.jsxDEV(y,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:86,columnNumber:15},globalThis),i.shortDescription[0]]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:85,columnNumber:13},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:[s.shortDescription.length,"/500 characters"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:90,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:71,columnNumber:9},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{htmlFor:"academicYear",className:"block text-sm font-medium text-gray-700 mb-1",children:"Academic Year *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:99,columnNumber:13},globalThis),e.jsxDEV("select",{id:"academicYear",value:s.academicYear,onChange:m=>A("academicYear",m.target.value),className:`input ${i.academicYear?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,children:[e.jsxDEV("option",{value:"",children:"Select academic year"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:108,columnNumber:15},globalThis),d.map(m=>e.jsxDEV("option",{value:m.value,children:m.label},m.value,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:110,columnNumber:17},globalThis))]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:102,columnNumber:13},globalThis),i.academicYear&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[e.jsxDEV(y,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:117,columnNumber:17},globalThis),i.academicYear[0]]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:116,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:98,columnNumber:11},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{htmlFor:"courseType",className:"block text-sm font-medium text-gray-700 mb-1",children:"Course Type *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:125,columnNumber:13},globalThis),e.jsxDEV("select",{id:"courseType",value:s.courseType,onChange:m=>A("courseType",m.target.value),className:`input ${i.courseType?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,children:[e.jsxDEV("option",{value:"",children:"Select course type"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:134,columnNumber:15},globalThis),a.map(m=>e.jsxDEV("option",{value:m.value,children:m.label},m.value,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:136,columnNumber:17},globalThis))]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:128,columnNumber:13},globalThis),i.courseType&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[e.jsxDEV(y,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:143,columnNumber:17},globalThis),i.courseType[0]]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:142,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:124,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:96,columnNumber:9},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{htmlFor:"assessmentType",className:"block text-sm font-medium text-gray-700 mb-1",children:"Assessment Type *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:154,columnNumber:13},globalThis),e.jsxDEV("select",{id:"assessmentType",value:s.assessmentType,onChange:m=>A("assessmentType",m.target.value),className:`input ${i.assessmentType?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,children:[e.jsxDEV("option",{value:"",children:"Select assessment type"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:163,columnNumber:15},globalThis),o.map(m=>e.jsxDEV("option",{value:m.value,children:m.label},m.value,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:165,columnNumber:17},globalThis))]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:157,columnNumber:13},globalThis),i.assessmentType&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[e.jsxDEV(y,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:172,columnNumber:17},globalThis),i.assessmentType[0]]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:171,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:153,columnNumber:11},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{htmlFor:"dueDate",className:"block text-sm font-medium text-gray-700 mb-1",children:"Due Date *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:180,columnNumber:13},globalThis),e.jsxDEV("div",{className:"relative",children:[e.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsxDEV(J,{className:"h-4 w-4 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:185,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:184,columnNumber:15},globalThis),e.jsxDEV("input",{type:"date",id:"dueDate",value:p(s.dueDate),onChange:m=>E(m.target.value),className:`input pl-10 ${i.dueDate?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,min:new Date().toISOString().split("T")[0]},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:187,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:183,columnNumber:13},globalThis),i.dueDate&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[e.jsxDEV(y,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:198,columnNumber:17},globalThis),i.dueDate[0]]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:197,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:179,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:151,columnNumber:9},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{htmlFor:"instructions",className:"block text-sm font-medium text-gray-700 mb-1",children:"Instructions (Optional)"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:207,columnNumber:11},globalThis),e.jsxDEV("textarea",{id:"instructions",rows:6,value:s.instructions,onChange:m=>A("instructions",m.target.value),className:`input ${i.instructions?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,placeholder:"Provide detailed instructions for students taking this assessment...",maxLength:2e3},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:210,columnNumber:11},globalThis),i.instructions&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[e.jsxDEV(y,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:221,columnNumber:15},globalThis),i.instructions[0]]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:220,columnNumber:13},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:[s.instructions.length,"/2000 characters"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:225,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:206,columnNumber:9},globalThis),e.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(G,{className:"h-5 w-5 text-blue-400 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:233,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-blue-900",children:"Assessment Information"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:235,columnNumber:15},globalThis),e.jsxDEV("div",{className:"text-sm text-blue-800 mt-1 space-y-1",children:[e.jsxDEV("p",{children:"• Assessment names must be unique within your institution"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:237,columnNumber:17},globalThis),e.jsxDEV("p",{children:"• Due dates must be set in the future for student access"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:238,columnNumber:17},globalThis),e.jsxDEV("p",{children:"• Assessment type determines how peer reviews are conducted"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:239,columnNumber:17},globalThis),e.jsxDEV("p",{children:"• Instructions will be displayed to students before they begin"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:240,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:236,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:234,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:232,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:231,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:44,columnNumber:7},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",lineNumber:33,columnNumber:5},globalThis)},Te=({questionsData:s,onChange:t,validationErrors:i})=>{const[d,a]=j.useState(new Set([0])),[o,A]=j.useState(null),p=()=>{const l={id:Date.now(),questionText:"",questionType:"MCQ",options:[{id:Date.now()+1,text:"",marks:0},{id:Date.now()+2,text:"",marks:0}]},n=[...s,l];t(n),a(r=>new Set([...r,n.length-1]))},E=l=>{if(s.length<=1)return;const n=s.filter((r,x)=>x!==l);t(n),a(r=>{const x=new Set;return r.forEach(b=>{b<l?x.add(b):b>l&&x.add(b-1)}),x})},m=l=>{const n=s[l],r={...n,id:Date.now(),questionText:n.questionText+" (Copy)",options:n.options.map(b=>({...b,id:Date.now()+Math.random()}))},x=[...s];x.splice(l+1,0,r),t(x),a(b=>new Set([...b,l+1]))},f=(l,n,r)=>{const x=[...s];x[l]={...x[l],[n]:r},t(x)},u=l=>{const n=[...s],r=n[l];r.options.length>=8||(r.options.push({id:Date.now(),text:"",marks:0}),t(n))},N=(l,n)=>{const r=[...s],x=r[l];x.options.length<=2||(x.options.splice(n,1),t(r))},h=(l,n,r,x)=>{const b=[...s],v=b[l].options[n];v[r]=r==="marks"?x===""?0:Number(x):x,t(b)},w=(l,n,r)=>{const x=[...s],b=x[l].options,v=r==="up"?n-1:n+1;v>=0&&v<b.length&&([b[n],b[v]]=[b[v],b[n]],t(x))},V=l=>{a(n=>{const r=new Set(n);return r.has(l)?r.delete(l):r.add(l),r})},R=(l,n)=>{A(n),l.dataTransfer.effectAllowed="move"},T=l=>{l.preventDefault(),l.dataTransfer.dropEffect="move"},M=(l,n)=>{if(l.preventDefault(),o===null||o===n){A(null);return}const r=[...s],x=r[o];r.splice(o,1);const b=o<n?n-1:n;r.splice(b,0,x),t(r),A(null)};return e.jsxDEV("div",{className:"p-6",children:[e.jsxDEV("div",{className:"mb-6",children:[e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("div",{children:[e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(k,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:179,columnNumber:15},globalThis),"Assessment Questions"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:178,columnNumber:13},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Create Multiple Choice Questions (MCQ) for your assessment. Each question can have 2-8 options with different mark allocations."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:182,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:177,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:p,disabled:s.length>=50,className:"btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsxDEV(F,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:191,columnNumber:13},globalThis),e.jsxDEV("span",{children:"Add Question"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:192,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:186,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:176,columnNumber:9},globalThis),s.length>0&&e.jsxDEV("div",{className:"mt-2 text-sm text-gray-500",children:[s.length," question",s.length!==1?"s":""," created (max 50)"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:197,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:175,columnNumber:7},globalThis),e.jsxDEV("div",{className:"space-y-4",children:s.map((l,n)=>{const r=i[n]||{},x=d.has(n);return e.jsxDEV("div",{className:`border rounded-lg ${r&&Object.keys(r).length>0?"border-red-300":"border-gray-200"}`,draggable:!0,onDragStart:b=>R(b,n),onDragOver:T,onDrop:b=>M(b,n),children:[e.jsxDEV("div",{className:"p-4 bg-gray-50 border-b border-gray-200 flex items-center justify-between",children:[e.jsxDEV("div",{className:"flex items-center space-x-3",children:[e.jsxDEV(pe,{className:"h-5 w-5 text-gray-400 cursor-move"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:221,columnNumber:19},globalThis),e.jsxDEV("span",{className:"font-medium text-gray-900",children:["Question ",n+1]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:222,columnNumber:19},globalThis),r&&Object.keys(r).length>0&&e.jsxDEV(y,{className:"h-4 w-4 text-red-500"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:226,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:220,columnNumber:17},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-2",children:[e.jsxDEV("button",{onClick:()=>m(n),className:"text-gray-400 hover:text-gray-600",title:"Duplicate Question",children:e.jsxDEV(de,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:236,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:231,columnNumber:19},globalThis),e.jsxDEV("button",{onClick:()=>V(n),className:"text-gray-400 hover:text-gray-600",title:x?"Collapse":"Expand",children:x?e.jsxDEV(B,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:243,columnNumber:35},globalThis):e.jsxDEV(S,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:243,columnNumber:68},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:238,columnNumber:19},globalThis),e.jsxDEV("button",{onClick:()=>E(n),disabled:s.length<=1,className:"text-red-400 hover:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed",title:"Remove Question",children:e.jsxDEV(q,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:251,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:245,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:230,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:219,columnNumber:15},globalThis),x&&e.jsxDEV("div",{className:"p-4 space-y-4",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Question Text *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:261,columnNumber:21},globalThis),e.jsxDEV("textarea",{rows:3,value:l.questionText,onChange:b=>f(n,"questionText",b.target.value),className:`input ${r.questionText?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,placeholder:"Enter your question here...",maxLength:1e3},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:264,columnNumber:21},globalThis),r.questionText&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[e.jsxDEV(y,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:274,columnNumber:25},globalThis),r.questionText[0]]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:273,columnNumber:23},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:[l.questionText.length,"/1000 characters"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:278,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:260,columnNumber:19},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Question Type"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:285,columnNumber:21},globalThis),e.jsxDEV("select",{value:l.questionType,onChange:b=>f(n,"questionType",b.target.value),className:"input",disabled:!0,children:e.jsxDEV("option",{value:"MCQ",children:"Multiple Choice Question"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:294,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:288,columnNumber:21},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"Currently only MCQ questions are supported"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:296,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:284,columnNumber:19},globalThis),l.questionType==="MCQ"&&e.jsxDEV("div",{children:[e.jsxDEV("div",{className:"flex justify-between items-center mb-3",children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700",children:"Answer Options *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:305,columnNumber:25},globalThis),e.jsxDEV("button",{onClick:()=>u(n),disabled:l.options.length>=8,className:"btn-outline text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsxDEV(F,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:313,columnNumber:27},globalThis),e.jsxDEV("span",{children:"Add Option"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:314,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:308,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:304,columnNumber:23},globalThis),e.jsxDEV("div",{className:"space-y-3",children:l.options.map((b,v)=>e.jsxDEV("div",{className:`border rounded-md p-3 ${r.optionErrors&&r.optionErrors[v]?"border-red-300 bg-red-50":"border-gray-200"}`,children:e.jsxDEV("div",{className:"flex items-start space-x-3",children:[e.jsxDEV("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600 mt-1",children:String.fromCharCode(65+v)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:330,columnNumber:31},globalThis),e.jsxDEV("div",{className:"flex-1 space-y-3",children:[e.jsxDEV("div",{children:[e.jsxDEV("input",{type:"text",value:b.text,onChange:C=>h(n,v,"text",C.target.value),className:`input ${r.optionErrors&&r.optionErrors[v]?.text?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,placeholder:`Option ${String.fromCharCode(65+v)} text`,maxLength:200},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:338,columnNumber:35},globalThis),r.optionErrors&&r.optionErrors[v]?.text&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600",children:r.optionErrors[v].text},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:351,columnNumber:37},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:337,columnNumber:33},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-3",children:e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Marks"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:360,columnNumber:37},globalThis),e.jsxDEV("input",{type:"number",min:"0",max:"100",value:b.marks,onChange:C=>h(n,v,"marks",C.target.value),className:`input ${r.optionErrors&&r.optionErrors[v]?.marks?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,placeholder:"0"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:363,columnNumber:37},globalThis),r.optionErrors&&r.optionErrors[v]?.marks&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600",children:r.optionErrors[v].marks},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:377,columnNumber:39},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:359,columnNumber:35},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:358,columnNumber:33},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:335,columnNumber:31},globalThis),e.jsxDEV("div",{className:"flex-shrink-0 flex flex-col space-y-1",children:[e.jsxDEV("button",{onClick:()=>w(n,v,"up"),disabled:v===0,className:"text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",title:"Move Up",children:e.jsxDEV(U,{className:"h-4 w-4 transform rotate-180"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:393,columnNumber:35},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:387,columnNumber:33},globalThis),e.jsxDEV("button",{onClick:()=>w(n,v,"down"),disabled:v===l.options.length-1,className:"text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",title:"Move Down",children:e.jsxDEV(U,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:401,columnNumber:35},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:395,columnNumber:33},globalThis),e.jsxDEV("button",{onClick:()=>N(n,v),disabled:l.options.length<=2,className:"text-red-400 hover:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed",title:"Remove Option",children:e.jsxDEV(q,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:409,columnNumber:35},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:403,columnNumber:33},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:386,columnNumber:31},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:328,columnNumber:29},globalThis)},b.id||v,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:320,columnNumber:27},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:318,columnNumber:23},globalThis),e.jsxDEV("div",{className:"mt-3 text-sm text-gray-500",children:[l.options.length,"/8 options • Minimum 2, Maximum 8 options per question"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:418,columnNumber:23},globalThis),e.jsxDEV("div",{className:"mt-4 bg-blue-50 border border-blue-200 rounded-md p-3",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(y,{className:"h-4 w-4 text-blue-400 mt-0.5 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:425,columnNumber:27},globalThis),e.jsxDEV("div",{className:"text-sm text-blue-800",children:[e.jsxDEV("p",{className:"font-medium",children:"Peer Review MCQ Guidelines:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:427,columnNumber:29},globalThis),e.jsxDEV("ul",{className:"mt-1 space-y-1",children:[e.jsxDEV("li",{children:"• All options are considered valid answers in peer review context"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:429,columnNumber:31},globalThis),e.jsxDEV("li",{children:"• Assign marks based on the quality or depth of the response"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:430,columnNumber:31},globalThis),e.jsxDEV("li",{children:"• Higher marks can indicate more comprehensive or insightful answers"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:431,columnNumber:31},globalThis),e.jsxDEV("li",{children:"• Students will see all options and their associated marks"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:432,columnNumber:31},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:428,columnNumber:29},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:426,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:424,columnNumber:25},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:423,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:303,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:258,columnNumber:17},globalThis)]},l.id||n,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:210,columnNumber:13},globalThis)})},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:204,columnNumber:7},globalThis),s.length===0&&e.jsxDEV("div",{className:"text-center py-12 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg",children:[e.jsxDEV(k,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:449,columnNumber:11},globalThis),e.jsxDEV("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No questions yet"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:450,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by adding your first question to the assessment."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:451,columnNumber:11},globalThis),e.jsxDEV("div",{className:"mt-6",children:e.jsxDEV("button",{onClick:p,className:"btn-primary flex items-center space-x-2 mx-auto",children:[e.jsxDEV(F,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:459,columnNumber:15},globalThis),e.jsxDEV("span",{children:"Add First Question"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:460,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:455,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:454,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:448,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",lineNumber:174,columnNumber:5},globalThis)},Pe=({assessmentData:s,questionsData:t,validationErrors:i,onCreateAssessment:d,creating:a})=>{const[o,A]=j.useState(!1),p=Object.keys(i).length>0,E=t.reduce((u,N)=>u+N.options.reduce((h,w)=>Math.max(h,w.marks),0),0),m=u=>u?new Date(u).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"Not set",f=u=>{switch(u){case"Formative":return"Ongoing assessment for learning and feedback";case"Summative":return"Assessment of learning at the end of instruction";case"Peer Review":return"Students assess and provide feedback on peer work";default:return"Assessment type not specified"}};return e.jsxDEV("div",{className:"p-6",children:[e.jsxDEV("div",{className:"mb-6",children:[e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(S,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:54,columnNumber:11},globalThis),"Review & Publish Assessment"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:53,columnNumber:9},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Review your assessment details and questions before publishing. Once published, the assessment will be available for assignment to student batches."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:57,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:52,columnNumber:7},globalThis),p?e.jsxDEV("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(y,{className:"h-5 w-5 text-red-400 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:66,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-red-900 mb-2",children:"Please fix the following errors before publishing:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:68,columnNumber:15},globalThis),e.jsxDEV("ul",{className:"text-sm text-red-800 space-y-1",children:K({assessment:i,questions:i}).map((u,N)=>e.jsxDEV("li",{children:["• ",u]},N,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:76,columnNumber:19},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:71,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:67,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:65,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:64,columnNumber:9},globalThis):e.jsxDEV("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(Q,{className:"h-5 w-5 text-green-400 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:85,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-green-900",children:"Assessment is ready for publishing"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:87,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-green-800 mt-1",children:"All required fields are completed and validation checks have passed."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:90,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:86,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:84,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:83,columnNumber:9},globalThis),e.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg mb-6",children:[e.jsxDEV("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxDEV("h4",{className:"text-md font-medium text-gray-900 flex items-center",children:[e.jsxDEV(Y,{className:"h-4 w-4 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:102,columnNumber:13},globalThis),"Assessment Summary"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:101,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:100,columnNumber:9},globalThis),e.jsxDEV("div",{className:"p-6 space-y-4",children:[e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxDEV("div",{children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Assessment Name"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:111,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-900",children:s.name||"Not specified"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:112,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:110,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Assessment Type"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:115,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-900",children:s.assessmentType||"Not specified"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:116,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-xs text-gray-500 mt-1",children:f(s.assessmentType)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:119,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:114,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Academic Year"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:124,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-900",children:s.academicYear||"Not specified"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:125,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:123,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Course Type"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:128,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-900",children:s.courseType||"Not specified"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:129,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:127,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Due Date"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:132,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-900 flex items-center",children:[e.jsxDEV(J,{className:"h-4 w-4 mr-1 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:134,columnNumber:17},globalThis),m(s.dueDate)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:133,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:131,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Total Questions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:139,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-900 flex items-center",children:[e.jsxDEV(k,{className:"h-4 w-4 mr-1 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:141,columnNumber:17},globalThis),t.length," question",t.length!==1?"s":""]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:140,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:138,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:109,columnNumber:11},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Description"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:149,columnNumber:13},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-900 bg-gray-50 p-3 rounded-md",children:s.shortDescription||"No description provided"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:150,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:148,columnNumber:11},globalThis),s.instructions&&e.jsxDEV("div",{children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Instructions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:158,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-900 bg-gray-50 p-3 rounded-md whitespace-pre-wrap",children:s.instructions},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:159,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:157,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:107,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:99,columnNumber:7},globalThis),e.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg mb-6",children:[e.jsxDEV("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("h4",{className:"text-md font-medium text-gray-900 flex items-center",children:[e.jsxDEV(k,{className:"h-4 w-4 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:172,columnNumber:15},globalThis),"Questions Overview"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:171,columnNumber:13},globalThis),e.jsxDEV("button",{onClick:()=>A(!o),className:"btn-outline text-sm flex items-center space-x-1",children:[e.jsxDEV(S,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:179,columnNumber:15},globalThis),e.jsxDEV("span",{children:[o?"Hide":"Show"," Preview"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:180,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:175,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:170,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:169,columnNumber:9},globalThis),e.jsxDEV("div",{className:"p-6",children:[e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-blue-600",children:t.length},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:189,columnNumber:15},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Total Questions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:190,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:188,columnNumber:13},globalThis),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-green-600",children:E},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:193,columnNumber:15},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Maximum Marks"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:194,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:192,columnNumber:13},globalThis),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-purple-600",children:t.reduce((u,N)=>u+N.options.length,0)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:197,columnNumber:15},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Total Options"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:200,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:196,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:187,columnNumber:11},globalThis),e.jsxDEV("div",{className:"space-y-3",children:t.map((u,N)=>e.jsxDEV("div",{className:"border border-gray-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex justify-between items-start",children:[e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV("h6",{className:"text-sm font-medium text-gray-900",children:["Question ",N+1]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:210,columnNumber:21},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1 line-clamp-2",children:u.questionText||"No question text"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:213,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:209,columnNumber:19},globalThis),e.jsxDEV("div",{className:"text-right text-sm text-gray-500",children:[e.jsxDEV("div",{children:[u.options.length," options"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:218,columnNumber:21},globalThis),e.jsxDEV("div",{children:["Max: ",Math.max(...u.options.map(h=>h.marks))," marks"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:219,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:217,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:208,columnNumber:17},globalThis)},N,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:207,columnNumber:15},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:205,columnNumber:11},globalThis),o&&e.jsxDEV("div",{className:"mt-6 border-t border-gray-200 pt-6",children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-900 mb-4",children:"Question Preview"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:229,columnNumber:15},globalThis),e.jsxDEV("div",{className:"space-y-6",children:t.map((u,N)=>e.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-md p-4",children:[e.jsxDEV("div",{className:"mb-3",children:[e.jsxDEV("h6",{className:"text-sm font-medium text-gray-900",children:["Question ",N+1]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:234,columnNumber:23},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-700 mt-1",children:u.questionText},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:237,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:233,columnNumber:21},globalThis),e.jsxDEV("div",{className:"space-y-2",children:u.options.map((h,w)=>e.jsxDEV("div",{className:"flex items-center justify-between bg-white border border-gray-200 rounded p-2",children:[e.jsxDEV("div",{className:"flex items-center space-x-2",children:[e.jsxDEV("span",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600",children:String.fromCharCode(65+w)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:246,columnNumber:29},globalThis),e.jsxDEV("span",{className:"text-sm text-gray-900",children:h.text},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:249,columnNumber:29},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:245,columnNumber:27},globalThis),e.jsxDEV("span",{className:"text-sm font-medium text-gray-600",children:[h.marks," marks"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:251,columnNumber:27},globalThis)]},w,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:244,columnNumber:25},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:242,columnNumber:21},globalThis)]},N,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:232,columnNumber:19},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:230,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:228,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:185,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:168,columnNumber:7},globalThis),e.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(G,{className:"h-5 w-5 text-blue-400 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:268,columnNumber:11},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-blue-900",children:"Publishing Information"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:270,columnNumber:13},globalThis),e.jsxDEV("div",{className:"text-sm text-blue-800 mt-1 space-y-1",children:[e.jsxDEV("p",{children:"• Once published, the assessment will be available for assignment to student batches"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:272,columnNumber:15},globalThis),e.jsxDEV("p",{children:"• You can assign the assessment to multiple batches after publishing"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:273,columnNumber:15},globalThis),e.jsxDEV("p",{children:"• Students will be able to access the assessment once it's assigned to their batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:274,columnNumber:15},globalThis),e.jsxDEV("p",{children:"• Assessment questions and options cannot be modified after publishing"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:275,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:271,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:269,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:267,columnNumber:9},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:266,columnNumber:7},globalThis),e.jsxDEV("div",{className:"flex justify-center",children:e.jsxDEV("button",{onClick:d,disabled:p||a,className:"btn-primary flex items-center space-x-2 px-8 py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed",children:a?e.jsxDEV(e.Fragment,{children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:290,columnNumber:15},globalThis),e.jsxDEV("span",{children:"Publishing Assessment..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:291,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:289,columnNumber:13},globalThis):e.jsxDEV(e.Fragment,{children:[e.jsxDEV(Q,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:295,columnNumber:15},globalThis),e.jsxDEV("span",{children:"Publish Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:296,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:294,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:283,columnNumber:9},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:282,columnNumber:7},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",lineNumber:51,columnNumber:5},globalThis)},De=({onAuthenticate:s,loading:t,user:i})=>{const[d,a]=j.useState({adminId:i?.id||"",password:""}),[o,A]=j.useState(!1),[p,E]=j.useState({}),m=()=>{const N={};return d.adminId?d.adminId!==i?.id&&(N.adminId="Admin ID does not match current user"):N.adminId="Super Admin ID is required",d.password?d.password.length<6&&(N.password="Password must be at least 6 characters"):N.password="Password is required",E(N),Object.keys(N).length===0},f=N=>{N.preventDefault(),m()&&s(d)},u=(N,h)=>{a(w=>({...w,[N]:h})),p[N]&&E(w=>({...w,[N]:""}))};return e.jsxDEV("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:e.jsxDEV("div",{className:"max-w-md w-full space-y-8",children:[e.jsxDEV("div",{children:[e.jsxDEV("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100",children:e.jsxDEV(O,{className:"h-8 w-8 text-red-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:60,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:59,columnNumber:11},globalThis),e.jsxDEV("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Super Admin Authentication"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:62,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Additional verification required to create assessments"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:65,columnNumber:11},globalThis),e.jsxDEV("div",{className:"mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(y,{className:"h-5 w-5 text-yellow-400 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:70,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-yellow-900",children:"Security Notice"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:72,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-yellow-800 mt-1",children:"Assessment creation requires Super Admin privileges. Please verify your identity to proceed."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:73,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:71,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:69,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:68,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:58,columnNumber:9},globalThis),e.jsxDEV("form",{className:"mt-8 space-y-6",onSubmit:f,children:[e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{htmlFor:"adminId",className:"block text-sm font-medium text-gray-700 mb-1",children:"Super Admin ID"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:85,columnNumber:15},globalThis),e.jsxDEV("div",{className:"relative",children:[e.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsxDEV(be,{className:"h-5 w-5 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:90,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:89,columnNumber:17},globalThis),e.jsxDEV("input",{id:"adminId",name:"adminId",type:"text",value:d.adminId,onChange:N=>u("adminId",N.target.value),className:`input pl-10 ${p.adminId?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,placeholder:"Enter your Super Admin ID",disabled:t},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:92,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:88,columnNumber:15},globalThis),p.adminId&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600",children:p.adminId},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:104,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:84,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:110,columnNumber:15},globalThis),e.jsxDEV("div",{className:"relative",children:[e.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsxDEV(Ne,{className:"h-5 w-5 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:115,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:114,columnNumber:17},globalThis),e.jsxDEV("input",{id:"password",name:"password",type:o?"text":"password",value:d.password,onChange:N=>u("password",N.target.value),className:`input pl-10 pr-10 ${p.password?"border-red-300 focus:border-red-500 focus:ring-red-500":""}`,placeholder:"Enter your password",disabled:t},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:117,columnNumber:17},globalThis),e.jsxDEV("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>A(!o),disabled:t,children:o?e.jsxDEV(B,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:134,columnNumber:21},globalThis):e.jsxDEV(S,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:136,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:127,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:113,columnNumber:15},globalThis),p.password&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600",children:p.password},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:141,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:109,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:82,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(Q,{className:"h-5 w-5 text-blue-400 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:149,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-blue-900",children:"Current User"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:151,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-blue-800 mt-1",children:["Logged in as: ",e.jsxDEV("strong",{children:[i?.first_name," ",i?.last_name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:153,columnNumber:33},globalThis)," (",i?.email,")"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:152,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-blue-800",children:["Role: ",e.jsxDEV("strong",{children:i?.role},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:156,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:155,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:150,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:148,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:147,columnNumber:11},globalThis),e.jsxDEV("div",{children:e.jsxDEV("button",{type:"submit",disabled:t,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed",children:t?e.jsxDEV(e.Fragment,{children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:171,columnNumber:19},globalThis),"Authenticating..."]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:170,columnNumber:17},globalThis):e.jsxDEV(e.Fragment,{children:[e.jsxDEV(O,{className:"h-4 w-4 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:176,columnNumber:19},globalThis),"Authenticate & Continue"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:175,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:164,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:163,columnNumber:11},globalThis),e.jsxDEV("div",{className:"mt-6",children:e.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-md p-4",children:[e.jsxDEV("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Security Information"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:186,columnNumber:15},globalThis),e.jsxDEV("ul",{className:"text-sm text-gray-600 space-y-1",children:[e.jsxDEV("li",{children:"• Two-factor authentication ensures only authorized Super Admins can create assessments"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:188,columnNumber:17},globalThis),e.jsxDEV("li",{children:"• Your credentials are verified against the current session"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:189,columnNumber:17},globalThis),e.jsxDEV("li",{children:"• This additional step protects sensitive assessment creation functionality"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:190,columnNumber:17},globalThis),e.jsxDEV("li",{children:"• All authentication attempts are logged for security auditing"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:191,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:187,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:185,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:184,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:81,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:57,columnNumber:7},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",lineNumber:56,columnNumber:5},globalThis)},Re=()=>{const s=oe(),{createAssessmentWithQuestions:t,checkAssessmentNameUnique:i,getInstitution:d}=ce(),{user:a}=ue(),[o,A]=j.useState(0),[p,E]=j.useState(!1),[m,f]=j.useState(!1),[u,N]=j.useState({name:"",shortDescription:"",academicYear:"",courseType:"",assessmentType:"",dueDate:"",instructions:""}),[h,w]=j.useState([]),[V,R]=j.useState(!1),[T,M]=j.useState({type:"",text:""}),[l,n]=j.useState({}),[r,x]=j.useState(null),[b,v]=j.useState(null),C=[{id:"info",title:"Assessment Details",description:"Basic assessment information and configuration",icon:Y},{id:"questions",title:"Questions",description:"Create and manage assessment questions",icon:he},{id:"review",title:"Review & Publish",description:"Review assessment and publish for use",icon:S}];j.useEffect(()=>{X(),ee();const c=setInterval(se,3e4);return()=>{c&&clearInterval(c)}},[]),j.useEffect(()=>{n({})},[u,h]);const X=async()=>{try{const c=await d();x(c)}catch{}},ee=()=>{try{const c=localStorage.getItem("createAssessment_draft_assessment"),g=localStorage.getItem("createAssessment_draft_questions"),L=localStorage.getItem("createAssessment_draft_step");c&&N(JSON.parse(c)),g&&w(JSON.parse(g)),L&&A(parseInt(L));const P=localStorage.getItem("createAssessment_draft_timestamp");P&&v(new Date(P))}catch{}},se=()=>{(u.name||h.length>0)&&I()},I=()=>{try{localStorage.setItem("createAssessment_draft_assessment",JSON.stringify(u)),localStorage.setItem("createAssessment_draft_questions",JSON.stringify(h)),localStorage.setItem("createAssessment_draft_step",o.toString()),localStorage.setItem("createAssessment_draft_timestamp",new Date().toISOString()),v(new Date)}catch{}},ie=()=>{try{localStorage.removeItem("createAssessment_draft_assessment"),localStorage.removeItem("createAssessment_draft_questions"),localStorage.removeItem("createAssessment_draft_step"),localStorage.removeItem("createAssessment_draft_timestamp"),v(null)}catch{}},D=(c,g)=>{M({type:c,text:g}),setTimeout(()=>M({type:"",text:""}),5e3)},te=async c=>{f(!0);try{if(c.adminId!==a.id||!c.password)throw new Error("Invalid Super Admin credentials");if(a.role!=="super_admin")throw new Error("Only Super Admins can create assessments");E(!0),D("success","Authentication successful")}catch(g){D("error",g.message)}finally{f(!1)}},ne=()=>{let c={};switch(o){case 0:c=Z(u);break;case 1:c=W(h);break;case 2:const g=H(u,h);c={...g.assessment,...g.questions};break}return n(c),Object.keys(c).length===0},me=()=>{ne()&&(A(c=>Math.min(c+1,C.length-1)),I())},re=()=>{A(c=>Math.max(c-1,0))},le=c=>{N(g=>({...g,...c}))},ae=c=>{w(c)},$=async()=>{try{R(!0);const c=H(u,h);if(!c.isValid){n({...c.assessment,...c.questions}),D("error","Please fix validation errors before creating assessment");return}if(!(await i(u.name,r?.id)).unique){D("error","An assessment with this name already exists");return}const L=we(u),P=ye(h);await t({...L,institutionId:r?.id},P,a.id),D("success","Assessment created successfully!"),ie(),setTimeout(()=>{s("/assessment-management")},2e3)}catch(c){D("error",c.message)}finally{R(!1)}};return!a||a.role!=="super_admin"?e.jsxDEV("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV(O,{className:"mx-auto h-12 w-12 text-red-500"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:298,columnNumber:11},globalThis),e.jsxDEV("h2",{className:"mt-2 text-lg font-medium text-gray-900",children:"Access Denied"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:299,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"Only Super Admins can create assessments."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:300,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:297,columnNumber:9},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:296,columnNumber:7},globalThis):p?e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("div",{children:[e.jsxDEV("h1",{className:"text-2xl font-bold text-gray-900",children:"Create Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:324,columnNumber:11},globalThis),e.jsxDEV("p",{className:"text-gray-600",children:"Create a comprehensive assessment with questions for peer review"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:325,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:323,columnNumber:9},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-3",children:[b&&e.jsxDEV("div",{className:"flex items-center text-sm text-gray-500",children:[e.jsxDEV(Ae,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:330,columnNumber:15},globalThis),e.jsxDEV("span",{children:["Last saved: ",b.toLocaleTimeString()]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:331,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:329,columnNumber:13},globalThis),e.jsxDEV("button",{onClick:()=>s("/assessment-management"),className:"btn-outline flex items-center space-x-2",children:[e.jsxDEV(z,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:338,columnNumber:13},globalThis),e.jsxDEV("span",{children:"Back to Assessments"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:339,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:334,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:327,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:322,columnNumber:7},globalThis),T.text&&e.jsxDEV("div",{className:`rounded-md p-4 ${T.type==="error"?"bg-red-50":"bg-green-50"}`,children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:T.type==="error"?e.jsxDEV(y,{className:"h-5 w-5 text-red-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:350,columnNumber:17},globalThis):e.jsxDEV(Q,{className:"h-5 w-5 text-green-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:352,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:348,columnNumber:13},globalThis),e.jsxDEV("div",{className:"ml-3",children:e.jsxDEV("p",{className:`text-sm ${T.type==="error"?"text-red-800":"text-green-800"}`,children:T.text},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:356,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:355,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:347,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:346,columnNumber:9},globalThis),e.jsxDEV("div",{className:"bg-white shadow rounded-lg p-6",children:e.jsxDEV("nav",{"aria-label":"Progress",children:e.jsxDEV("ol",{className:"flex items-center",children:C.map((c,g)=>{const L=c.icon,P=g===o,_=g<o;return e.jsxDEV("li",{className:`relative ${g!==C.length-1?"pr-8 sm:pr-20":""}`,children:[e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:`relative flex h-8 w-8 items-center justify-center rounded-full ${_?"bg-green-600":P?"bg-blue-600":"bg-gray-300"}`,children:e.jsxDEV(L,{className:"h-4 w-4 text-white"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:383,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:376,columnNumber:21},globalThis),e.jsxDEV("div",{className:"ml-4 min-w-0",children:[e.jsxDEV("p",{className:`text-sm font-medium ${P?"text-blue-600":_?"text-green-600":"text-gray-500"}`,children:c.title},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:386,columnNumber:23},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-500",children:c.description},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:391,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:385,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:375,columnNumber:19},globalThis),g!==C.length-1&&e.jsxDEV("div",{className:"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:395,columnNumber:21},globalThis)]},c.id,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:374,columnNumber:17},globalThis)})},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:367,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:366,columnNumber:9},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:365,columnNumber:7},globalThis),e.jsxDEV("div",{className:"bg-white shadow rounded-lg",children:[o===0&&e.jsxDEV(Ce,{assessmentData:u,onChange:le,validationErrors:l,academicYearOptions:ge(),courseTypeOptions:je,assessmentTypeOptions:Ee},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:407,columnNumber:11},globalThis),o===1&&e.jsxDEV(Te,{questionsData:h,onChange:ae,validationErrors:l},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:418,columnNumber:11},globalThis),o===2&&e.jsxDEV(Pe,{assessmentData:u,questionsData:h,validationErrors:l,onCreateAssessment:$,creating:V},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:426,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:405,columnNumber:7},globalThis),e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("div",{children:o>0&&e.jsxDEV("button",{onClick:re,className:"btn-outline flex items-center space-x-2",children:[e.jsxDEV(z,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:444,columnNumber:15},globalThis),e.jsxDEV("span",{children:"Previous"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:445,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:440,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:438,columnNumber:9},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-3",children:[e.jsxDEV("button",{onClick:I,className:"btn-outline flex items-center space-x-2",children:[e.jsxDEV(xe,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:455,columnNumber:13},globalThis),e.jsxDEV("span",{children:"Save Draft"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:456,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:451,columnNumber:11},globalThis),o<C.length-1?e.jsxDEV("button",{onClick:me,className:"btn-primary flex items-center space-x-2",children:[e.jsxDEV("span",{children:"Next"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:464,columnNumber:15},globalThis),e.jsxDEV(ve,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:465,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:460,columnNumber:13},globalThis):e.jsxDEV("button",{onClick:$,disabled:V,className:"btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:V?e.jsxDEV(e.Fragment,{children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:475,columnNumber:19},globalThis),e.jsxDEV("span",{children:"Creating..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:476,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:474,columnNumber:17},globalThis):e.jsxDEV(e.Fragment,{children:[e.jsxDEV(Q,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:480,columnNumber:19},globalThis),e.jsxDEV("span",{children:"Create Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:481,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:479,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:468,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:450,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:437,columnNumber:7},globalThis),Object.keys(l).length>0&&e.jsxDEV("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(y,{className:"h-5 w-5 text-red-400 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:493,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-red-900 mb-2",children:"Please fix the following errors:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:495,columnNumber:15},globalThis),e.jsxDEV("ul",{className:"text-sm text-red-800 space-y-1",children:K({assessment:o===0?l:{},questions:o===1?l:{}}).map((c,g)=>e.jsxDEV("li",{children:["• ",c]},g,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:501,columnNumber:19},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:496,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:494,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:492,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:491,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:320,columnNumber:5},globalThis):e.jsxDEV(De,{onAuthenticate:te,loading:m,user:a},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",lineNumber:311,columnNumber:7},globalThis)};export{Re as default};
