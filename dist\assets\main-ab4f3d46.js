import { r as reactExports, j as jsxDevRuntimeExports, u as useNavigate, a as useLocation, O as Outlet, R as React, H as <PERSON><PERSON><PERSON><PERSON><PERSON>, b as Routes, c as Route, N as Navigate, d as client } from './chunk-2ef8e52b.js';
import { C as CheckCircle, A as AlertCircle, a as AlertTriangle, I as Info, R as RefreshCw, E as ExternalLink, X, B as Building, S as Sparkles, b as ArrowRight, c as Shield, G as GraduationCap, d as BookOpen, H as Home, U as Users, e as UserPlus, F as FileText, f as ClipboardList, g as Settings$1, L as LogOut, M as Menu, h as Code, i as Mail, j as Globe, K as Keyboard, k as File, l as Eye, m as Search, n as HelpCircle } from './chunk-028772a4.js';
import './chunk-03d61bd9.js';

true&&(function polyfill() {
    const relList = document.createElement('link').relList;
    if (relList && relList.supports && relList.supports('modulepreload')) {
        return;
    }
    for (const link of document.querySelectorAll('link[rel="modulepreload"]')) {
        processPreload(link);
    }
    new MutationObserver((mutations) => {
        for (const mutation of mutations) {
            if (mutation.type !== 'childList') {
                continue;
            }
            for (const node of mutation.addedNodes) {
                if (node.tagName === 'LINK' && node.rel === 'modulepreload')
                    processPreload(node);
            }
        }
    }).observe(document, { childList: true, subtree: true });
    function getFetchOpts(link) {
        const fetchOpts = {};
        if (link.integrity)
            fetchOpts.integrity = link.integrity;
        if (link.referrerPolicy)
            fetchOpts.referrerPolicy = link.referrerPolicy;
        if (link.crossOrigin === 'use-credentials')
            fetchOpts.credentials = 'include';
        else if (link.crossOrigin === 'anonymous')
            fetchOpts.credentials = 'omit';
        else
            fetchOpts.credentials = 'same-origin';
        return fetchOpts;
    }
    function processPreload(link) {
        if (link.ep)
            // ep marker = processed
            return;
        link.ep = true;
        // prepopulate the load record
        const fetchOpts = getFetchOpts(link);
        fetch(link.href, fetchOpts);
    }
}());

const scriptRel = 'modulepreload';const assetsURL = function(dep, importerUrl) { return new URL(dep, importerUrl).href };const seen = {};const __vitePreload = function preload(baseModule, deps, importerUrl) {
    // @ts-expect-error true will be replaced with boolean later
    if (!true || !deps || deps.length === 0) {
        return baseModule();
    }
    const links = document.getElementsByTagName('link');
    return Promise.all(deps.map((dep) => {
        // @ts-expect-error assetsURL is declared before preload.toString()
        dep = assetsURL(dep, importerUrl);
        if (dep in seen)
            return;
        seen[dep] = true;
        const isCss = dep.endsWith('.css');
        const cssSelector = isCss ? '[rel="stylesheet"]' : '';
        const isBaseRelative = !!importerUrl;
        // check if the file is already preloaded by SSR markup
        if (isBaseRelative) {
            // When isBaseRelative is true then we have `importerUrl` and `dep` is
            // already converted to an absolute URL by the `assetsURL` function
            for (let i = links.length - 1; i >= 0; i--) {
                const link = links[i];
                // The `links[i].href` is an absolute URL thanks to browser doing the work
                // for us. See https://html.spec.whatwg.org/multipage/common-dom-interfaces.html#reflecting-content-attributes-in-idl-attributes:idl-domstring-5
                if (link.href === dep && (!isCss || link.rel === 'stylesheet')) {
                    return;
                }
            }
        }
        else if (document.querySelector(`link[href="${dep}"]${cssSelector}`)) {
            return;
        }
        const link = document.createElement('link');
        link.rel = isCss ? 'stylesheet' : scriptRel;
        if (!isCss) {
            link.as = 'script';
            link.crossOrigin = '';
        }
        link.href = dep;
        document.head.appendChild(link);
        if (isCss) {
            return new Promise((res, rej) => {
                link.addEventListener('load', res);
                link.addEventListener('error', () => rej(new Error(`Unable to preload CSS for ${dep}`)));
            });
        }
    }))
        .then(() => baseModule())
        .catch((err) => {
        const e = new Event('vite:preloadError', { cancelable: true });
        // @ts-expect-error custom payload
        e.payload = err;
        window.dispatchEvent(e);
        if (!e.defaultPrevented) {
            throw err;
        }
    });
};

const AuthContext = reactExports.createContext();
const useAuth = () => {
  const context = reactExports.useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
const AuthProvider = ({ children }) => {
  const defaultUser = {
    id: 1,
    username: "admin",
    password: "password",
    // Hidden from UI
    first_name: "System",
    last_name: "Administrator",
    email: "<EMAIL>",
    role: "super_admin",
    institution_id: 1,
    created_at: (/* @__PURE__ */ new Date()).toISOString(),
    updated_at: (/* @__PURE__ */ new Date()).toISOString()
  };
  const [user, setUser] = reactExports.useState(null);
  const [loading, setLoading] = reactExports.useState(true);
  reactExports.useEffect(() => {
    const initializeAuth = async () => {
      try {
        const selectedRole = localStorage.getItem("selectedRole");
        if (selectedRole) {
          const roleUser = {
            ...defaultUser,
            role: selectedRole,
            first_name: getRoleDisplayName(selectedRole),
            last_name: "User"
          };
          setUser(roleUser);
          localStorage.setItem("peer_review_user", JSON.stringify(roleUser));
        } else {
          const storedUser = localStorage.getItem("peer_review_user");
          if (storedUser) {
            try {
              const parsedUser = JSON.parse(storedUser);
              setUser(parsedUser);
            } catch {
              localStorage.removeItem("peer_review_user");
            }
          }
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
      } finally {
        setLoading(false);
      }
    };
    initializeAuth();
  }, []);
  const getRoleDisplayName = (role) => {
    switch (role) {
      case "super_admin":
        return "Administrator";
      case "admin":
        return "Administrator";
      case "teacher":
        return "Teacher";
      case "student":
        return "Student";
      default:
        return "User";
    }
  };
  const login = async (credentials) => {
    try {
      setLoading(true);
      if (credentials && (credentials.username === "admin" || credentials.password === "password")) {
        const currentUser2 = user || defaultUser;
        setUser(currentUser2);
        localStorage.setItem("peer_review_user", JSON.stringify(currentUser2));
        return { success: true, user: currentUser2 };
      }
      const currentUser = user || defaultUser;
      setUser(currentUser);
      localStorage.setItem("peer_review_user", JSON.stringify(currentUser));
      return { success: true, user: currentUser };
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };
  const logout = reactExports.useCallback(() => {
    setUser(null);
    localStorage.removeItem("peer_review_user");
    localStorage.removeItem("selectedRole");
  }, []);
  const updateUser = (updatedUser) => {
    setUser(updatedUser);
    localStorage.setItem("peer_review_user", JSON.stringify(updatedUser));
  };
  const value = {
    user,
    login,
    logout,
    updateUser,
    loading,
    isAuthenticated: !!user,
    isAdmin: user?.role === "admin" || user?.role === "super_admin",
    isSuperAdmin: user?.role === "super_admin",
    isTeacher: user?.role === "teacher",
    isStudent: user?.role === "student"
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AuthContext.Provider, { value, children }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/AuthContext.jsx",
    lineNumber: 145,
    columnNumber: 5
  }, globalThis);
};

const DatabaseContext = reactExports.createContext();
const isElectron = () => {
  return typeof window !== "undefined" && window.electronAPI;
};
const getElectronAPI$1 = () => {
  if (isElectron()) {
    return window.electronAPI;
  }
  throw new Error("Electron API not available. Please ensure you are running the desktop application.");
};
const useDatabase = () => {
  const context = reactExports.useContext(DatabaseContext);
  if (!context) {
    throw new Error("useDatabase must be used within a DatabaseProvider");
  }
  return context;
};
const DatabaseProvider = ({ children }) => {
  const createUser = async (userData) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.users.create(userData);
    } catch (error) {
      throw new Error(`Failed to create user: ${error.message}`);
    }
  };
  const getAllUsers = async (filters = {}) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.users.getAll(filters);
    } catch (error) {
      throw new Error(`Failed to get users: ${error.message}`);
    }
  };
  const updateUser = async (userId, userData, updatedBy) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.admin.updateUser(userId, userData, updatedBy);
    } catch (error) {
      throw new Error(`Failed to update user: ${error.message}`);
    }
  };
  const resetUserPassword = async (userId, newPassword, resetBy) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.admin.resetPassword(userId, newPassword, resetBy);
    } catch (error) {
      throw new Error(`Failed to reset password: ${error.message}`);
    }
  };
  const deactivateUser = async (userId, deactivatedBy) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.admin.deactivateUser(userId, deactivatedBy);
    } catch (error) {
      throw new Error(`Failed to deactivate user: ${error.message}`);
    }
  };
  const activateUser = async (userId, activatedBy) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.admin.activateUser(userId, activatedBy);
    } catch (error) {
      throw new Error(`Failed to activate user: ${error.message}`);
    }
  };
  const generateSecurePassword = async () => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.admin.generatePassword();
    } catch (error) {
      throw new Error(`Failed to generate password: ${error.message}`);
    }
  };
  const getAuditLog = async (filters = {}) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.admin.getAuditLog(filters);
    } catch (error) {
      throw new Error(`Failed to get audit log: ${error.message}`);
    }
  };
  const createBatch = async (batchData) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.create(batchData);
    } catch (error) {
      throw new Error(`Failed to create batch: ${error.message}`);
    }
  };
  const getAllBatches = async (filters = {}) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.getAll(filters);
    } catch (error) {
      throw new Error(`Failed to get batches: ${error.message}`);
    }
  };
  const createBatchWithStudents = async (batchData, studentsData, createdBy) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.createWithStudents(batchData, studentsData, createdBy);
    } catch (error) {
      throw new Error(`Failed to create batch with students: ${error.message}`);
    }
  };
  const checkBatchNameUnique = async (name, teacherId, excludeBatchId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.checkNameUnique(name, teacherId, excludeBatchId);
    } catch (error) {
      throw new Error(`Failed to check batch name uniqueness: ${error.message}`);
    }
  };
  const checkStudentIdsUnique = async (studentIds, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.checkStudentIdsUnique(studentIds, institutionId);
    } catch (error) {
      throw new Error(`Failed to check student ID uniqueness: ${error.message}`);
    }
  };
  const getBatchDetails = async (batchId, teacherId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.getDetails(batchId, teacherId);
    } catch (error) {
      throw new Error(`Failed to get batch details: ${error.message}`);
    }
  };
  const updateBatch = async (batchId, batchData, updatedBy, teacherId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.update(batchId, batchData, updatedBy, teacherId);
    } catch (error) {
      throw new Error(`Failed to update batch: ${error.message}`);
    }
  };
  const addStudentsToBatch = async (batchId, studentsData, addedBy, teacherId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.addStudents(batchId, studentsData, addedBy, teacherId);
    } catch (error) {
      throw new Error(`Failed to add students to batch: ${error.message}`);
    }
  };
  const removeStudentFromBatch = async (batchId, studentId, removedBy, teacherId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.removeStudent(batchId, studentId, removedBy, teacherId);
    } catch (error) {
      throw new Error(`Failed to remove student from batch: ${error.message}`);
    }
  };
  const archiveBatch = async (batchId, archivedBy, teacherId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.archive(batchId, archivedBy, teacherId);
    } catch (error) {
      throw new Error(`Failed to archive batch: ${error.message}`);
    }
  };
  const activateBatch = async (batchId, activatedBy, teacherId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.activate(batchId, activatedBy, teacherId);
    } catch (error) {
      throw new Error(`Failed to activate batch: ${error.message}`);
    }
  };
  const getBatchStatistics = async (teacherId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.batches.getStatistics(teacherId);
    } catch (error) {
      throw new Error(`Failed to get batch statistics: ${error.message}`);
    }
  };
  const createAssessmentWithQuestions = async (assessmentData, questionsData, createdBy) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.createWithQuestions(assessmentData, questionsData, createdBy);
    } catch (error) {
      throw new Error(`Failed to create assessment: ${error.message}`);
    }
  };
  const checkAssessmentNameUnique = async (name, institutionId, excludeAssessmentId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.checkNameUnique(name, institutionId, excludeAssessmentId);
    } catch (error) {
      throw new Error(`Failed to check assessment name uniqueness: ${error.message}`);
    }
  };
  const getAllAssessments = async (filters = {}) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.getAll(filters);
    } catch (error) {
      throw new Error(`Failed to get assessments: ${error.message}`);
    }
  };
  const getAssessmentDetails = async (assessmentId, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.getDetails(assessmentId, institutionId);
    } catch (error) {
      throw new Error(`Failed to get assessment details: ${error.message}`);
    }
  };
  const publishAssessment = async (assessmentId, publishedBy, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.publish(assessmentId, publishedBy, institutionId);
    } catch (error) {
      throw new Error(`Failed to publish assessment: ${error.message}`);
    }
  };
  const assignAssessmentToBatches = async (assessmentId, batchIds, assignedBy, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.assignToBatches(assessmentId, batchIds, assignedBy, institutionId);
    } catch (error) {
      throw new Error(`Failed to assign assessment to batches: ${error.message}`);
    }
  };
  const deleteAssessment = async (assessmentId, deletedBy, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.delete(assessmentId, deletedBy, institutionId);
    } catch (error) {
      throw new Error(`Failed to delete assessment: ${error.message}`);
    }
  };
  const getUserAccessibleBatches = async (userId, userRole, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.getUserAccessibleBatches(userId, userRole, institutionId);
    } catch (error) {
      throw new Error(`Failed to get user accessible batches: ${error.message}`);
    }
  };
  const getAssessmentsForBatch = async (batchId, userId, userRole) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.getAssessmentsForBatch(batchId, userId, userRole);
    } catch (error) {
      throw new Error(`Failed to get assessments for batch: ${error.message}`);
    }
  };
  const getBatchStudentsWithProgress = async (batchId, assessmentId, userId, userRole) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.getBatchStudentsWithProgress(batchId, assessmentId, userId, userRole);
    } catch (error) {
      throw new Error(`Failed to get batch students with progress: ${error.message}`);
    }
  };
  const getStudentAssessmentHistory = async (studentId, assessmentId, batchId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.getStudentAssessmentHistory(studentId, assessmentId, batchId);
    } catch (error) {
      throw new Error(`Failed to get student assessment history: ${error.message}`);
    }
  };
  const canStudentAccessAssessment = async (studentId, assessmentId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.canStudentAccessAssessment(studentId, assessmentId);
    } catch (error) {
      throw new Error(`Failed to check student assessment access: ${error.message}`);
    }
  };
  const generateAssessmentForm = async (assessmentId, teacherId, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.generateAssessmentForm(assessmentId, teacherId, institutionId);
    } catch (error) {
      throw new Error(`Failed to generate assessment form: ${error.message}`);
    }
  };
  const uploadStudentResponse = async (assessmentId, studentId, responses, uploadedBy, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.uploadStudentResponse(assessmentId, studentId, responses, uploadedBy, institutionId);
    } catch (error) {
      throw new Error(`Failed to upload student response: ${error.message}`);
    }
  };
  const getStudentStatistics = async (studentId, assessmentId, teacherId, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.getStudentStatistics(studentId, assessmentId, teacherId, institutionId);
    } catch (error) {
      throw new Error(`Failed to get student statistics: ${error.message}`);
    }
  };
  const getBatchCompletionStatus = async (batchId, assessmentId, teacherId, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.getBatchCompletionStatus(batchId, assessmentId, teacherId, institutionId);
    } catch (error) {
      throw new Error(`Failed to get batch completion status: ${error.message}`);
    }
  };
  const processAssessmentForPeerReview = async (assessmentId, batchId, processedBy, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.processForPeerReview(assessmentId, batchId, processedBy, institutionId);
    } catch (error) {
      throw new Error(`Failed to process assessment for peer review: ${error.message}`);
    }
  };
  const canProcessAssessment = async (assessmentId, batchId, teacherId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.canProcess(assessmentId, batchId, teacherId);
    } catch (error) {
      throw new Error(`Failed to check if assessment can be processed: ${error.message}`);
    }
  };
  const getBatchSubmissionStatus = async (batchId, assessmentId, teacherId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.getBatchSubmissionStatus(batchId, assessmentId, teacherId);
    } catch (error) {
      throw new Error(`Failed to get batch submission status: ${error.message}`);
    }
  };
  const getAssessmentStatistics = async (assessmentId, batchId, teacherId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.assessments.getStatistics(assessmentId, batchId, teacherId);
    } catch (error) {
      throw new Error(`Failed to get assessment statistics: ${error.message}`);
    }
  };
  const getUserSettings = async (userId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.settings.getUserSettings(userId);
    } catch (error) {
      throw new Error(`Failed to get user settings: ${error.message}`);
    }
  };
  const updateUserSetting = async (userId, settingKey, settingValue) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.settings.updateUserSetting(userId, settingKey, settingValue);
    } catch (error) {
      throw new Error(`Failed to update user setting: ${error.message}`);
    }
  };
  const getInstitutionDetails = async (institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.institution.getDetails(institutionId);
    } catch (error) {
      throw new Error(`Failed to get institution details: ${error.message}`);
    }
  };
  const updateInstitutionDetails = async (institutionId, institutionData, detailsData, updatedBy) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.institution.updateDetails(institutionId, institutionData, detailsData, updatedBy);
    } catch (error) {
      throw new Error(`Failed to update institution details: ${error.message}`);
    }
  };
  const getDashboardStatistics = async (userId, userRole, institutionId) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.dashboard.getStatistics(userId, userRole, institutionId);
    } catch (error) {
      throw new Error(`Failed to get dashboard statistics: ${error.message}`);
    }
  };
  const submitReview = async (reviewData) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.reviews.submit(reviewData);
    } catch (error) {
      throw new Error(`Failed to submit review: ${error.message}`);
    }
  };
  const getReviewStats = async (filters = {}) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.reviews.getStats(filters);
    } catch (error) {
      throw new Error(`Failed to get review stats: ${error.message}`);
    }
  };
  const getSetting = async (key) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.settings.get(key);
    } catch (error) {
      throw new Error(`Failed to get setting: ${error.message}`);
    }
  };
  const setSetting = async (key, value2) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.settings.set(key, value2);
    } catch (error) {
      throw new Error(`Failed to set setting: ${error.message}`);
    }
  };
  const getInstitution = async () => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.institution.get();
    } catch (error) {
      throw new Error(`Failed to get institution: ${error.message}`);
    }
  };
  const updateInstitution = async (institutionData, updatedBy) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.institution.update(institutionData, updatedBy);
    } catch (error) {
      throw new Error(`Failed to update institution: ${error.message}`);
    }
  };
  const uploadInstitutionLogo = async (logoData, updatedBy) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.institution.uploadLogo(logoData, updatedBy);
    } catch (error) {
      throw new Error(`Failed to upload logo: ${error.message}`);
    }
  };
  const removeInstitutionLogo = async (updatedBy) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.institution.removeLogo(updatedBy);
    } catch (error) {
      throw new Error(`Failed to remove logo: ${error.message}`);
    }
  };
  const getInstitutionLogoPath = async (logoFileName) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.institution.getLogoPath(logoFileName);
    } catch (error) {
      throw new Error(`Failed to get logo path: ${error.message}`);
    }
  };
  const generatePDFReport = async (reportData) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.pdf.generateReport(reportData);
    } catch (error) {
      throw new Error(`Failed to generate PDF report: ${error.message}`);
    }
  };
  const showSaveDialog = async (options) => {
    try {
      const electronAPI = getElectronAPI$1();
      return await electronAPI.file.showSaveDialog(options);
    } catch (error) {
      throw new Error(`Failed to show save dialog: ${error.message}`);
    }
  };
  const value = {
    // User operations
    createUser,
    getAllUsers,
    // Admin operations
    updateUser,
    resetUserPassword,
    deactivateUser,
    activateUser,
    generateSecurePassword,
    getAuditLog,
    // Batch operations
    createBatch,
    getAllBatches,
    createBatchWithStudents,
    checkBatchNameUnique,
    checkStudentIdsUnique,
    getBatchDetails,
    updateBatch,
    addStudentsToBatch,
    removeStudentFromBatch,
    archiveBatch,
    activateBatch,
    getBatchStatistics,
    // Assessment operations
    createAssessmentWithQuestions,
    checkAssessmentNameUnique,
    getAllAssessments,
    getAssessmentDetails,
    publishAssessment,
    assignAssessmentToBatches,
    deleteAssessment,
    // Assessment access operations
    getUserAccessibleBatches,
    getAssessmentsForBatch,
    getBatchStudentsWithProgress,
    getStudentAssessmentHistory,
    canStudentAccessAssessment,
    // Student management operations
    generateAssessmentForm,
    uploadStudentResponse,
    getStudentStatistics,
    // Assessment processing operations
    getBatchCompletionStatus,
    processAssessmentForPeerReview,
    canProcessAssessment,
    getBatchSubmissionStatus,
    // Assessment statistics operations
    getAssessmentStatistics,
    // Settings operations
    getUserSettings,
    updateUserSetting,
    // Institution operations
    getInstitutionDetails,
    updateInstitutionDetails,
    // Dashboard operations
    getDashboardStatistics,
    // Review operations
    submitReview,
    getReviewStats,
    // Settings operations
    getSetting,
    setSetting,
    // Institution operations
    getInstitution,
    updateInstitution,
    uploadInstitutionLogo,
    removeInstitutionLogo,
    getInstitutionLogoPath,
    // PDF operations
    generatePDFReport,
    // File operations
    showSaveDialog
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(DatabaseContext.Provider, { value, children }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/DatabaseContext.jsx",
    lineNumber: 632,
    columnNumber: 5
  }, globalThis);
};

const MESSAGE_TYPES = {
  SUCCESS: "success",
  ERROR: "error",
  WARNING: "warning",
  INFO: "info"
};
const MESSAGE_CONFIG = {
  [MESSAGE_TYPES.SUCCESS]: {
    icon: CheckCircle,
    bgColor: "bg-green-50",
    borderColor: "border-green-200",
    textColor: "text-green-800",
    iconColor: "text-green-400",
    defaultDuration: 5e3
  },
  [MESSAGE_TYPES.ERROR]: {
    icon: AlertCircle,
    bgColor: "bg-red-50",
    borderColor: "border-red-200",
    textColor: "text-red-800",
    iconColor: "text-red-400",
    defaultDuration: 8e3
  },
  [MESSAGE_TYPES.WARNING]: {
    icon: AlertTriangle,
    bgColor: "bg-yellow-50",
    borderColor: "border-yellow-200",
    textColor: "text-yellow-800",
    iconColor: "text-yellow-400",
    defaultDuration: 6e3
  },
  [MESSAGE_TYPES.INFO]: {
    icon: Info,
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200",
    textColor: "text-blue-800",
    iconColor: "text-blue-400",
    defaultDuration: 5e3
  }
};
const MessageContext = reactExports.createContext();
const useMessage = () => {
  const context = reactExports.useContext(MessageContext);
  if (!context) {
    throw new Error("useMessage must be used within a MessageProvider");
  }
  return context;
};
const MessageItem = ({ message, onDismiss, onAction }) => {
  const config = MESSAGE_CONFIG[message.type];
  const Icon = config.icon;
  const timeoutRef = reactExports.useRef();
  reactExports.useEffect(() => {
    if (message.autoDismiss && message.duration > 0) {
      timeoutRef.current = setTimeout(() => {
        onDismiss(message.id);
      }, message.duration);
    }
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [message.id, message.autoDismiss, message.duration, onDismiss]);
  const handleAction = (action) => {
    if (action.callback) {
      action.callback();
    }
    if (action.dismissAfter) {
      onDismiss(message.id);
    }
    if (onAction) {
      onAction(message.id, action);
    }
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `rounded-md p-4 mb-3 border ${config.bgColor} ${config.borderColor} shadow-sm animate-in slide-in-from-right duration-300`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Icon, { className: `h-5 w-5 ${config.iconColor}` }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
      lineNumber: 94,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
      lineNumber: 93,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3 flex-1", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `text-sm font-medium ${config.textColor}`, children: [
      message.title && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "font-semibold mb-1", children: message.title }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
        lineNumber: 99,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: message.text }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
        lineNumber: 101,
        columnNumber: 13
      }, globalThis),
      message.details && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-2 text-xs opacity-75", children: Array.isArray(message.details) ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "list-disc list-inside space-y-1", children: message.details.map((detail, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: detail }, index, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
        lineNumber: 108,
        columnNumber: 23
      }, globalThis)) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
        lineNumber: 106,
        columnNumber: 19
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: message.details }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
        lineNumber: 112,
        columnNumber: 19
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
        lineNumber: 104,
        columnNumber: 15
      }, globalThis),
      message.actions && message.actions.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-3 flex flex-wrap gap-2", children: message.actions.map((action, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: () => handleAction(action),
          className: `inline-flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors ${action.primary ? `bg-${message.type === "error" ? "red" : message.type === "success" ? "green" : message.type === "warning" ? "yellow" : "blue"}-600 text-white hover:bg-${message.type === "error" ? "red" : message.type === "success" ? "green" : message.type === "warning" ? "yellow" : "blue"}-700` : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"}`,
          disabled: action.loading,
          children: [
            action.loading && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RefreshCw, { className: "h-3 w-3 mr-1 animate-spin" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
              lineNumber: 130,
              columnNumber: 40
            }, globalThis),
            action.icon && !action.loading && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(action.icon, { className: "h-3 w-3 mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
              lineNumber: 131,
              columnNumber: 56
            }, globalThis),
            action.label,
            action.external && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ExternalLink, { className: "h-3 w-3 ml-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
              lineNumber: 133,
              columnNumber: 41
            }, globalThis)
          ]
        },
        index,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
          lineNumber: 120,
          columnNumber: 19
        },
        globalThis
      )) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
        lineNumber: 118,
        columnNumber: 15
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
      lineNumber: 97,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
      lineNumber: 96,
      columnNumber: 9
    }, globalThis),
    message.dismissible !== false && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-4 flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      "button",
      {
        onClick: () => onDismiss(message.id),
        className: `inline-flex rounded-md p-1.5 ${config.textColor} hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-gray-500`,
        children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "sr-only", children: "Dismiss" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
            lineNumber: 147,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-4 w-4" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
            lineNumber: 148,
            columnNumber: 15
          }, globalThis)
        ]
      },
      void 0,
      true,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
        lineNumber: 143,
        columnNumber: 13
      },
      globalThis
    ) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
      lineNumber: 142,
      columnNumber: 11
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
    lineNumber: 92,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
    lineNumber: 91,
    columnNumber: 5
  }, globalThis);
};
const MessagesContainer = ({ messages, onDismiss, onAction, position = "top-right" }) => {
  if (messages.length === 0) {
    return null;
  }
  const positionClasses = {
    "top-right": "fixed top-4 right-4 z-50 max-w-md",
    "top-left": "fixed top-4 left-4 z-50 max-w-md",
    "bottom-right": "fixed bottom-4 right-4 z-50 max-w-md",
    "bottom-left": "fixed bottom-4 left-4 z-50 max-w-md",
    "top-center": "fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md",
    "bottom-center": "fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md"
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: positionClasses[position], children: messages.map((message) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
    MessageItem,
    {
      message,
      onDismiss,
      onAction
    },
    message.id,
    false,
    {
      fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
      lineNumber: 173,
      columnNumber: 9
    },
    globalThis
  )) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
    lineNumber: 171,
    columnNumber: 5
  }, globalThis);
};
const MessageProvider = ({ children, maxMessages = 5, position = "top-right" }) => {
  const [messages, setMessages] = reactExports.useState([]);
  const messageIdRef = reactExports.useRef(0);
  const showMessage = reactExports.useCallback((type, text, options = {}) => {
    const id = ++messageIdRef.current;
    const config = MESSAGE_CONFIG[type];
    const message = {
      id,
      type,
      text,
      title: options.title,
      details: options.details,
      actions: options.actions,
      autoDismiss: options.autoDismiss !== false,
      duration: options.duration || config.defaultDuration,
      dismissible: options.dismissible !== false,
      persistent: options.persistent || false,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      ...options
    };
    setMessages((prev) => {
      const newMessages = [message, ...prev];
      const filteredMessages = newMessages.filter(
        (msg, index) => msg.persistent || index < maxMessages
      );
      return filteredMessages;
    });
    return id;
  }, [maxMessages]);
  const dismissMessage = reactExports.useCallback((id) => {
    setMessages((prev) => prev.filter((msg) => msg.id !== id));
  }, []);
  const dismissAll = reactExports.useCallback(() => {
    setMessages([]);
  }, []);
  const updateMessage = reactExports.useCallback((id, updates) => {
    setMessages((prev) => prev.map(
      (msg) => msg.id === id ? { ...msg, ...updates } : msg
    ));
  }, []);
  const onAction = reactExports.useCallback((messageId, action) => {
    console.log("Message action:", { messageId, action });
  }, []);
  const value = {
    messages,
    showMessage,
    dismissMessage,
    dismissAll,
    updateMessage,
    // Convenience methods
    showSuccess: (text, options) => showMessage(MESSAGE_TYPES.SUCCESS, text, options),
    showError: (text, options) => showMessage(MESSAGE_TYPES.ERROR, text, options),
    showWarning: (text, options) => showMessage(MESSAGE_TYPES.WARNING, text, options),
    showInfo: (text, options) => showMessage(MESSAGE_TYPES.INFO, text, options)
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(MessageContext.Provider, { value, children: [
    children,
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      MessagesContainer,
      {
        messages,
        onDismiss: dismissMessage,
        onAction,
        position
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
        lineNumber: 254,
        columnNumber: 7
      },
      globalThis
    )
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",
    lineNumber: 252,
    columnNumber: 5
  }, globalThis);
};

const MenuContext = reactExports.createContext();
const MenuProvider = ({ children }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showMessage } = useMessage();
  reactExports.useEffect(() => {
    if (window.electronAPI && user) {
      window.electronAPI.invoke("menu:setUserContext", user);
    }
  }, [user]);
  const handleMenuNavigation = reactExports.useCallback((path) => {
    navigate(path);
  }, [navigate]);
  const handleMenuAction = reactExports.useCallback((action) => {
    switch (action) {
      case "save-assessment":
        window.dispatchEvent(new CustomEvent("menu:save-assessment"));
        break;
      case "show-search":
        window.dispatchEvent(new CustomEvent("menu:show-search"));
        break;
      default:
        console.log("Unhandled menu action:", action);
    }
  }, []);
  const handleExportData = reactExports.useCallback(async ({ type, filePath }) => {
    try {
      showMessage("Exporting data...", "info");
      switch (type) {
        case "assessment-results":
          await exportAssessmentResults(filePath);
          break;
        case "student-data":
          await exportStudentData(filePath);
          break;
        case "reports":
          await exportReports(filePath);
          break;
        default:
          throw new Error(`Unknown export type: ${type}`);
      }
      showMessage("Data exported successfully!", "success");
    } catch (error) {
      showMessage(`Export failed: ${error.message}`, "error");
    }
  }, [showMessage]);
  const handleImportData = reactExports.useCallback(async ({ type, filePath }) => {
    try {
      showMessage("Importing data...", "info");
      switch (type) {
        case "student-data":
          await importStudentData(filePath);
          break;
        case "assessment-data":
          await importAssessmentData(filePath);
          break;
        default:
          throw new Error(`Unknown import type: ${type}`);
      }
      showMessage("Data imported successfully!", "success");
    } catch (error) {
      showMessage(`Import failed: ${error.message}`, "error");
    }
  }, [showMessage]);
  const handleCreateBackup = reactExports.useCallback(async ({ filePath }) => {
    try {
      showMessage("Creating backup...", "info");
      const result = await window.electronAPI.invoke("backup:create", { filePath });
      if (result.success) {
        showMessage("Backup created successfully!", "success");
      } else {
        throw new Error(result.error || "Backup creation failed");
      }
    } catch (error) {
      showMessage(`Backup failed: ${error.message}`, "error");
    }
  }, [showMessage]);
  const handleRestoreBackup = reactExports.useCallback(async ({ filePath }) => {
    try {
      showMessage("Restoring backup...", "info");
      const result = await window.electronAPI.invoke("backup:restore", { filePath });
      if (result.success) {
        showMessage("Backup restored successfully! Please restart the application.", "success");
      } else {
        throw new Error(result.error || "Backup restoration failed");
      }
    } catch (error) {
      showMessage(`Restore failed: ${error.message}`, "error");
    }
  }, [showMessage]);
  const handleShowTutorial = reactExports.useCallback(() => {
    navigate("/tutorial");
  }, [navigate]);
  const handleShowShortcuts = reactExports.useCallback((shortcuts) => {
    window.dispatchEvent(new CustomEvent("menu:show-shortcuts", { detail: shortcuts }));
  }, []);
  const handleCheckUpdates = reactExports.useCallback(async () => {
    try {
      showMessage("Checking for updates...", "info");
      const result = await window.electronAPI.invoke("updater:checkForUpdates");
      if (result.updateAvailable) {
        showMessage("Update available! Download will start automatically.", "success");
      } else {
        showMessage("You are using the latest version.", "info");
      }
    } catch (error) {
      showMessage(`Update check failed: ${error.message}`, "error");
    }
  }, [showMessage]);
  const handleShowAbout = reactExports.useCallback((aboutInfo) => {
    window.dispatchEvent(new CustomEvent("menu:show-about", { detail: aboutInfo }));
  }, []);
  reactExports.useEffect(() => {
    if (!window.electronAPI) {
      return;
    }
    const handleMenuNavigate = (_, path) => handleMenuNavigation(path);
    const handleMenuActionEvent = (_, action) => handleMenuAction(action);
    const handleExportDataEvent = (_, data) => handleExportData(data);
    const handleImportDataEvent = (_, data) => handleImportData(data);
    const handleCreateBackupEvent = (_, data) => handleCreateBackup(data);
    const handleRestoreBackupEvent = (_, data) => handleRestoreBackup(data);
    const handleShowTutorialEvent = () => handleShowTutorial();
    const handleShowShortcutsEvent = (_, shortcuts) => handleShowShortcuts(shortcuts);
    const handleCheckUpdatesEvent = () => handleCheckUpdates();
    const handleShowAboutEvent = (_, aboutInfo) => handleShowAbout(aboutInfo);
    window.electronAPI.on("menu:navigate", handleMenuNavigate);
    window.electronAPI.on("menu:action", handleMenuActionEvent);
    window.electronAPI.on("menu:export-data", handleExportDataEvent);
    window.electronAPI.on("menu:import-data", handleImportDataEvent);
    window.electronAPI.on("menu:create-backup", handleCreateBackupEvent);
    window.electronAPI.on("menu:restore-backup", handleRestoreBackupEvent);
    window.electronAPI.on("menu:show-tutorial", handleShowTutorialEvent);
    window.electronAPI.on("menu:show-shortcuts", handleShowShortcutsEvent);
    window.electronAPI.on("menu:check-updates", handleCheckUpdatesEvent);
    window.electronAPI.on("menu:show-about", handleShowAboutEvent);
    return () => {
      window.electronAPI.removeAllListeners("menu:navigate");
      window.electronAPI.removeAllListeners("menu:action");
      window.electronAPI.removeAllListeners("menu:export-data");
      window.electronAPI.removeAllListeners("menu:import-data");
      window.electronAPI.removeAllListeners("menu:create-backup");
      window.electronAPI.removeAllListeners("menu:restore-backup");
      window.electronAPI.removeAllListeners("menu:show-tutorial");
      window.electronAPI.removeAllListeners("menu:show-shortcuts");
      window.electronAPI.removeAllListeners("menu:check-updates");
      window.electronAPI.removeAllListeners("menu:show-about");
    };
  }, [
    handleMenuNavigation,
    handleMenuAction,
    handleExportData,
    handleImportData,
    handleCreateBackup,
    handleRestoreBackup,
    handleShowTutorial,
    handleShowShortcuts,
    handleCheckUpdates,
    handleShowAbout
  ]);
  const value = {
    handleMenuNavigation,
    handleMenuAction,
    handleExportData,
    handleImportData,
    handleCreateBackup,
    handleRestoreBackup,
    handleShowTutorial,
    handleShowShortcuts,
    handleCheckUpdates,
    handleShowAbout
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(MenuContext.Provider, { value, children }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/contexts/MenuContext.jsx",
    lineNumber: 250,
    columnNumber: 5
  }, globalThis);
};
async function exportAssessmentResults(filePath) {
  console.log("Exporting assessment results to:", filePath);
}
async function exportStudentData(filePath) {
  console.log("Exporting student data to:", filePath);
}
async function exportReports(filePath) {
  console.log("Exporting reports to:", filePath);
}
async function importStudentData(filePath) {
  console.log("Importing student data from:", filePath);
}
async function importAssessmentData(filePath) {
  console.log("Importing assessment data from:", filePath);
}

const WelcomeScreen = () => {
  const navigate = useNavigate();
  const roleCards = [
    {
      id: "super_admin",
      // Internal role remains super_admin for full permissions
      title: "Administrator",
      description: "Full system access with assessment creation, user management, and system administration",
      icon: Shield,
      color: "purple",
      features: ["Create Assessments", "Manage Users", "System Settings", "Institution Management", "Full Access"],
      path: "/dashboard?role=super_admin"
    },
    {
      id: "teacher",
      title: "Teacher",
      description: "Manage batches, conduct assessments, and view student progress",
      icon: GraduationCap,
      color: "green",
      features: ["Batch Management", "Take Assessments", "Student Progress", "Reports"],
      path: "/dashboard?role=teacher"
    },
    {
      id: "student",
      title: "Student",
      description: "Participate in peer review assessments and view results",
      icon: BookOpen,
      color: "blue",
      features: ["Take Assessments", "View Results", "Peer Reviews", "Progress Tracking"],
      path: "/dashboard?role=student"
    }
  ];
  const handleRoleSelection = (role) => {
    localStorage.setItem("selectedRole", role.id);
    navigate(role.path);
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-6xl w-full", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center mb-12", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-center mb-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Building, { className: "h-16 w-16 text-blue-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
          lineNumber: 61,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-4xl font-bold text-gray-900", children: "Peer Review System" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
          lineNumber: 62,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
        lineNumber: 60,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
        lineNumber: 59,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-center mb-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Sparkles, { className: "h-10 w-10 text-blue-600" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
        lineNumber: 67,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
        lineNumber: 66,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
        lineNumber: 65,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-3xl font-bold text-gray-900 mb-4", children: "Welcome to Peer Review System" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
        lineNumber: 70,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xl text-gray-600 mb-8 max-w-3xl mx-auto", children: "A comprehensive offline desktop application for peer review assessments in educational institutions. Choose your role to get started." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
        lineNumber: 73,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
      lineNumber: 58,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8 mb-12", children: roleCards.map((role) => {
      const Icon = role.icon;
      const colorClasses = {
        purple: "from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700",
        blue: "from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700",
        green: "from-green-500 to-green-600 hover:from-green-600 hover:to-green-700",
        orange: "from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700"
      };
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: () => handleRoleSelection(role),
          className: `
                  relative p-8 rounded-2xl shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl
                  bg-gradient-to-br ${colorClasses[role.color]} text-white text-left group
                `,
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-start space-x-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "w-16 h-16 bg-white bg-opacity-20 rounded-xl flex items-center justify-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Icon, { className: "h-8 w-8 text-white" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
              lineNumber: 102,
              columnNumber: 23
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
              lineNumber: 101,
              columnNumber: 21
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
              lineNumber: 100,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1 min-w-0", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-2xl font-bold text-white mb-2", children: role.title }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
                lineNumber: 106,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-white text-opacity-90 mb-4 leading-relaxed", children: role.description }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
                lineNumber: 109,
                columnNumber: 21
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-2", children: role.features.map((feature, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "w-1.5 h-1.5 bg-white rounded-full" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
                  lineNumber: 115,
                  columnNumber: 27
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm text-white text-opacity-80", children: feature }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
                  lineNumber: 116,
                  columnNumber: 27
                }, globalThis)
              ] }, index, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
                lineNumber: 114,
                columnNumber: 25
              }, globalThis)) }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
                lineNumber: 112,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
              lineNumber: 105,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowRight, { className: "h-6 w-6 text-white text-opacity-60 group-hover:text-opacity-100 transition-opacity" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
              lineNumber: 122,
              columnNumber: 21
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
              lineNumber: 121,
              columnNumber: 19
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
            lineNumber: 99,
            columnNumber: 17
          }, globalThis)
        },
        role.id,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
          lineNumber: 91,
          columnNumber: 15
        },
        globalThis
      );
    }) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
      lineNumber: 80,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-500 text-sm", children: "Select your role to access the appropriate dashboard and features" }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
      lineNumber: 132,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
      lineNumber: 131,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
    lineNumber: 56,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",
    lineNumber: 55,
    columnNumber: 5
  }, globalThis);
};

const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = reactExports.useState(false);
  const { user, logout, isAdmin, isSuperAdmin, isTeacher } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const handleLogout = () => {
    logout();
    navigate("/login");
  };
  const navigationItems = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: Home,
      show: true
    },
    {
      name: "Admin Panel",
      href: "/admin",
      icon: Users,
      show: isAdmin
    },
    {
      name: "Batch Management",
      href: "/batches",
      icon: UserPlus,
      show: isTeacher || isAdmin
    },
    {
      name: "Create Assessment",
      href: "/create-assessment",
      icon: FileText,
      show: isSuperAdmin
    },
    {
      name: "Assessment Management",
      href: "/assessments",
      icon: ClipboardList,
      show: isSuperAdmin
    },
    {
      name: "Take Assessment",
      href: "/take-assessment",
      icon: ClipboardList,
      show: user?.role === "teacher" || user?.role === "student"
    },
    {
      name: "Settings",
      href: "/settings",
      icon: Settings$1,
      show: true
    }
  ];
  const visibleItems = navigationItems.filter((item) => item.show);
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex h-screen bg-gray-100", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `${sidebarOpen ? "translate-x-0" : "-translate-x-full"} fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between h-16 px-4 border-b", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Building, { className: "h-8 w-8 text-primary-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
            lineNumber: 81,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-xl font-bold text-gray-900", children: "Peer Review" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
            lineNumber: 82,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
          lineNumber: 80,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setSidebarOpen(false),
            className: "lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-6 w-6" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
              lineNumber: 88,
              columnNumber: 13
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
            lineNumber: 84,
            columnNumber: 11
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 79,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("nav", { className: "mt-5 px-2", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-1", children: visibleItems.map((item) => {
        const Icon = item.icon;
        const isActive = location.pathname === item.href;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => {
              navigate(item.href);
              setSidebarOpen(false);
            },
            className: `${isActive ? "bg-primary-100 border-primary-500 text-primary-700" : "border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900"} group flex items-center px-2 py-2 text-sm font-medium rounded-md border-l-4 w-full text-left transition-colors duration-150`,
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                Icon,
                {
                  className: `${isActive ? "text-primary-500" : "text-gray-400 group-hover:text-gray-500"} mr-3 h-6 w-6`
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
                  lineNumber: 111,
                  columnNumber: 19
                },
                globalThis
              ),
              item.name
            ]
          },
          item.name,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
            lineNumber: 99,
            columnNumber: 17
          },
          globalThis
        );
      }) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 93,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 92,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "absolute bottom-0 left-0 right-0 p-4 border-t", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3 mb-3", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-white font-medium", children: [
            user?.first_name?.[0],
            user?.last_name?.[0]
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
            lineNumber: 128,
            columnNumber: 17
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
            lineNumber: 127,
            columnNumber: 15
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
            lineNumber: 126,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1 min-w-0", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm font-medium text-gray-900 truncate", children: [
              user?.first_name,
              " ",
              user?.last_name
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
              lineNumber: 134,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-500 truncate capitalize", children: user?.role?.replace("_", " ") }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
              lineNumber: 137,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
            lineNumber: 133,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
          lineNumber: 125,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: handleLogout,
            className: "flex items-center w-full px-2 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LogOut, { className: "mr-3 h-5 w-5" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
                lineNumber: 146,
                columnNumber: 13
              }, globalThis),
              "Sign out"
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
            lineNumber: 142,
            columnNumber: 11
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 124,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
      lineNumber: 78,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1 flex flex-col overflow-hidden", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("header", { className: "bg-white shadow-sm border-b border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between h-16 px-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setSidebarOpen(true),
            className: "lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Menu, { className: "h-6 w-6" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
              lineNumber: 161,
              columnNumber: 15
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
            lineNumber: 157,
            columnNumber: 13
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1 lg:ml-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-2xl font-semibold text-gray-900", children: visibleItems.find((item) => item.href === location.pathname)?.name || "Dashboard" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
          lineNumber: 165,
          columnNumber: 15
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
          lineNumber: 164,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 156,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 155,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("main", { className: "flex-1 overflow-y-auto bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "py-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Outlet, {}, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 176,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 175,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 174,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 173,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
      lineNumber: 153,
      columnNumber: 7
    }, globalThis),
    sidebarOpen && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      "div",
      {
        className: "fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden",
        onClick: () => setSidebarOpen(false)
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
        lineNumber: 184,
        columnNumber: 9
      },
      globalThis
    )
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",
    lineNumber: 76,
    columnNumber: 5
  }, globalThis);
};

const LoadingSpinner = ({ size = "medium", text = "Loading..." }) => {
  const sizeClasses = {
    small: "h-4 w-4",
    medium: "h-8 w-8",
    large: "h-12 w-12"
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex flex-col items-center justify-center min-h-screen", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `animate-spin rounded-full border-b-2 border-primary-600 ${sizeClasses[size]}` }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/LoadingSpinner.jsx",
      lineNumber: 12,
      columnNumber: 7
    }, globalThis),
    text && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-4 text-sm text-gray-600", children: text }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/LoadingSpinner.jsx",
      lineNumber: 14,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/LoadingSpinner.jsx",
    lineNumber: 11,
    columnNumber: 5
  }, globalThis);
};

const useElectronAPI = () => {
  const [isElectronReady, setIsElectronReady] = reactExports.useState(false);
  const [electronAPI, setElectronAPI] = reactExports.useState(null);
  const [error, setError] = reactExports.useState(null);

  reactExports.useEffect(() => {
    const checkElectronAPI = () => {
      if (typeof window !== 'undefined' && window.electronAPI) {
        setElectronAPI(window.electronAPI);
        setIsElectronReady(true);
        setError(null);
      } else {
        setError('Electron API not available. Please ensure you are running the desktop application.');
      }
    };

    // Check immediately
    checkElectronAPI();

    // Also check after a short delay in case the API is still loading
    const timeout = setTimeout(checkElectronAPI, 1000);

    return () => clearTimeout(timeout);
  }, []);

  return {
    isElectronReady,
    electronAPI,
    error,
    isElectron: !!electronAPI
  };
};

// Helper function to get Electron API directly
const getElectronAPI = () => {
  if (typeof window !== 'undefined' && window.electronAPI) {
    return window.electronAPI;
  }
  return null;
};

const ElectronWrapper = ({ children }) => {
  const { isElectronReady, error, isElectron } = useElectronAPI();
  if (!isElectronReady && !error) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      LoadingSpinner,
      {
        text: "Initializing application...",
        size: "large"
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
        lineNumber: 12,
        columnNumber: 7
      },
      globalThis
    );
  }
  if (error || !isElectron) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-h-screen flex items-center justify-center bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-md w-full bg-white shadow-lg rounded-lg p-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center mb-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-8 w-8 text-red-500 mr-3" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
          lineNumber: 25,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-xl font-bold text-gray-900", children: "Application Error" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
          lineNumber: 26,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
        lineNumber: 24,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: error || "The Peer Review System requires the desktop application to function properly." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
          lineNumber: 30,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-yellow-50 border border-yellow-200 rounded-md p-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-sm font-medium text-yellow-800 mb-2", children: "Troubleshooting Steps:" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
            lineNumber: 35,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-yellow-700 space-y-1", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Ensure you're running the Electron desktop application" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
              lineNumber: 39,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Try restarting the application" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
              lineNumber: 40,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Check that all dependencies are properly installed" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
              lineNumber: 41,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Contact support if the issue persists" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
              lineNumber: 42,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
            lineNumber: 38,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
          lineNumber: 34,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => window.location.reload(),
            className: "w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",
            children: "Retry"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
            lineNumber: 46,
            columnNumber: 13
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
        lineNumber: 29,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
      lineNumber: 23,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",
      lineNumber: 22,
      columnNumber: 7
    }, globalThis);
  }
  return children;
};

const AboutModal = () => {
  const [isOpen, setIsOpen] = reactExports.useState(false);
  const [aboutInfo, setAboutInfo] = reactExports.useState(null);
  reactExports.useEffect(() => {
    const handleShowAbout = (event) => {
      setAboutInfo(event.detail);
      setIsOpen(true);
    };
    window.addEventListener("menu:show-about", handleShowAbout);
    return () => {
      window.removeEventListener("menu:show-about", handleShowAbout);
    };
  }, []);
  const handleClose = () => {
    setIsOpen(false);
    setAboutInfo(null);
  };
  const handleLinkClick = (url) => {
    if (window.electronAPI) {
      window.electronAPI.invoke("shell:openExternal", url);
    } else {
      window.open(url, "_blank");
    }
  };
  if (!isOpen || !aboutInfo) {
    return null;
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between p-6 border-b border-gray-200", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Info, { className: "w-6 h-6 text-white" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 53,
          columnNumber: 15
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 52,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-xl font-semibold text-gray-900", children: "About" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 56,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: aboutInfo.name }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 57,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 55,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
        lineNumber: 51,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: handleClose,
          className: "p-2 hover:bg-gray-100 rounded-lg transition-colors",
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "w-5 h-5 text-gray-500" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 64,
            columnNumber: 13
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 60,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
      lineNumber: 50,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6 space-y-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-2xl font-bold text-gray-900 mb-2", children: aboutInfo.name }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 72,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-lg text-gray-600 mb-4", children: [
          "Version ",
          aboutInfo.version
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 75,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-700 max-w-lg mx-auto", children: aboutInfo.description }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 76,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
        lineNumber: 71,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-gray-50 rounded-lg p-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-semibold text-gray-900 mb-3 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Code, { className: "w-5 h-5 mr-2" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 84,
            columnNumber: 15
          }, globalThis),
          "Developer"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 83,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-700", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: aboutInfo.developer.name }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 89,
            columnNumber: 17
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 88,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                onClick: () => handleLinkClick(`mailto:${aboutInfo.developer.email}`),
                className: "flex items-center text-blue-600 hover:text-blue-800 transition-colors",
                children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Mail, { className: "w-4 h-4 mr-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
                    lineNumber: 96,
                    columnNumber: 19
                  }, globalThis),
                  aboutInfo.developer.email
                ]
              },
              void 0,
              true,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
                lineNumber: 92,
                columnNumber: 17
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                onClick: () => handleLinkClick(aboutInfo.developer.website),
                className: "flex items-center text-blue-600 hover:text-blue-800 transition-colors",
                children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Globe, { className: "w-4 h-4 mr-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
                    lineNumber: 103,
                    columnNumber: 19
                  }, globalThis),
                  "Portfolio",
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ExternalLink, { className: "w-3 h-3 ml-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
                    lineNumber: 105,
                    columnNumber: 19
                  }, globalThis)
                ]
              },
              void 0,
              true,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
                lineNumber: 99,
                columnNumber: 17
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 91,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 87,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
        lineNumber: 82,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-gray-50 rounded-lg p-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-semibold text-gray-900 mb-3 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Globe, { className: "w-5 h-5 mr-2" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 114,
            columnNumber: 15
          }, globalThis),
          "Company"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 113,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-700", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: aboutInfo.company.name }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 119,
            columnNumber: 17
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 118,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                onClick: () => handleLinkClick(`mailto:${aboutInfo.company.contact}`),
                className: "flex items-center text-blue-600 hover:text-blue-800 transition-colors",
                children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Mail, { className: "w-4 h-4 mr-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
                    lineNumber: 126,
                    columnNumber: 19
                  }, globalThis),
                  aboutInfo.company.contact
                ]
              },
              void 0,
              true,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
                lineNumber: 122,
                columnNumber: 17
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                onClick: () => handleLinkClick(aboutInfo.company.website),
                className: "flex items-center text-blue-600 hover:text-blue-800 transition-colors",
                children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Globe, { className: "w-4 h-4 mr-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
                    lineNumber: 133,
                    columnNumber: 19
                  }, globalThis),
                  "Website",
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ExternalLink, { className: "w-3 h-3 ml-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
                    lineNumber: 135,
                    columnNumber: 19
                  }, globalThis)
                ]
              },
              void 0,
              true,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
                lineNumber: 129,
                columnNumber: 17
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 121,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 117,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
        lineNumber: 112,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-gray-50 rounded-lg p-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-semibold text-gray-900 mb-3", children: "System Information" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 143,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-2 gap-4 text-sm", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: "Electron Version" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
              lineNumber: 148,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "font-mono text-gray-900", children: aboutInfo.electron }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
              lineNumber: 149,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 147,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: "Node.js Version" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
              lineNumber: 152,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "font-mono text-gray-900", children: aboutInfo.node }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
              lineNumber: 153,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 151,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: "Chrome Version" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
              lineNumber: 156,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "font-mono text-gray-900", children: aboutInfo.chrome }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
              lineNumber: 157,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 155,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: "License" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
              lineNumber: 160,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "font-mono text-gray-900", children: aboutInfo.license }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
              lineNumber: 161,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
            lineNumber: 159,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 146,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
        lineNumber: 142,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center text-sm text-gray-600 border-t border-gray-200 pt-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: aboutInfo.copyright }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 168,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1", children: "All rights reserved." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
          lineNumber: 169,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
        lineNumber: 167,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
      lineNumber: 69,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end p-6 border-t border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      "button",
      {
        onClick: handleClose,
        className: "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",
        children: "Close"
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
        lineNumber: 175,
        columnNumber: 11
      },
      globalThis
    ) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
      lineNumber: 174,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
    lineNumber: 48,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",
    lineNumber: 47,
    columnNumber: 5
  }, globalThis);
};

const KeyboardShortcutsModal = () => {
  const [isOpen, setIsOpen] = reactExports.useState(false);
  const [shortcuts, setShortcuts] = reactExports.useState([]);
  reactExports.useEffect(() => {
    const handleShowShortcuts = (event) => {
      setShortcuts(event.detail || []);
      setIsOpen(true);
    };
    window.addEventListener("menu:show-shortcuts", handleShowShortcuts);
    return () => {
      window.removeEventListener("menu:show-shortcuts", handleShowShortcuts);
    };
  }, []);
  const handleClose = () => {
    setIsOpen(false);
    setShortcuts([]);
  };
  const organizeShortcuts = (shortcuts2) => {
    const categories = {
      file: { name: "File Operations", icon: File, shortcuts: [] },
      edit: { name: "Edit Operations", icon: Settings$1, shortcuts: [] },
      view: { name: "View & Navigation", icon: Eye, shortcuts: [] },
      search: { name: "Search & Find", icon: Search, shortcuts: [] },
      help: { name: "Help & Support", icon: HelpCircle, shortcuts: [] }
    };
    shortcuts2.forEach((shortcut) => {
      const action = shortcut.action.toLowerCase();
      if (action.includes("new") || action.includes("open") || action.includes("save") || action.includes("print")) {
        categories.file.shortcuts.push(shortcut);
      } else if (action.includes("undo") || action.includes("redo") || action.includes("cut") || action.includes("copy") || action.includes("paste") || action.includes("preferences")) {
        categories.edit.shortcuts.push(shortcut);
      } else if (action.includes("dashboard") || action.includes("assessment") || action.includes("student") || action.includes("report") || action.includes("zoom") || action.includes("full screen") || action.includes("refresh")) {
        categories.view.shortcuts.push(shortcut);
      } else if (action.includes("find") || action.includes("search")) {
        categories.search.shortcuts.push(shortcut);
      } else {
        categories.help.shortcuts.push(shortcut);
      }
    });
    return categories;
  };
  const formatShortcut = (shortcut) => {
    return shortcut.replace("CmdOrCtrl", "Ctrl").replace("Plus", "+");
  };
  if (!isOpen) {
    return null;
  }
  const categorizedShortcuts = organizeShortcuts(shortcuts);
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between p-6 border-b border-gray-200", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Keyboard, { className: "w-6 h-6 text-white" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 80,
          columnNumber: 15
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 79,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-xl font-semibold text-gray-900", children: "Keyboard Shortcuts" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
            lineNumber: 83,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Quick access to all features" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
            lineNumber: 84,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 82,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
        lineNumber: 78,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: handleClose,
          className: "p-2 hover:bg-gray-100 rounded-lg transition-colors",
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "w-5 h-5 text-gray-500" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
            lineNumber: 91,
            columnNumber: 13
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 87,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
      lineNumber: 77,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: Object.entries(categorizedShortcuts).map(([key, category]) => {
        if (category.shortcuts.length === 0) {
          return null;
        }
        const IconComponent = category.icon;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-gray-50 rounded-lg p-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-semibold text-gray-900 mb-4 flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(IconComponent, { className: "w-5 h-5 mr-2 text-gray-600" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
              lineNumber: 106,
              columnNumber: 21
            }, globalThis),
            category.name
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
            lineNumber: 105,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-3", children: category.shortcuts.map((shortcut, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-700 text-sm", children: shortcut.action }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
              lineNumber: 112,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-1", children: formatShortcut(shortcut.shortcut).split("+").map((key2, keyIndex) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(React.Fragment, { children: [
              keyIndex > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-400 text-xs", children: "+" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
                lineNumber: 119,
                columnNumber: 33
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("kbd", { className: "px-2 py-1 bg-white border border-gray-300 rounded text-xs font-mono text-gray-800 shadow-sm", children: key2.trim() }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
                lineNumber: 121,
                columnNumber: 31
              }, globalThis)
            ] }, keyIndex, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
              lineNumber: 117,
              columnNumber: 29
            }, globalThis)) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
              lineNumber: 115,
              columnNumber: 25
            }, globalThis)
          ] }, index, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
            lineNumber: 111,
            columnNumber: 23
          }, globalThis)) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
            lineNumber: 109,
            columnNumber: 19
          }, globalThis)
        ] }, key, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 104,
          columnNumber: 17
        }, globalThis);
      }) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
        lineNumber: 97,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-6 bg-blue-50 rounded-lg p-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-md font-semibold text-blue-900 mb-2", children: "Tips" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 137,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-blue-800 space-y-1", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
            "• Use ",
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("kbd", { className: "px-1 py-0.5 bg-blue-100 rounded text-xs", children: "Ctrl" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
              lineNumber: 139,
              columnNumber: 25
            }, globalThis),
            " key combinations for quick access to common functions"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
            lineNumber: 139,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
            "• Function keys like ",
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("kbd", { className: "px-1 py-0.5 bg-blue-100 rounded text-xs", children: "F11" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
              lineNumber: 140,
              columnNumber: 40
            }, globalThis),
            " provide instant access to view options"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
            lineNumber: 140,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
            "• Number keys ",
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("kbd", { className: "px-1 py-0.5 bg-blue-100 rounded text-xs", children: "Ctrl+1-5" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
              lineNumber: 141,
              columnNumber: 33
            }, globalThis),
            " quickly navigate between main sections"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
            lineNumber: 141,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Most shortcuts work globally throughout the application" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
            lineNumber: 142,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 138,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
        lineNumber: 136,
        columnNumber: 11
      }, globalThis),
      shortcuts.length === 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Keyboard, { className: "w-16 h-16 text-gray-300 mx-auto mb-4" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 149,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "No Shortcuts Available" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 150,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: "Keyboard shortcuts will be displayed here when available." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 151,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
        lineNumber: 148,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
      lineNumber: 96,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center p-6 border-t border-gray-200", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: [
        "Press ",
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("kbd", { className: "px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs font-mono", children: "Ctrl+?" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 159,
          columnNumber: 19
        }, globalThis),
        " to open this dialog anytime"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
        lineNumber: 158,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: handleClose,
          className: "px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",
          children: "Close"
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
          lineNumber: 161,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
      lineNumber: 157,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
    lineNumber: 75,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",
    lineNumber: 74,
    columnNumber: 5
  }, globalThis);
};

const Dashboard = reactExports.lazy(() => __vitePreload(() => import('./Dashboardx-fa9229b1.js'),true?["./Dashboardx-fa9229b1.js","./chunk-2ef8e52b.js","./chunk-03d61bd9.js","./chunk-87c5b779.js","./chunk-028772a4.js"]:void 0,import.meta.url));
const AdminPanel = reactExports.lazy(() => __vitePreload(() => import('./AdminPanelx-690d83e1.js'),true?["./AdminPanelx-690d83e1.js","./chunk-2ef8e52b.js","./chunk-03d61bd9.js","./chunk-8b627984.js","./chunk-028772a4.js"]:void 0,import.meta.url));
const BatchManagement = reactExports.lazy(() => __vitePreload(() => import('./BatchManagementx-3f5280c6.js'),true?["./BatchManagementx-3f5280c6.js","./chunk-2ef8e52b.js","./chunk-03d61bd9.js","./chunk-4660e7bb.js","./chunk-8b627984.js","./chunk-028772a4.js"]:void 0,import.meta.url));
const CreateBatch = reactExports.lazy(() => __vitePreload(() => import('./CreateBatchx-07e66662.js'),true?["./CreateBatchx-07e66662.js","./chunk-2ef8e52b.js","./chunk-03d61bd9.js","./chunk-8b627984.js","./chunk-028772a4.js","./chunk-4660e7bb.js"]:void 0,import.meta.url));
const CreateAssessment = reactExports.lazy(() => __vitePreload(() => import('./CreateAssessmentx-5a9d09c5.js'),true?["./CreateAssessmentx-5a9d09c5.js","./chunk-2ef8e52b.js","./chunk-03d61bd9.js","./chunk-028772a4.js"]:void 0,import.meta.url));
const AssessmentManagement = reactExports.lazy(() => __vitePreload(() => import('./AssessmentManagementx-239a6f9b.js'),true?["./AssessmentManagementx-239a6f9b.js","./chunk-2ef8e52b.js","./chunk-03d61bd9.js","./chunk-028772a4.js"]:void 0,import.meta.url));
const TakeAssessment = reactExports.lazy(() => __vitePreload(() => import('./TakeAssessmentx-b85473cf.js'),true?["./TakeAssessmentx-b85473cf.js","./chunk-2ef8e52b.js","./chunk-03d61bd9.js","./chunk-028772a4.js","./chunk-87c5b779.js"]:void 0,import.meta.url));
const Settings = reactExports.lazy(() => __vitePreload(() => import('./Settingsx-765f5b6e.js'),true?["./Settingsx-765f5b6e.js","./chunk-2ef8e52b.js","./chunk-03d61bd9.js","./chunk-1466ddc5.js","./chunk-028772a4.js"]:void 0,import.meta.url));
const Institution = reactExports.lazy(() => __vitePreload(() => import('./Institutionx-db90ac5e.js'),true?["./Institutionx-db90ac5e.js","./chunk-2ef8e52b.js","./chunk-03d61bd9.js","./chunk-028772a4.js"]:void 0,import.meta.url));
const Tutorial = reactExports.lazy(() => __vitePreload(() => import('./Tutorialx-0fca7ecf.js'),true?["./Tutorialx-0fca7ecf.js","./chunk-2ef8e52b.js","./chunk-03d61bd9.js","./chunk-028772a4.js"]:void 0,import.meta.url));
function AppContent() {
  const { user, loading } = useAuth();
  if (loading) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
      lineNumber: 35,
      columnNumber: 12
    }, this);
  }
  if (!user) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(HashRouter, { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Routes, { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Route, { path: "*", element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(WelcomeScreen, {}, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
      lineNumber: 43,
      columnNumber: 36
    }, this) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
      lineNumber: 43,
      columnNumber: 11
    }, this) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
      lineNumber: 42,
      columnNumber: 9
    }, this) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
      lineNumber: 41,
      columnNumber: 7
    }, this);
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(HashRouter, { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(MenuProvider, { children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Routes, { children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Route, { path: "/", element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Layout, {}, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
        lineNumber: 53,
        columnNumber: 36
      }, this), children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Route, { index: true, element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Navigate, { to: "/dashboard", replace: true }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
          lineNumber: 54,
          columnNumber: 35
        }, this) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
          lineNumber: 54,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "dashboard",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 58,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Dashboard, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 59,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 58,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 55,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "admin",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 67,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AdminPanel, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 68,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 67,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 64,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "batches",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 76,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BatchManagement, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 77,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 76,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 73,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "create-batch",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 85,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CreateBatch, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 86,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 85,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 82,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "batch-management",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 94,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BatchManagement, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 95,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 94,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 91,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "create-assessment",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 103,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CreateAssessment, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 104,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 103,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 100,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "assessments",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 112,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AssessmentManagement, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 113,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 112,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 109,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "take-assessment",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 121,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(TakeAssessment, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 122,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 121,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 118,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "test",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 130,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(TakeAssessment, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 131,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 130,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 127,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "settings",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 139,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Settings, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 140,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 139,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 136,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "institution",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 148,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Institution, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 149,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 148,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 145,
            columnNumber: 13
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          Route,
          {
            path: "tutorial",
            element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(reactExports.Suspense, { fallback: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LoadingSpinner, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 157,
              columnNumber: 37
            }, this), children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Tutorial, {}, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 158,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
              lineNumber: 157,
              columnNumber: 17
            }, this)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
            lineNumber: 154,
            columnNumber: 13
          },
          this
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
        lineNumber: 53,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Route, { path: "*", element: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Navigate, { to: "/dashboard", replace: true }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
        lineNumber: 164,
        columnNumber: 36
      }, this) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
        lineNumber: 164,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
      lineNumber: 52,
      columnNumber: 9
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AboutModal, {}, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
      lineNumber: 168,
      columnNumber: 9
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(KeyboardShortcutsModal, {}, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
      lineNumber: 169,
      columnNumber: 9
    }, this)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
    lineNumber: 51,
    columnNumber: 7
  }, this) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
    lineNumber: 50,
    columnNumber: 5
  }, this);
}
function App() {
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ElectronWrapper, { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(DatabaseProvider, { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AuthProvider, { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(MessageProvider, { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-h-screen bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AppContent, {}, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
    lineNumber: 182,
    columnNumber: 15
  }, this) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
    lineNumber: 181,
    columnNumber: 13
  }, this) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
    lineNumber: 180,
    columnNumber: 11
  }, this) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
    lineNumber: 179,
    columnNumber: 9
  }, this) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
    lineNumber: 178,
    columnNumber: 7
  }, this) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/App.jsx",
    lineNumber: 177,
    columnNumber: 5
  }, this);
}

const index = '';

client.createRoot(document.getElementById("root")).render(
  React.createElement(
    React.StrictMode,
    null,
    React.createElement(App, null)
  )
);

export { useMessage as a, useDatabase as b, getElectronAPI as g, useAuth as u };
