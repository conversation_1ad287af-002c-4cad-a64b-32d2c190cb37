/**
 * Application Functionality Test Script
 * Tests actual application startup and role navigation
 */

const { spawn } = require('child_process');
const path = require('path');

class AppFunctionalityTester {
  constructor() {
    this.testResults = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async testDevelopmentMode() {
    this.log('🔧 Testing Development Mode');
    
    return new Promise((resolve) => {
      const devProcess = spawn('npx', ['electron', '.'], {
        env: { ...process.env, ELECTRON_DEV: 'true' },
        stdio: 'pipe'
      });

      let output = '';
      let hasDevMode = false;
      let hasSplash = false;
      let hasDatabase = false;
      let hasAppReady = false;

      devProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      devProcess.stderr.on('data', (data) => {
        output += data.toString();
        
        // Check for development mode indicators
        if (data.toString().includes('Development mode enabled')) {
          hasDevMode = true;
          this.log('✅ Development mode detected');
        }
        
        if (data.toString().includes('Splash screen ready')) {
          hasSplash = true;
          this.log('✅ Splash screen initialized');
        }
        
        if (data.toString().includes('Database initialization result')) {
          hasDatabase = true;
          this.log('✅ Database initialized');
        }
        
        if (data.toString().includes('Application ready and visible')) {
          hasAppReady = true;
          this.log('✅ Application ready');
        }
      });

      // Kill process after 15 seconds
      setTimeout(() => {
        devProcess.kill('SIGTERM');
        
        const results = {
          devMode: hasDevMode,
          splash: hasSplash,
          database: hasDatabase,
          appReady: hasAppReady,
          output: output
        };
        
        resolve(results);
      }, 15000);
    });
  }

  async testProductionMode() {
    this.log('🏭 Testing Production Mode');
    
    return new Promise((resolve) => {
      const prodProcess = spawn('npx', ['electron', '.'], {
        env: { ...process.env, NODE_ENV: 'production' },
        stdio: 'pipe'
      });

      let output = '';
      let hasDevMode = false;
      let hasDatabase = false;
      let hasAppReady = false;

      prodProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      prodProcess.stderr.on('data', (data) => {
        output += data.toString();
        
        // In production, should NOT have development mode
        if (data.toString().includes('Development mode enabled')) {
          hasDevMode = true;
        }
        
        if (data.toString().includes('Database initialization result')) {
          hasDatabase = true;
          this.log('✅ Database initialized in production');
        }
        
        if (data.toString().includes('Application ready and visible')) {
          hasAppReady = true;
          this.log('✅ Application ready in production');
        }
      });

      // Kill process after 15 seconds
      setTimeout(() => {
        prodProcess.kill('SIGTERM');
        
        const results = {
          devMode: hasDevMode,
          database: hasDatabase,
          appReady: hasAppReady,
          output: output
        };
        
        resolve(results);
      }, 15000);
    });
  }

  async runTests() {
    this.log('🚀 Starting Application Functionality Tests');
    this.log('===========================================');

    try {
      // Test Development Mode
      const devResults = await this.testDevelopmentMode();
      
      if (devResults.devMode) {
        this.log('✅ Development mode properly enabled', 'success');
      } else {
        this.log('❌ Development mode not detected', 'error');
      }

      if (devResults.database) {
        this.log('✅ Database initialization working', 'success');
      } else {
        this.log('❌ Database initialization failed', 'error');
      }

      if (devResults.appReady) {
        this.log('✅ Application startup successful', 'success');
      } else {
        this.log('❌ Application failed to start', 'error');
      }

      // Test Production Mode
      const prodResults = await this.testProductionMode();
      
      if (!prodResults.devMode) {
        this.log('✅ Production mode has no development features', 'success');
      } else {
        this.log('❌ Production mode still has development features', 'error');
      }

      if (prodResults.database) {
        this.log('✅ Production database initialization working', 'success');
      } else {
        this.log('❌ Production database initialization failed', 'error');
      }

      // Summary
      this.log('===========================================');
      this.log('📋 Test Summary:');
      this.log(`   Development Mode: ${devResults.devMode ? '✅' : '❌'}`);
      this.log(`   Database Init: ${devResults.database ? '✅' : '❌'}`);
      this.log(`   App Startup: ${devResults.appReady ? '✅' : '❌'}`);
      this.log(`   Production Security: ${!prodResults.devMode ? '✅' : '❌'}`);
      
      const allPassed = devResults.devMode && devResults.database && devResults.appReady && !prodResults.devMode;
      
      if (allPassed) {
        this.log('🎉 All functionality tests passed!', 'success');
      } else {
        this.log('⚠️ Some functionality tests failed. Check the logs above.', 'error');
      }

    } catch (error) {
      this.log(`❌ Test execution failed: ${error.message}`, 'error');
    }
  }
}

// Run tests
const tester = new AppFunctionalityTester();
tester.runTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
