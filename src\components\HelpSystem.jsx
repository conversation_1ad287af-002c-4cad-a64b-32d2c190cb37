import React, { useState, useEffect } from 'react';
import {
  HelpCircle,
  Search,
  Book,
  FileText,
  ChevronRight,
  ChevronDown,
  X,
  ExternalLink,
  MessageCircle,
  Lightbulb
} from 'lucide-react';

/**
 * Comprehensive help system with searchable documentation
 */
export const HelpSystem = ({ isOpen, onClose, initialTopic = null }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTopic, setSelectedTopic] = useState(initialTopic);
  const [expandedCategories, setExpandedCategories] = useState(new Set());

  // Help content structure
  const helpContent = {
    'getting-started': {
      title: 'Getting Started',
      icon: Lightbulb,
      topics: {
        'first-login': {
          title: 'First Time Login',
          content: `
            <h3>Welcome to the Peer Review System</h3>
            <p>Follow these steps to get started:</p>
            <ol>
              <li>Use your provided username and password to log in</li>
              <li>You'll be prompted to change your password on first login</li>
              <li>Complete your profile information</li>
              <li>Explore the dashboard to familiarize yourself with the interface</li>
            </ol>
            <div class="bg-blue-50 p-4 rounded-lg mt-4">
              <p><strong>Tip:</strong> Keep your login credentials secure and never share them with others.</p>
            </div>
          `
        },
        'navigation': {
          title: 'Navigation Basics',
          content: `
            <h3>Navigating the Application</h3>
            <p>The application is organized into several main sections:</p>
            <ul>
              <li><strong>Dashboard:</strong> Overview of your activities and statistics</li>
              <li><strong>Assessments:</strong> Create and manage peer review assessments</li>
              <li><strong>Batches:</strong> Organize students into groups</li>
              <li><strong>Settings:</strong> Configure your preferences and account</li>
            </ul>
            <p>Use the sidebar navigation to move between sections. The current page is highlighted in blue.</p>
          `
        }
      }
    },
    'assessments': {
      title: 'Assessment Management',
      icon: FileText,
      topics: {
        'creating-assessments': {
          title: 'Creating Assessments',
          content: `
            <h3>How to Create a New Assessment</h3>
            <ol>
              <li>Navigate to the Assessments section</li>
              <li>Click "Create New Assessment"</li>
              <li>Fill in the assessment details (name, description, instructions)</li>
              <li>Add questions using the question management interface</li>
              <li>Review and publish the assessment</li>
            </ol>
            <div class="bg-yellow-50 p-4 rounded-lg mt-4">
              <p><strong>Note:</strong> Only Super Admins can create assessments. Teachers can assign existing assessments to their batches.</p>
            </div>
          `
        },
        'question-types': {
          title: 'Question Types',
          content: `
            <h3>Supported Question Types</h3>
            <p>The system supports multiple-choice questions (MCQ) with the following features:</p>
            <ul>
              <li>Multiple correct answers per question</li>
              <li>Rich text formatting in questions and options</li>
              <li>Image support for visual questions</li>
              <li>Customizable point values</li>
            </ul>
            <p>Each question can have 2-6 answer options, with any number of them being correct.</p>
          `
        }
      }
    },
    'batch-management': {
      title: 'Batch Management',
      icon: Book,
      topics: {
        'creating-batches': {
          title: 'Creating Student Batches',
          content: `
            <h3>Setting Up Student Batches</h3>
            <p>Batches help organize students for assessments:</p>
            <ol>
              <li>Go to Batch Management</li>
              <li>Click "Create New Batch"</li>
              <li>Enter batch details (name, description, academic year)</li>
              <li>Add students manually or import from CSV</li>
              <li>Review and save the batch</li>
            </ol>
            <div class="bg-green-50 p-4 rounded-lg mt-4">
              <p><strong>CSV Import:</strong> Use the provided template to bulk import student data.</p>
            </div>
          `
        },
        'managing-students': {
          title: 'Managing Students',
          content: `
            <h3>Student Management Features</h3>
            <ul>
              <li>Add individual students to batches</li>
              <li>Remove students from batches</li>
              <li>View student progress and statistics</li>
              <li>Generate student reports</li>
            </ul>
            <p>Students are automatically created user accounts when added to batches.</p>
          `
        }
      }
    },
    'troubleshooting': {
      title: 'Troubleshooting',
      icon: HelpCircle,
      topics: {
        'common-issues': {
          title: 'Common Issues',
          content: `
            <h3>Frequently Encountered Problems</h3>
            <div class="space-y-4">
              <div>
                <h4 class="font-semibold">Login Problems</h4>
                <ul>
                  <li>Check your username and password</li>
                  <li>Ensure Caps Lock is off</li>
                  <li>Contact your administrator for password reset</li>
                </ul>
              </div>
              <div>
                <h4 class="font-semibold">Performance Issues</h4>
                <ul>
                  <li>Close other applications to free up memory</li>
                  <li>Restart the application</li>
                  <li>Check for available updates</li>
                </ul>
              </div>
            </div>
          `
        },
        'error-messages': {
          title: 'Understanding Error Messages',
          content: `
            <h3>Common Error Messages</h3>
            <div class="space-y-3">
              <div class="border-l-4 border-red-500 pl-4">
                <p class="font-semibold">Database Error</p>
                <p>Usually indicates a temporary connection issue. Try refreshing the page.</p>
              </div>
              <div class="border-l-4 border-yellow-500 pl-4">
                <p class="font-semibold">Validation Error</p>
                <p>Check that all required fields are filled correctly.</p>
              </div>
              <div class="border-l-4 border-blue-500 pl-4">
                <p class="font-semibold">Permission Denied</p>
                <p>You don't have permission for this action. Contact your administrator.</p>
              </div>
            </div>
          `
        }
      }
    }
  };

  // Filter content based on search term
  const filteredContent = React.useMemo(() => {
    if (!searchTerm) {return helpContent;}

    const filtered = {};
    Object.entries(helpContent).forEach(([categoryKey, category]) => {
      const matchingTopics = {};
      Object.entries(category.topics).forEach(([topicKey, topic]) => {
        if (
          topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          topic.content.toLowerCase().includes(searchTerm.toLowerCase())
        ) {
          matchingTopics[topicKey] = topic;
        }
      });

      if (Object.keys(matchingTopics).length > 0) {
        filtered[categoryKey] = {
          ...category,
          topics: matchingTopics
        };
      }
    });

    return filtered;
  }, [searchTerm]);

  // Auto-expand categories when searching
  useEffect(() => {
    if (searchTerm) {
      setExpandedCategories(new Set(Object.keys(filteredContent)));
    }
  }, [searchTerm, filteredContent]);

  // Set initial topic if provided
  useEffect(() => {
    if (initialTopic) {
      setSelectedTopic(initialTopic);
      // Find and expand the category containing this topic
      Object.entries(helpContent).forEach(([categoryKey, category]) => {
        if (category.topics[initialTopic]) {
          setExpandedCategories(prev => new Set([...prev, categoryKey]));
        }
      });
    }
  }, [initialTopic]);

  const toggleCategory = (categoryKey) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryKey)) {
        newSet.delete(categoryKey);
      } else {
        newSet.add(categoryKey);
      }
      return newSet;
    });
  };

  const selectTopic = (topicKey) => {
    setSelectedTopic(topicKey);
  };

  if (!isOpen) {return null;}

  const currentTopic = selectedTopic ?
    Object.values(helpContent)
      .flatMap(category => Object.entries(category.topics))
      .find(([key]) => key === selectedTopic)?.[1]
    : null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full h-5/6 flex overflow-hidden">
        {/* Sidebar */}
        <div className="w-1/3 border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Help & Documentation</h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search help topics..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto p-4">
            {Object.entries(filteredContent).map(([categoryKey, category]) => {
              const Icon = category.icon;
              const isExpanded = expandedCategories.has(categoryKey);

              return (
                <div key={categoryKey} className="mb-2">
                  <button
                    onClick={() => toggleCategory(categoryKey)}
                    className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 rounded-md transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="h-5 w-5 text-gray-500" />
                      <span className="font-medium text-gray-900">{category.title}</span>
                    </div>
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4 text-gray-400" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    )}
                  </button>

                  {isExpanded && (
                    <div className="ml-8 mt-1 space-y-1">
                      {Object.entries(category.topics).map(([topicKey, topic]) => (
                        <button
                          key={topicKey}
                          onClick={() => selectTopic(topicKey)}
                          className={`w-full text-left p-2 rounded-md transition-colors ${
                            selectedTopic === topicKey
                              ? 'bg-blue-50 text-blue-700'
                              : 'text-gray-600 hover:bg-gray-50'
                          }`}
                        >
                          {topic.title}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col">
          {currentTopic ? (
            <div className="flex-1 overflow-y-auto p-6">
              <div
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ __html: currentTopic.content }}
              />
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <HelpCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Select a topic to get started
                </h3>
                <p className="text-gray-500">
                  Choose a topic from the sidebar to view detailed help information.
                </p>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 bg-gray-50">
            <div className="flex items-center justify-center text-sm text-gray-500">
              <span>All help documentation is available within this application</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Context-sensitive help tooltip
 */
export const HelpTooltip = ({ content, position = 'top' }) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className="relative inline-block">
      <button
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        className="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <HelpCircle className="h-4 w-4" />
      </button>

      {isVisible && (
        <div className={`absolute z-10 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg max-w-xs ${
          position === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'
        }`}>
          {content}
          <div className={`absolute left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45 ${
            position === 'top' ? 'top-full -mt-1' : 'bottom-full -mb-1'
          }`} />
        </div>
      )}
    </div>
  );
};

export default HelpSystem;
