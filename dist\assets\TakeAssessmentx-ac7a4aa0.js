import{r,R as Ge,j as e,u as He}from"./chunk-81a058b1.js";import{b as Ke,u as Xe}from"./main-272222cd.js";import{t as H,X,p as J,d as q,A as T,C as w,F as Q,a5 as R,w as K,U as M,a6 as xe,a7 as he,a8 as Ae,R as ve,l as Je,Q as Ze,a9 as I,G as Ne,q as es,W as ss,aa as ts,I as is,b as ls}from"./chunk-0b87a8e8.js";import{C as as,a as rs,L as ms,B as ns,A as os,p as cs,b as ps,c as us,d as ds,P as bs}from"./chunk-ca279186.js";import"./chunk-81a949b4.js";const Ns=({isOpen:N,onClose:f,student:d,assessment:b,onUpload:D,uploading:E})=>{const[t,j]=r.useState({}),[x,v]=r.useState({}),[h,a]=r.useState([]);Ge.useEffect(()=>{N&&b&&p()},[N,b]);const p=async()=>{try{if(!assessmentId||!electronAPI?.assessments?.getQuestions)throw new Error("Assessment ID or API not available");const n=await electronAPI.assessments.getQuestions(assessmentId);if(n.success)a(n.data);else throw new Error(n.error||"Failed to load questions")}catch(n){showMessage("error",`Failed to load assessment questions: ${n.message}`)}},C=(n,m)=>{j(i=>({...i,[n]:m})),x[n]&&v(i=>({...i,[n]:null}))},l=()=>{const n={};return h.forEach(m=>{t[m.id]||(n[m.id]="Please select an answer for this question")}),v(n),Object.keys(n).length===0},y=async n=>{if(n.preventDefault(),!!l())try{await D(t),f(),j({}),v({})}catch{}},P=()=>{f(),j({}),v({})};return N?e.jsxDEV("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxDEV("div",{className:"relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white",children:[e.jsxDEV("div",{className:"flex justify-between items-center pb-4 border-b border-gray-200",children:[e.jsxDEV("div",{children:[e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(H,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:109,columnNumber:15},globalThis),"Upload Student Response"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:108,columnNumber:13},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Enter the student's responses for offline assessment submission"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:112,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:107,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:P,className:"text-gray-400 hover:text-gray-600",disabled:E,children:e.jsxDEV(X,{className:"h-6 w-6"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:121,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:116,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:106,columnNumber:9},globalThis),e.jsxDEV("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(J,{className:"h-5 w-5 text-blue-400 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:129,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-blue-900",children:"Student Information"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:131,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-blue-800",children:e.jsxDEV("strong",{children:[d?.first_name," ",d?.last_name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:133,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:132,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-blue-800",children:["ID: ",d?.student_id]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:135,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-blue-800",children:d?.email},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:136,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:130,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:128,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:127,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-green-50 border border-green-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(q,{className:"h-5 w-5 text-green-400 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:143,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-green-900",children:"Assessment Information"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:145,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-green-800",children:e.jsxDEV("strong",{children:b?.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:147,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:146,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-green-800",children:["Type: ",b?.assessment_type]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:149,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-green-800",children:["Questions: ",h.length]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:150,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:144,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:142,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:141,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:126,columnNumber:9},globalThis),e.jsxDEV("form",{onSubmit:y,className:"mt-6",children:[e.jsxDEV("div",{className:"max-h-96 overflow-y-auto",children:h.length>0?e.jsxDEV("div",{className:"space-y-6",children:h.map((n,m)=>e.jsxDEV("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsxDEV("div",{className:"mb-4",children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-900",children:["Question ",m+1,": ",n.text]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:164,columnNumber:23},globalThis),x[n.id]&&e.jsxDEV("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[e.jsxDEV(T,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:169,columnNumber:27},globalThis),x[n.id]]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:168,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:163,columnNumber:21},globalThis),e.jsxDEV("div",{className:"space-y-2",children:n.options.map((i,g)=>e.jsxDEV("label",{className:`flex items-center p-3 border rounded-md cursor-pointer transition-colors ${t[n.id]===i.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:[e.jsxDEV("input",{type:"radio",name:`question_${n.id}`,value:i.id,checked:t[n.id]===i.id,onChange:()=>C(n.id,i.id),className:"mr-3",disabled:E},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:185,columnNumber:27},globalThis),e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV("span",{className:"text-sm text-gray-900",children:[String.fromCharCode(65+g),". ",i.text]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:195,columnNumber:29},globalThis),e.jsxDEV("span",{className:"ml-2 text-sm font-medium text-green-600",children:["(",i.marks," marks)"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:198,columnNumber:29},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:194,columnNumber:27},globalThis),t[n.id]===i.id&&e.jsxDEV(w,{className:"h-5 w-5 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:203,columnNumber:29},globalThis)]},i.id,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:177,columnNumber:25},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:175,columnNumber:21},globalThis)]},n.id,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:162,columnNumber:19},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:160,columnNumber:15},globalThis):e.jsxDEV("div",{className:"text-center py-8",children:[e.jsxDEV(Q,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:213,columnNumber:17},globalThis),e.jsxDEV("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Loading Questions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:214,columnNumber:17},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"Please wait while we load the assessment questions..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:215,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:212,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:158,columnNumber:11},globalThis),e.jsxDEV("div",{className:"mt-6 flex justify-end space-x-3 pt-4 border-t border-gray-200",children:[e.jsxDEV("button",{type:"button",onClick:P,disabled:E,className:"btn-outline",children:"Cancel"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:224,columnNumber:13},globalThis),e.jsxDEV("button",{type:"submit",disabled:E||h.length===0,className:"btn-primary flex items-center space-x-2",children:E?e.jsxDEV(e.Fragment,{children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:239,columnNumber:19},globalThis),e.jsxDEV("span",{children:"Uploading..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:240,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:238,columnNumber:17},globalThis):e.jsxDEV(e.Fragment,{children:[e.jsxDEV(H,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:244,columnNumber:19},globalThis),e.jsxDEV("span",{children:"Upload Response"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:245,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:243,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:232,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:223,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:157,columnNumber:9},globalThis),e.jsxDEV("div",{className:"mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(T,{className:"h-5 w-5 text-yellow-400 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:255,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-yellow-900",children:"Upload Instructions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:257,columnNumber:15},globalThis),e.jsxDEV("div",{className:"text-sm text-yellow-800 mt-1 space-y-1",children:[e.jsxDEV("p",{children:"• Select the student's chosen answer for each question"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:259,columnNumber:17},globalThis),e.jsxDEV("p",{children:"• Ensure all questions are answered before uploading"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:260,columnNumber:17},globalThis),e.jsxDEV("p",{children:"• The system will automatically calculate the total score"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:261,columnNumber:17},globalThis),e.jsxDEV("p",{children:"• Once uploaded, the response cannot be modified"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:262,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:258,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:256,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:254,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:253,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:104,columnNumber:7},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/UploadResponseModal.jsx",lineNumber:103,columnNumber:5},globalThis):null},xs=({isOpen:N,onClose:f,student:d,assessment:b,onLoadStatistics:D,loading:E})=>{const[t,j]=r.useState(null),[x,v]=r.useState(null);r.useEffect(()=>{N&&d&&b&&h()},[N,d,b]);const h=async()=>{try{v(null);const l=await D(d.id,b.id);j(l)}catch(l){v(l.message)}},a=l=>l?new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Not submitted",p=l=>l>=80?"text-green-600 bg-green-100":l>=60?"text-yellow-600 bg-yellow-100":"text-red-600 bg-red-100",C=l=>l===1?"st":l===2?"nd":l===3?"rd":"th";return N?e.jsxDEV("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxDEV("div",{className:"relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white",children:[e.jsxDEV("div",{className:"flex justify-between items-center pb-4 border-b border-gray-200",children:[e.jsxDEV("div",{children:[e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(R,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:77,columnNumber:15},globalThis),"Student Assessment Statistics"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:76,columnNumber:13},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Detailed performance analysis and batch comparison"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:80,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:75,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:f,className:"text-gray-400 hover:text-gray-600",children:e.jsxDEV(X,{className:"h-6 w-6"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:88,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:84,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:74,columnNumber:9},globalThis),e.jsxDEV("div",{className:"mt-6",children:E?e.jsxDEV("div",{className:"text-center py-12",children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:96,columnNumber:15},globalThis),e.jsxDEV("p",{className:"mt-4 text-sm text-gray-500",children:"Loading statistics..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:97,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:95,columnNumber:13},globalThis):x?e.jsxDEV("div",{className:"text-center py-12",children:[e.jsxDEV(T,{className:"mx-auto h-12 w-12 text-red-500"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:101,columnNumber:15},globalThis),e.jsxDEV("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Error Loading Statistics"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:102,columnNumber:15},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:x},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:103,columnNumber:15},globalThis),e.jsxDEV("button",{onClick:h,className:"mt-4 btn-primary",children:"Retry"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:104,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:100,columnNumber:13},globalThis):t?e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(J,{className:"h-6 w-6 text-blue-600 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:117,columnNumber:21},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-lg font-medium text-blue-900",children:[t.student.first_name," ",t.student.last_name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:119,columnNumber:23},globalThis),e.jsxDEV("p",{className:"text-sm text-blue-700",children:["ID: ",t.student.student_id]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:122,columnNumber:23},globalThis),e.jsxDEV("p",{className:"text-sm text-blue-700",children:t.student.email},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:123,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:118,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:116,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:115,columnNumber:17},globalThis),e.jsxDEV("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(q,{className:"h-6 w-6 text-green-600 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:130,columnNumber:21},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-lg font-medium text-green-900",children:t.assessment.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:132,columnNumber:23},globalThis),e.jsxDEV("p",{className:"text-sm text-green-700",children:["Type: ",t.assessment.assessment_type]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:135,columnNumber:23},globalThis),e.jsxDEV("p",{className:"text-sm text-green-700",children:["Due: ",a(t.assessment.due_date)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:136,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:131,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:129,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:128,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:114,columnNumber:15},globalThis),t.hasSubmitted?e.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[e.jsxDEV("h4",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[e.jsxDEV(w,{className:"h-5 w-5 text-green-600 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:148,columnNumber:21},globalThis),"Submission Details"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:147,columnNumber:19},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-blue-600",children:t.response.total_score},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:154,columnNumber:23},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Total Score"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:157,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:153,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-green-600",children:t.questionStats.length},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:161,columnNumber:23},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Questions Answered"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:164,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:160,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-purple-600",children:[Math.round(t.response.total_score/Math.max(...t.questionStats.map(l=>l.maxMarks))/t.questionStats.length*100),"%"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:168,columnNumber:23},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Average Performance"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:171,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:167,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-sm text-gray-500 flex items-center justify-center",children:[e.jsxDEV(K,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:176,columnNumber:25},globalThis),"Submitted"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:175,columnNumber:23},globalThis),e.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:a(t.response.submitted_at)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:179,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:174,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:152,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:146,columnNumber:17},globalThis):e.jsxDEV("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center",children:[e.jsxDEV(T,{className:"mx-auto h-12 w-12 text-yellow-500"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:187,columnNumber:19},globalThis),e.jsxDEV("h3",{className:"mt-2 text-lg font-medium text-yellow-900",children:"No Submission Found"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:188,columnNumber:19},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-yellow-700",children:"This student has not submitted a response for this assessment yet."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:189,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:186,columnNumber:17},globalThis),t.batchComparison&&e.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[e.jsxDEV("h4",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[e.jsxDEV(M,{className:"h-5 w-5 text-blue-600 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:199,columnNumber:21},globalThis),"Batch Performance Comparison"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:198,columnNumber:19},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-blue-600",children:[t.batchComparison.studentRank,C(t.batchComparison.studentRank)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:205,columnNumber:23},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Rank in Batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:208,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:204,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-green-600",children:Math.round(t.batchComparison.batchAverage)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:212,columnNumber:23},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Batch Average"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:215,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:211,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-yellow-600",children:t.batchComparison.batchMax},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:219,columnNumber:23},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Highest Score"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:222,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:218,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-red-600",children:t.batchComparison.batchMin},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:226,columnNumber:23},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Lowest Score"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:229,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:225,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"text-2xl font-bold text-purple-600",children:t.batchComparison.totalStudents},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:233,columnNumber:23},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Total Students"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:236,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:232,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:203,columnNumber:19},globalThis),e.jsxDEV("div",{className:"mt-4 text-center",children:e.jsxDEV("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${t.batchComparison.studentScore>t.batchComparison.batchAverage?"text-green-800 bg-green-100":t.batchComparison.studentScore===t.batchComparison.batchAverage?"text-yellow-800 bg-yellow-100":"text-red-800 bg-red-100"}`,children:t.batchComparison.studentScore>t.batchComparison.batchAverage?e.jsxDEV(e.Fragment,{children:[e.jsxDEV(xe,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:251,columnNumber:27},globalThis),"Above Average"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:250,columnNumber:25},globalThis):t.batchComparison.studentScore===t.batchComparison.batchAverage?e.jsxDEV(e.Fragment,{children:[e.jsxDEV(he,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:256,columnNumber:27},globalThis),"At Average"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:255,columnNumber:25},globalThis):e.jsxDEV(e.Fragment,{children:[e.jsxDEV(T,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:261,columnNumber:27},globalThis),"Below Average"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:260,columnNumber:25},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:242,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:241,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:197,columnNumber:17},globalThis),t.questionStats&&t.questionStats.length>0&&e.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[e.jsxDEV("h4",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[e.jsxDEV(Ae,{className:"h-5 w-5 text-blue-600 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:274,columnNumber:21},globalThis),"Question-wise Performance"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:273,columnNumber:19},globalThis),e.jsxDEV("div",{className:"overflow-x-auto",children:e.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxDEV("thead",{className:"bg-gray-50",children:e.jsxDEV("tr",{children:[e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Question"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:282,columnNumber:27},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Selected Answer"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:285,columnNumber:27},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Marks Earned"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:288,columnNumber:27},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Max Marks"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:291,columnNumber:27},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Performance"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:294,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:281,columnNumber:25},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:280,columnNumber:23},globalThis),e.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:t.questionStats.map((l,y)=>e.jsxDEV("tr",{className:"hover:bg-gray-50",children:[e.jsxDEV("td",{className:"px-6 py-4",children:[e.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:["Q",y+1]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:303,columnNumber:31},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:l.questionText},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:306,columnNumber:31},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:302,columnNumber:29},globalThis),e.jsxDEV("td",{className:"px-6 py-4 text-sm text-gray-900",children:l.selectedOption},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:310,columnNumber:29},globalThis),e.jsxDEV("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:l.marksEarned},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:313,columnNumber:29},globalThis),e.jsxDEV("td",{className:"px-6 py-4 text-sm text-gray-900",children:l.maxMarks},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:316,columnNumber:29},globalThis),e.jsxDEV("td",{className:"px-6 py-4",children:e.jsxDEV("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${p(l.percentage)}`,children:[Math.round(l.percentage),"%"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:320,columnNumber:31},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:319,columnNumber:29},globalThis)]},l.questionId,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:301,columnNumber:27},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:299,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:279,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:278,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:272,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:112,columnNumber:13},globalThis):e.jsxDEV("div",{className:"text-center py-12",children:[e.jsxDEV(R,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:334,columnNumber:15},globalThis),e.jsxDEV("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No Statistics Available"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:335,columnNumber:15},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"Statistics will be available once the student submits their assessment."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:336,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:333,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:93,columnNumber:9},globalThis),e.jsxDEV("div",{className:"mt-6 flex justify-end pt-4 border-t border-gray-200",children:e.jsxDEV("button",{onClick:f,className:"btn-primary",children:"Close"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:345,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:344,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:72,columnNumber:7},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StudentStatisticsModal.jsx",lineNumber:71,columnNumber:5},globalThis):null};as.register(rs,ms,ns,os,cs,ps,us);const hs=({isOpen:N,onClose:f,assessment:d,batch:b,onLoadStatistics:D,loading:E})=>{const[t,j]=r.useState(null),[x,v]=r.useState(!1),[h,a]=r.useState(""),[p,C]=r.useState(null);r.useEffect(()=>{N&&d&&b&&l()},[N,d,b]);const l=async()=>{try{v(!0),a("");const i=await D(d.id,b.id);j(i)}catch(i){a(i.message)}finally{v(!1)}},y=()=>t?.batchStats?.scoreDistribution?{labels:t.batchStats.scoreDistribution.map(i=>i.label),datasets:[{label:"Number of Students",data:t.batchStats.scoreDistribution.map(i=>i.count),backgroundColor:["#10B981","#3B82F6","#8B5CF6","#F59E0B","#EF4444","#6B7280"],borderColor:["#059669","#2563EB","#7C3AED","#D97706","#DC2626","#4B5563"],borderWidth:1}]}:null,P=i=>i?.options?{labels:i.options.map(g=>`Option ${g.option_order}: ${g.option_text.substring(0,30)}...`),datasets:[{data:i.options.map(g=>g.percentage),backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6","#6B7280"],borderColor:["#2563EB","#059669","#D97706","#DC2626","#7C3AED","#4B5563"],borderWidth:2}]}:null,n={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"},title:{display:!0,text:"Score Distribution"}}},m={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"},tooltip:{callbacks:{label:function(i){return`${i.label}: ${i.parsed}%`}}}}};return N?e.jsxDEV("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxDEV("div",{className:"relative top-4 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white",children:[e.jsxDEV("div",{className:"flex justify-between items-center mb-6",children:[e.jsxDEV("div",{children:[e.jsxDEV("h3",{className:"text-xl font-bold text-gray-900 flex items-center",children:[e.jsxDEV(R,{className:"h-6 w-6 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:171,columnNumber:15},globalThis),"Assessment Statistics"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:170,columnNumber:13},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:[d?.name," - ",b?.name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:174,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:169,columnNumber:11},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-2",children:[e.jsxDEV("button",{onClick:l,disabled:x,className:"btn-outline flex items-center space-x-1",children:[e.jsxDEV(ve,{className:`h-4 w-4 ${x?"animate-spin":""}`},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:184,columnNumber:15},globalThis),e.jsxDEV("span",{children:"Refresh"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:185,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:179,columnNumber:13},globalThis),e.jsxDEV("button",{onClick:f,className:"text-gray-400 hover:text-gray-600",children:e.jsxDEV(X,{className:"h-6 w-6"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:191,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:187,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:178,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:168,columnNumber:9},globalThis),x&&e.jsxDEV("div",{className:"text-center py-12",children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:199,columnNumber:13},globalThis),e.jsxDEV("p",{className:"mt-4 text-gray-600",children:"Loading statistics..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:200,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:198,columnNumber:11},globalThis),h&&e.jsxDEV("div",{className:"bg-red-50 border border-red-200 rounded-md p-4 mb-6",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(T,{className:"h-5 w-5 text-red-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:208,columnNumber:15},globalThis),e.jsxDEV("div",{className:"ml-3",children:[e.jsxDEV("h3",{className:"text-sm font-medium text-red-800",children:"Error Loading Statistics"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:210,columnNumber:17},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-red-700",children:h},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:211,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:209,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:207,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:206,columnNumber:11},globalThis),t&&!x&&e.jsxDEV("div",{className:"space-y-6",children:[e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxDEV("div",{className:"bg-blue-50 p-4 rounded-lg",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(M,{className:"h-8 w-8 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:224,columnNumber:19},globalThis),e.jsxDEV("div",{className:"ml-3",children:[e.jsxDEV("p",{className:"text-sm font-medium text-blue-600",children:"Total Students"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:226,columnNumber:21},globalThis),e.jsxDEV("p",{className:"text-2xl font-bold text-blue-900",children:t.batchStats.totalStudents},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:227,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:225,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:223,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:222,columnNumber:15},globalThis),e.jsxDEV("div",{className:"bg-green-50 p-4 rounded-lg",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(he,{className:"h-8 w-8 text-green-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:234,columnNumber:19},globalThis),e.jsxDEV("div",{className:"ml-3",children:[e.jsxDEV("p",{className:"text-sm font-medium text-green-600",children:"Average Score"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:236,columnNumber:21},globalThis),e.jsxDEV("p",{className:"text-2xl font-bold text-green-900",children:[t.batchStats.averageScore,"%"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:237,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:235,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:233,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:232,columnNumber:15},globalThis),e.jsxDEV("div",{className:"bg-yellow-50 p-4 rounded-lg",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(Ae,{className:"h-8 w-8 text-yellow-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:244,columnNumber:19},globalThis),e.jsxDEV("div",{className:"ml-3",children:[e.jsxDEV("p",{className:"text-sm font-medium text-yellow-600",children:"Highest Score"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:246,columnNumber:21},globalThis),e.jsxDEV("p",{className:"text-2xl font-bold text-yellow-900",children:[t.batchStats.highestScore,"%"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:247,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:245,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:243,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:242,columnNumber:15},globalThis),e.jsxDEV("div",{className:"bg-purple-50 p-4 rounded-lg",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(xe,{className:"h-8 w-8 text-purple-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:254,columnNumber:19},globalThis),e.jsxDEV("div",{className:"ml-3",children:[e.jsxDEV("p",{className:"text-sm font-medium text-purple-600",children:"Pass Rate"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:256,columnNumber:21},globalThis),e.jsxDEV("p",{className:"text-2xl font-bold text-purple-900",children:[t.batchStats.passRate,"%"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:257,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:255,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:253,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:252,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:221,columnNumber:13},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxDEV("div",{className:"bg-white border rounded-lg p-4",children:[e.jsxDEV("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"Score Distribution"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:267,columnNumber:17},globalThis),e.jsxDEV("div",{className:"h-64",children:y()&&e.jsxDEV(ds,{data:y(),options:n},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:270,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:268,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:266,columnNumber:15},globalThis),e.jsxDEV("div",{className:"bg-white border rounded-lg p-4",children:[e.jsxDEV("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"Question Analysis"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:277,columnNumber:17},globalThis),e.jsxDEV("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:t.questionStats.map((i,g)=>e.jsxDEV("div",{className:`p-3 border rounded cursor-pointer transition-colors ${p?.questionId===i.questionId?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>C(i),children:[e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("span",{className:"text-sm font-medium",children:["Q",i.questionOrder]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:290,columnNumber:25},globalThis),e.jsxDEV("span",{className:"text-xs text-gray-500",children:[i.responseRate,"% response rate"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:291,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:289,columnNumber:23},globalThis),e.jsxDEV("p",{className:"text-xs text-gray-600 mt-1 truncate",children:i.questionText},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:293,columnNumber:23},globalThis)]},i.questionId,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:280,columnNumber:21},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:278,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:276,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:264,columnNumber:13},globalThis),p&&e.jsxDEV("div",{className:"bg-white border rounded-lg p-4",children:[e.jsxDEV("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:["Question ",p.questionOrder," - Option Distribution"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:305,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mb-4",children:p.questionText},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:308,columnNumber:17},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxDEV("div",{className:"h-64",children:P(p)&&e.jsxDEV(bs,{data:P(p),options:m},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:313,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:311,columnNumber:19},globalThis),e.jsxDEV("div",{className:"space-y-2",children:[e.jsxDEV("h5",{className:"font-medium text-gray-900",children:"Option Details:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:318,columnNumber:21},globalThis),p.options.map((i,g)=>e.jsxDEV("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV("span",{className:"text-sm font-medium",children:["Option ",i.option_order,":"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:322,columnNumber:27},globalThis),e.jsxDEV("span",{className:"text-sm text-gray-600 ml-2",children:i.option_text},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:323,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:321,columnNumber:25},globalThis),e.jsxDEV("div",{className:"text-right",children:[e.jsxDEV("div",{className:"text-sm font-medium",children:[i.percentage,"%"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:326,columnNumber:27},globalThis),e.jsxDEV("div",{className:"text-xs text-gray-500",children:["(",i.count," students)"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:327,columnNumber:27},globalThis),e.jsxDEV("div",{className:"text-xs text-blue-600",children:[i.marks," marks"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:328,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:325,columnNumber:25},globalThis)]},i.id,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:320,columnNumber:23},globalThis)),p.mostSelectedOption&&e.jsxDEV("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded",children:[e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(w,{className:"h-4 w-4 text-green-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:336,columnNumber:27},globalThis),e.jsxDEV("span",{className:"ml-2 text-sm font-medium text-green-800",children:"Most Selected (Recommended Answer):"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:337,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:335,columnNumber:25},globalThis),e.jsxDEV("p",{className:"text-sm text-green-700 mt-1",children:["Option ",p.mostSelectedOption.option_order,": ",p.mostSelectedOption.option_text]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:339,columnNumber:25},globalThis),e.jsxDEV("p",{className:"text-xs text-green-600 mt-1",children:[p.mostSelectedOption.marks," marks - Selected by ",p.mostSelectedOption.percentage,"% of students"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:342,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:334,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:317,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:310,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:304,columnNumber:15},globalThis),e.jsxDEV("div",{className:"bg-white border rounded-lg p-4",children:[e.jsxDEV("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"Student Rankings"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:354,columnNumber:15},globalThis),e.jsxDEV("div",{className:"overflow-x-auto",children:[e.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxDEV("thead",{className:"bg-gray-50",children:e.jsxDEV("tr",{children:[e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:359,columnNumber:23},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:360,columnNumber:23},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student ID"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:361,columnNumber:23},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Score"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:362,columnNumber:23},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Percentile"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:363,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:358,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:357,columnNumber:19},globalThis),e.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:t.studentRankings.slice(0,10).map(i=>e.jsxDEV("tr",{children:[e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",i.rank]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:369,columnNumber:25},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[i.first_name," ",i.last_name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:372,columnNumber:25},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:i.student_id},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:375,columnNumber:25},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[i.total_score,"%"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:378,columnNumber:25},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[i.percentile,"%"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:381,columnNumber:25},globalThis)]},i.id,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:368,columnNumber:23},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:366,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:356,columnNumber:17},globalThis),t.studentRankings.length>10&&e.jsxDEV("p",{className:"text-sm text-gray-500 text-center mt-4",children:["Showing top 10 students. Total: ",t.studentRankings.length," students"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:389,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:355,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:353,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:219,columnNumber:11},globalThis),e.jsxDEV("div",{className:"mt-6 flex justify-end",children:e.jsxDEV("button",{onClick:f,className:"btn-outline",children:"Close"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:400,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:399,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:166,columnNumber:7},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/assessment/StatisticsDashboard.jsx",lineNumber:165,columnNumber:5},globalThis):null},js=()=>{const N=He(),{getUserAccessibleBatches:f,getAssessmentsForBatch:d,getBatchStudentsWithProgress:b,canStudentAccessAssessment:D,generateAssessmentForm:E,uploadStudentResponse:t,getStudentStatistics:j,getBatchCompletionStatus:x,processAssessmentForPeerReview:v,getAssessmentStatistics:h}=Ke(),{user:a}=Xe(),[p,C]=r.useState([]),[l,y]=r.useState(null),[P,n]=r.useState([]),[m,i]=r.useState(null),[g,U]=r.useState([]),[ge,Z]=r.useState(!0),[fe,ee]=r.useState(!1),[Ee,se]=r.useState(!1),[L,te]=r.useState({type:"",text:""}),[V,je]=r.useState(""),[_,ye]=r.useState("all"),[z,we]=r.useState(!1),[Te,W]=r.useState(!1),[De,ie]=r.useState(!1),[Y,S]=r.useState(null),[F,$]=r.useState({}),[Ce,le]=r.useState(!1),[Pe,ae]=r.useState(!1),[c,re]=r.useState(null),[me,ne]=r.useState(!1),[Le,G]=r.useState(!1),[Ve,oe]=r.useState(!1);r.useEffect(()=>{ce()},[]),r.useEffect(()=>{l?Se():(n([]),i(null),U([]))},[l]),r.useEffect(()=>{l&&m?(pe(),a.role==="teacher"&&O()):(U([]),re(null))},[l,m]),r.useEffect(()=>{if(l&&m&&a.role==="teacher"){const s=setInterval(()=>{O()},3e4);return()=>clearInterval(s)}},[l,m]);const ce=async()=>{try{Z(!0);const s=await f(a.id,a.role,a.institution_id);C(s),a.role==="student"&&s.length===1&&y(s[0])}catch(s){A("error",s.message)}finally{Z(!1)}},Se=async()=>{try{ee(!0);const s=await d(l.id,a.id,a.role);n(s)}catch(s){A("error",s.message)}finally{ee(!1)}},pe=async()=>{try{se(!0);const s=await b(l.id,m.id,a.id,a.role);U(s)}catch(s){A("error",s.message)}finally{se(!1)}},A=(s,o)=>{te({type:s,text:o}),setTimeout(()=>te({type:"",text:""}),5e3)},ke=s=>{y(s),i(null),U([])},Me=s=>{i(s)},ue=async s=>{try{if(a.role==="student"){const o=await D(a.id,s.id);if(!o.canAccess){A("error",o.reason);return}}N(`/assessment/${s.id}/take`)}catch(o){A("error",o.message)}},Re=s=>{switch(s){case"active":return"text-green-600 bg-green-100";case"overdue":return"text-red-600 bg-red-100";case"completed":return"text-blue-600 bg-blue-100";default:return"text-gray-600 bg-gray-100"}},Ue=s=>{switch(s){case"completed":return"text-green-600 bg-green-100";case"in_progress":return"text-yellow-600 bg-yellow-100";case"not_started":return"text-gray-600 bg-gray-100";default:return"text-gray-600 bg-gray-100"}},B=s=>s?new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Not set",_e=async s=>{const o=`download-form-${s.id}`;try{$(Ye=>({...Ye,[o]:!0}));const u=await E(m.id,a.id,a.institution_id),We=new Blob([u.html],{type:"text/html"}),be=window.URL.createObjectURL(We),k=document.createElement("a");k.href=be,k.download=`${s.first_name}_${s.last_name}_${u.filename}`,document.body.appendChild(k),k.click(),document.body.removeChild(k),window.URL.revokeObjectURL(be),A("success",`Assessment form downloaded for ${s.first_name} ${s.last_name}`)}catch(u){A("error",u.message)}finally{$(u=>({...u,[o]:!1}))}},Fe=s=>{S(s),W(!0)},$e=async s=>{try{le(!0);const o=await t(m.id,Y.id,s,a.id,a.institution_id);A("success",`Response uploaded successfully. Score: ${o.totalScore}`),pe(),O(),W(!1),S(null)}catch(o){A("error",o.message)}finally{le(!1)}},O=async()=>{try{const s=await x(l.id,m.id,a.id,a.institution_id);re(s)}catch{}},Be=()=>{G(!0)},Oe=async()=>{try{ne(!0),G(!1);const s=await v(m.id,l.id,a.id,a.institution_id);A("success",`${s.message} Statistics and PDF reports have been generated successfully.`),O()}catch(s){A("error",s.message)}finally{ne(!1)}},Ie=async s=>{const o=`download-pdf-${s.id}`;try{$(u=>({...u,[o]:!0})),A("info","Peer review PDF generation will be available when peer reviews are implemented")}catch(u){A("error",u.message)}finally{$(u=>({...u,[o]:!1}))}},Qe=s=>{S(s),ie(!0)},qe=async(s,o)=>{ae(!0);try{return await j(s,o,a.id,a.institution_id)}finally{ae(!1)}},ze=async(s,o)=>{try{return await h(s,o,a.id)}catch(u){throw new Error(`Failed to load assessment statistics: ${u.message}`)}},de=P.filter(s=>{const o=!V||s.name.toLowerCase().includes(V.toLowerCase())||s.assessment_type.toLowerCase().includes(V.toLowerCase()),u=_==="all"||s.status===_;return o&&u});return!a||!["teacher","student","admin","super_admin"].includes(a.role)?e.jsxDEV("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV(M,{className:"mx-auto h-12 w-12 text-red-500"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:409,columnNumber:11},globalThis),e.jsxDEV("h2",{className:"mt-2 text-lg font-medium text-gray-900",children:"Access Denied"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:410,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"You need appropriate privileges to access assessments."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:411,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:408,columnNumber:9},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:407,columnNumber:7},globalThis):e.jsxDEV("div",{className:"space-y-6","data-testid":"take-assessment",children:[e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("div",{children:[e.jsxDEV("h1",{className:"text-2xl font-bold text-gray-900",children:"Take Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:424,columnNumber:11},globalThis),e.jsxDEV("p",{className:"text-gray-600",children:a.role==="student"?"Access your assigned assessments and track your progress":"Monitor student progress and manage assessment access"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:425,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:423,columnNumber:9},globalThis),e.jsxDEV("button",{onClick:ce,className:"btn-outline flex items-center space-x-2",children:[e.jsxDEV(ve,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:436,columnNumber:11},globalThis),e.jsxDEV("span",{children:"Refresh"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:437,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:432,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:422,columnNumber:7},globalThis),L.text&&e.jsxDEV("div",{className:`rounded-md p-4 ${L.type==="error"?"bg-red-50":"bg-green-50"}`,children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:L.type==="error"?e.jsxDEV(T,{className:"h-5 w-5 text-red-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:447,columnNumber:17},globalThis):e.jsxDEV(w,{className:"h-5 w-5 text-green-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:449,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:445,columnNumber:13},globalThis),e.jsxDEV("div",{className:"ml-3",children:e.jsxDEV("p",{className:`text-sm ${L.type==="error"?"text-red-800":"text-green-800"}`,children:L.text},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:453,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:452,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:444,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:443,columnNumber:9},globalThis),e.jsxDEV("div",{className:"bg-white shadow rounded-lg",children:[e.jsxDEV("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(M,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:465,columnNumber:13},globalThis),"Step 1: Select Batch"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:464,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:463,columnNumber:9},globalThis),e.jsxDEV("div",{className:"p-6",children:ge?e.jsxDEV("div",{className:"text-center py-8",children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:473,columnNumber:15},globalThis),e.jsxDEV("p",{className:"mt-2 text-sm text-gray-500",children:"Loading batches..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:474,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:472,columnNumber:13},globalThis):p.length>0?e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:p.map(s=>e.jsxDEV("div",{className:`border rounded-lg p-4 cursor-pointer transition-colors ${l?.id===s.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>ke(s),children:e.jsxDEV("div",{className:"flex items-start justify-between",children:[e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV("h4",{className:"text-sm font-medium text-gray-900",children:s.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:490,columnNumber:23},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-500 mt-1",children:s.description||"No description"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:491,columnNumber:23},globalThis),e.jsxDEV("div",{className:"mt-2 space-y-1",children:[e.jsxDEV("div",{className:"flex items-center text-xs text-gray-500",children:[e.jsxDEV(K,{className:"h-3 w-3 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:494,columnNumber:27},globalThis),s.academic_year]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:493,columnNumber:25},globalThis),e.jsxDEV("div",{className:"flex items-center text-xs text-gray-500",children:[e.jsxDEV(q,{className:"h-3 w-3 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:498,columnNumber:27},globalThis),s.course_type]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:497,columnNumber:25},globalThis),e.jsxDEV("div",{className:"flex items-center text-xs text-gray-500",children:[e.jsxDEV(J,{className:"h-3 w-3 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:502,columnNumber:27},globalThis),s.student_count," students"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:501,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:492,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:489,columnNumber:21},globalThis),l?.id===s.id&&e.jsxDEV(w,{className:"h-5 w-5 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:508,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:488,columnNumber:19},globalThis)},s.id,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:479,columnNumber:17},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:477,columnNumber:13},globalThis):e.jsxDEV("div",{className:"text-center py-8",children:[e.jsxDEV(M,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:516,columnNumber:15},globalThis),e.jsxDEV("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No batches available"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:517,columnNumber:15},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:a.role==="student"?"You are not enrolled in any batches yet.":"You have not created any batches yet."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:518,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:515,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:470,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:462,columnNumber:7},globalThis),l&&e.jsxDEV("div",{className:"bg-white shadow rounded-lg",children:[e.jsxDEV("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(Q,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:535,columnNumber:17},globalThis),"Step 2: Select Assessment"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:534,columnNumber:15},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:["Batch: ",e.jsxDEV("span",{className:"font-medium",children:l.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:539,columnNumber:24},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:538,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:533,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:532,columnNumber:11},globalThis),e.jsxDEV("div",{className:"p-6",children:[e.jsxDEV("div",{className:"mb-4 flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[e.jsxDEV("div",{className:"flex-1 max-w-lg",children:e.jsxDEV("div",{className:"relative",children:[e.jsxDEV(Je,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:549,columnNumber:19},globalThis),e.jsxDEV("input",{type:"text",placeholder:"Search assessments...",className:"pl-10 input",value:V,onChange:s=>je(s.target.value)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:550,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:548,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:547,columnNumber:15},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-3",children:e.jsxDEV("button",{onClick:()=>we(!z),className:`btn-outline flex items-center space-x-2 ${z?"bg-blue-50 border-blue-300":""}`,children:[e.jsxDEV(Ze,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:565,columnNumber:19},globalThis),e.jsxDEV("span",{children:"Filters"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:566,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:561,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:560,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:546,columnNumber:13},globalThis),z&&e.jsxDEV("div",{className:"mb-4 p-4 bg-gray-50 rounded-md",children:e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:576,columnNumber:21},globalThis),e.jsxDEV("select",{className:"input",value:_,onChange:s=>ye(s.target.value),children:[e.jsxDEV("option",{value:"all",children:"All Status"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:584,columnNumber:23},globalThis),e.jsxDEV("option",{value:"active",children:"Active"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:585,columnNumber:23},globalThis),e.jsxDEV("option",{value:"overdue",children:"Overdue"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:586,columnNumber:23},globalThis),e.jsxDEV("option",{value:"completed",children:"Completed"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:587,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:579,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:575,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:574,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:573,columnNumber:15},globalThis),fe?e.jsxDEV("div",{className:"text-center py-8",children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:597,columnNumber:17},globalThis),e.jsxDEV("p",{className:"mt-2 text-sm text-gray-500",children:"Loading assessments..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:598,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:596,columnNumber:15},globalThis):de.length>0?e.jsxDEV("div",{className:"space-y-4",children:de.map(s=>e.jsxDEV("div",{className:`border rounded-lg p-4 cursor-pointer transition-colors ${m?.id===s.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>Me(s),"data-testid":"question-container",children:e.jsxDEV("div",{className:"flex items-start justify-between",children:[e.jsxDEV("div",{className:"flex-1",children:[e.jsxDEV("div",{className:"flex items-center space-x-3",children:[e.jsxDEV("h4",{className:"text-sm font-medium text-gray-900",children:s.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:616,columnNumber:27},globalThis),e.jsxDEV("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${Re(s.status)}`,children:s.status},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:617,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:615,columnNumber:25},globalThis),e.jsxDEV("div",{className:"mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500",children:[e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(q,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:624,columnNumber:29},globalThis),s.assessment_type]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:623,columnNumber:27},globalThis),e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(K,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:628,columnNumber:29},globalThis),"Due: ",B(s.due_date)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:627,columnNumber:27},globalThis),e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV(Q,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:632,columnNumber:29},globalThis),s.question_count," questions"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:631,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:622,columnNumber:25},globalThis),e.jsxDEV("div",{className:"mt-2 flex items-center justify-between",children:[e.jsxDEV("div",{className:"text-xs text-gray-500",children:["Created by: ",s.created_by_name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:638,columnNumber:27},globalThis),a.role==="student"&&e.jsxDEV("button",{onClick:o=>{o.stopPropagation(),ue(s)},className:"btn-primary text-sm flex items-center space-x-1",children:[e.jsxDEV(I,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:649,columnNumber:31},globalThis),e.jsxDEV("span",{children:"Take Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:650,columnNumber:31},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:642,columnNumber:29},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:637,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:614,columnNumber:23},globalThis),m?.id===s.id&&e.jsxDEV(w,{className:"h-5 w-5 text-blue-600 ml-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:657,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:613,columnNumber:21},globalThis)},s.id,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:603,columnNumber:19},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:601,columnNumber:15},globalThis):e.jsxDEV("div",{className:"text-center py-8",children:[e.jsxDEV(Q,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:665,columnNumber:17},globalThis),e.jsxDEV("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No assessments found"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:666,columnNumber:17},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:V||_!=="all"?"Try adjusting your search or filters.":"No assessments have been assigned to this batch yet."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:667,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:664,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:544,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:531,columnNumber:9},globalThis),l&&m&&a.role==="teacher"&&e.jsxDEV("div",{className:"bg-white shadow rounded-lg",children:[e.jsxDEV("div",{className:"px-6 py-4 border-b border-gray-200",children:[e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(Ne,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:685,columnNumber:17},globalThis),"Step 3: Student Progress"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:684,columnNumber:15},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:["Assessment: ",e.jsxDEV("span",{className:"font-medium",children:m.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:689,columnNumber:29},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:688,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:683,columnNumber:13},globalThis),c&&e.jsxDEV("div",{className:"mt-4 flex items-center justify-between",children:[e.jsxDEV("div",{className:"flex items-center space-x-4",children:[e.jsxDEV("div",{className:"text-sm text-gray-600",children:[e.jsxDEV("span",{className:"font-medium text-gray-900",children:[c.statistics.completedStudents," of ",c.statistics.totalStudents]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:698,columnNumber:21},globalThis)," students completed"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:697,columnNumber:19},globalThis),e.jsxDEV("div",{className:"flex-1 bg-gray-200 rounded-full h-2 w-48",children:e.jsxDEV("div",{className:`h-2 rounded-full transition-all duration-300 ${c.statistics.allCompleted?"bg-green-600":"bg-blue-600"}`,style:{width:`${c.statistics.completionPercentage}%`}},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:703,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:702,columnNumber:19},globalThis),e.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:[c.statistics.completionPercentage,"%"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:710,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:696,columnNumber:17},globalThis),c.statistics.allCompleted&&e.jsxDEV("div",{className:"flex items-center text-green-600",children:[e.jsxDEV(w,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:717,columnNumber:21},globalThis),e.jsxDEV("span",{className:"text-sm font-medium",children:"All students completed"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:718,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:716,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:695,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:682,columnNumber:11},globalThis),e.jsxDEV("div",{className:"p-6",children:[c&&e.jsxDEV("div",{className:"mb-6 flex justify-center",children:e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("div",{className:"flex items-center space-x-4",children:[e.jsxDEV("button",{onClick:Be,disabled:!c.canProcess||me,className:`btn-primary flex items-center space-x-2 px-6 py-3 text-lg ${c.canProcess?"":"opacity-50 cursor-not-allowed"}`,title:c.processedStatus?"Assessment has already been processed":c.statistics.allCompleted?"Process assessment for peer review allocation":`${c.statistics.pendingStudents} students have not completed their responses`,children:me?e.jsxDEV(e.Fragment,{children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:747,columnNumber:27},globalThis),e.jsxDEV("span",{children:"Processing..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:748,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:746,columnNumber:25},globalThis):c.processedStatus?e.jsxDEV(e.Fragment,{children:[e.jsxDEV(w,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:752,columnNumber:27},globalThis),e.jsxDEV("span",{children:"Assessment Processed"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:753,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:751,columnNumber:25},globalThis):e.jsxDEV(e.Fragment,{children:[e.jsxDEV(I,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:757,columnNumber:27},globalThis),e.jsxDEV("span",{children:"Process Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:758,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:756,columnNumber:25},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:731,columnNumber:21},globalThis),c.processedStatus&&e.jsxDEV("button",{onClick:()=>oe(!0),className:"btn-outline flex items-center space-x-2 px-6 py-3 text-lg",title:"View detailed assessment statistics and analytics",children:[e.jsxDEV(R,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:770,columnNumber:25},globalThis),e.jsxDEV("span",{children:"See Stats"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:771,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:765,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:730,columnNumber:19},globalThis),!c.canProcess&&!c.processedStatus&&e.jsxDEV("p",{className:"mt-2 text-sm text-gray-500",children:[c.statistics.pendingStudents," student",c.statistics.pendingStudents!==1?"s":""," still need to complete their response",c.statistics.pendingStudents!==1?"s":""]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:777,columnNumber:21},globalThis),c.processedStatus&&e.jsxDEV("p",{className:"mt-2 text-sm text-green-600",children:["Processed on ",B(c.processedStatus.processed_at)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:783,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:729,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:728,columnNumber:15},globalThis),Ee?e.jsxDEV("div",{className:"text-center py-8",children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:793,columnNumber:17},globalThis),e.jsxDEV("p",{className:"mt-2 text-sm text-gray-500",children:"Loading student progress..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:794,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:792,columnNumber:15},globalThis):g.length>0?e.jsxDEV("div",{className:"overflow-x-auto",children:e.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxDEV("thead",{className:"bg-gray-50",children:e.jsxDEV("tr",{children:[e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:801,columnNumber:23},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student ID"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:804,columnNumber:23},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:807,columnNumber:23},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Submitted"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:810,columnNumber:23},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Score"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:813,columnNumber:23},globalThis),e.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:816,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:800,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:799,columnNumber:19},globalThis),e.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:g.map(s=>e.jsxDEV("tr",{className:"hover:bg-gray-50",children:[e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:[s.first_name," ",s.last_name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:825,columnNumber:27},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:s.email},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:828,columnNumber:27},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:824,columnNumber:25},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:s.student_id},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:830,columnNumber:25},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxDEV("div",{className:"flex items-center space-x-2",children:[s.assessment_status==="completed"?e.jsxDEV("div",{className:"flex items-center justify-center w-6 h-6 bg-green-100 rounded-full",children:e.jsxDEV(w,{className:"h-4 w-4 text-green-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:838,columnNumber:33},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:837,columnNumber:31},globalThis):e.jsxDEV("div",{className:"flex items-center justify-center w-6 h-6 bg-yellow-100 rounded-full",children:e.jsxDEV(es,{className:"h-4 w-4 text-yellow-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:842,columnNumber:33},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:841,columnNumber:31},globalThis),e.jsxDEV("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${Ue(s.assessment_status)}`,children:s.assessment_status==="completed"?"Completed":"Pending"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:847,columnNumber:29},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:834,columnNumber:27},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:833,columnNumber:25},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:s.submitted_at?B(s.submitted_at):"-"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:852,columnNumber:25},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:s.total_score!==null?`${s.total_score}%`:"-"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:855,columnNumber:25},globalThis),e.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxDEV("div",{className:"flex flex-col lg:flex-row lg:items-center lg:space-x-2 space-y-2 lg:space-y-0",children:[e.jsxDEV("button",{onClick:()=>_e(s),disabled:F[`download-form-${s.id}`],className:"btn-outline text-xs flex items-center space-x-1 px-2 py-1",title:"Download Assessment Form",children:[F[`download-form-${s.id}`]?e.jsxDEV("div",{className:"animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:868,columnNumber:33},globalThis):e.jsxDEV(ss,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:870,columnNumber:33},globalThis),e.jsxDEV("span",{className:"hidden lg:inline",children:"Form"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:872,columnNumber:31},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:861,columnNumber:29},globalThis),e.jsxDEV("button",{onClick:()=>Fe(s),disabled:s.assessment_status==="completed",className:"btn-outline text-xs flex items-center space-x-1 px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed",title:"Upload Student Response",children:[e.jsxDEV(H,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:882,columnNumber:31},globalThis),e.jsxDEV("span",{className:"hidden lg:inline",children:"Upload"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:883,columnNumber:31},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:876,columnNumber:29},globalThis),e.jsxDEV("button",{onClick:()=>Ie(s),disabled:F[`download-pdf-${s.id}`]||s.assessment_status!=="completed",className:"btn-outline text-xs flex items-center space-x-1 px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed",title:"Download Peer Review PDF",children:[F[`download-pdf-${s.id}`]?e.jsxDEV("div",{className:"animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:894,columnNumber:33},globalThis):e.jsxDEV(ts,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:896,columnNumber:33},globalThis),e.jsxDEV("span",{className:"hidden lg:inline",children:"PDF"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:898,columnNumber:31},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:887,columnNumber:29},globalThis),e.jsxDEV("button",{onClick:()=>Qe(s),disabled:s.assessment_status!=="completed",className:"btn-outline text-xs flex items-center space-x-1 px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed",title:"View Statistics",children:[e.jsxDEV(R,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:908,columnNumber:31},globalThis),e.jsxDEV("span",{className:"hidden lg:inline",children:"Stats"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:909,columnNumber:31},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:902,columnNumber:29},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:859,columnNumber:27},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:858,columnNumber:25},globalThis)]},s.id,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:823,columnNumber:23},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:821,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:798,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:797,columnNumber:15},globalThis):e.jsxDEV("div",{className:"text-center py-8",children:[e.jsxDEV(Ne,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:920,columnNumber:17},globalThis),e.jsxDEV("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No students found"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:921,columnNumber:17},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"No students are enrolled in this batch."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:922,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:919,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:725,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:681,columnNumber:9},globalThis),l&&m&&a.role==="student"&&e.jsxDEV("div",{className:"bg-white shadow rounded-lg",children:[e.jsxDEV("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(I,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:936,columnNumber:15},globalThis),"Assessment Access"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:935,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:934,columnNumber:11},globalThis),e.jsxDEV("div",{className:"p-6",children:[e.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(is,{className:"h-5 w-5 text-blue-400 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:944,columnNumber:17},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-blue-900",children:"Assessment Information"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:946,columnNumber:19},globalThis),e.jsxDEV("div",{className:"text-sm text-blue-800 mt-1 space-y-1",children:[e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Assessment:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:948,columnNumber:24},globalThis)," ",m.name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:948,columnNumber:21},globalThis),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Type:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:949,columnNumber:24},globalThis)," ",m.assessment_type]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:949,columnNumber:21},globalThis),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Due Date:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:950,columnNumber:24},globalThis)," ",B(m.due_date)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:950,columnNumber:21},globalThis),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Questions:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:951,columnNumber:24},globalThis)," ",m.question_count]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:951,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:947,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:945,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:943,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:942,columnNumber:13},globalThis),e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("button",{onClick:()=>ue(m),className:"btn-primary flex items-center space-x-2 mx-auto px-8 py-3 text-lg","data-testid":"submit-assessment-button",children:[e.jsxDEV(I,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:963,columnNumber:17},globalThis),e.jsxDEV("span",{children:"Start Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:964,columnNumber:17},globalThis),e.jsxDEV(ls,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:965,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:958,columnNumber:15},globalThis),e.jsxDEV("p",{className:"mt-4 text-sm text-gray-500",children:"Make sure you have enough time to complete the assessment before starting."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:968,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:957,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:941,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:933,columnNumber:9},globalThis),e.jsxDEV(Ns,{isOpen:Te,onClose:()=>{W(!1),S(null)},student:Y,assessment:m,onUpload:$e,uploading:Ce},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:977,columnNumber:7},globalThis),e.jsxDEV(xs,{isOpen:De,onClose:()=>{ie(!1),S(null)},student:Y,assessment:m,onLoadStatistics:qe,loading:Pe},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:990,columnNumber:7},globalThis),Le&&e.jsxDEV("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxDEV("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:e.jsxDEV("div",{className:"mt-3 text-center",children:[e.jsxDEV("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100",children:e.jsxDEV(T,{className:"h-6 w-6 text-yellow-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1008,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1007,columnNumber:15},globalThis),e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 mt-4",children:"Process Assessment for Peer Review?"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1010,columnNumber:15},globalThis),e.jsxDEV("div",{className:"mt-2 px-7 py-3",children:[e.jsxDEV("p",{className:"text-sm text-gray-500",children:['This will process the assessment "',m?.name,'" for peer review allocation. All ',c?.statistics.totalStudents," students have completed their responses."]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1014,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-500 mt-2",children:e.jsxDEV("strong",{children:"This action cannot be undone."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1019,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1018,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1013,columnNumber:15},globalThis),e.jsxDEV("div",{className:"items-center px-4 py-3",children:e.jsxDEV("div",{className:"flex space-x-3",children:[e.jsxDEV("button",{onClick:()=>G(!1),className:"btn-outline flex-1",children:"Cancel"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1024,columnNumber:19},globalThis),e.jsxDEV("button",{onClick:Oe,className:"btn-primary flex-1",children:"Process Assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1030,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1023,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1022,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1006,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1005,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1004,columnNumber:9},globalThis),e.jsxDEV(hs,{isOpen:Ve,onClose:()=>oe(!1),assessment:m,batch:l,onLoadStatistics:ze,loading:!1},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:1044,columnNumber:7},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/TakeAssessment.jsx",lineNumber:420,columnNumber:5},globalThis)};export{js as default};
