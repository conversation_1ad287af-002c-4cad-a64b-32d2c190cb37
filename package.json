{"name": "peer-review-system", "version": "1.0.0", "description": "Comprehensive offline Windows desktop application for peer review assessments", "main": "electron/main.js", "scripts": {"dev": "vite build --watch", "dev:electron": "npm run build && electron .", "dev:quick": "npm run build && cross-env ELECTRON_DEV=true electron .", "build": "vite build", "build:electron": "npm run build && electron-builder --win --publish=never", "preview": "npm run build && cross-env NODE_ENV=production electron .", "preview:vite": "vite preview", "start": "npm run build && electron .", "clean": "rimraf dist dist-electron data node_modules\\.vite", "clean:database": "node scripts/clean-database.js", "verify:production": "node scripts/verify-production-deployment.js", "deploy:prepare": "npm run clean:database && npm run verify:production", "postinstall": "electron-builder install-app-deps", "lint": "eslint . --ext .js,.jsx --report-unused-disable-directives", "lint:fix": "eslint . --ext .js,.jsx --fix", "lint:check": "eslint . --ext .js,.jsx", "lint:strict": "eslint . --ext .js,.jsx --max-warnings 0"}, "keywords": ["peer-review", "education", "assessment", "electron", "react", "desktop-app", "windows", "windows-app"], "author": "Maj. <PERSON><PERSON>", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://www.hrishikeshmohite.com, https://www.ajinkyacreatiion.com", "role": "Senior-<PERSON><PERSON><PERSON>"}], "license": "MILIT", "dependencies": {"bcryptjs": "^2.4.3", "chart.js": "^4.4.9", "clsx": "^2.0.0", "date-fns": "^2.30.0", "dompurify": "^3.2.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "pdfkit": "^0.14.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "sql.js": "^1.13.0", "sqlite3": "^5.1.7", "tailwind-merge": "^2.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@electron/rebuild": "^4.0.1", "@eslint/js": "^9.27.0", "@playwright/test": "^1.52.0", "@types/node": "^22.15.21", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "electron": "^27.1.3", "electron-builder": "^24.6.4", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "playwright-electron": "^0.5.0", "postcss": "^8.4.32", "rimraf": "^5.0.10", "tailwindcss": "^3.3.6", "vite": "^4.5.0"}, "build": {"appId": "com.ajinkyacreatiion.peer-review-system", "productName": "Peer Review System", "copyright": "Copyright © 2024 Ajinkyacreatiion PVT. LTD.", "directories": {"output": "dist-electron", "buildResources": "build"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*", "package.json", "!src/**/*", "!scripts/**/*", "!deploy/**/*", "!docs/**/*", "!tests/**/*", "!**/__tests__/**/*", "!**/*.test.js", "!**/*.spec.js", "!vite.config.js", "!tailwind.config.js", "!postcss.config.js", "!babel.config.js", "!jest.config.js", "!playwright.config.js", "!*.md", "!.giti<PERSON>re", "!.env*"], "extraResources": [{"from": "public/assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "public/icon.ico", "publisherName": "Ajinkyacreatiion PVT. LTD.", "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Peer Review System", "artifactName": "${productName}-Setup-${version}.${ext}", "deleteAppDataOnUninstall": false, "displayLanguageSelector": false, "warningsAsErrors": false, "packElevateHelper": true, "perMachine": false, "runAfterFinish": false, "menuCategory": "Education", "guid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"}, "compression": "maximum", "removePackageScripts": true, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false}}