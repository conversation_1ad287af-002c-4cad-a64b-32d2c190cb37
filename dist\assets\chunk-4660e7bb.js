import { e as validateStudentForm } from './chunk-8b627984.js';

// CSV processing utilities for bulk student upload


// CSV template headers
const CSV_HEADERS = [
  'Full Name',
  'Student ID',
  'Year of Birth',
  'Email'
];

// Generate CSV template content
const generateCSVTemplate = () => {
  const headers = CSV_HEADERS.join(',');
  const exampleData = [
    '<PERSON>,STU001,2000,<EMAIL>',
    '<PERSON>,STU002,1999,<EMAIL>',
    '<PERSON>,STU003,2001,'
  ];

  return headers + '\n' + exampleData.join('\n');
};

// Download CSV template file
const downloadCSVTemplate = () => {
  const csvContent = generateCSVTemplate();
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'student_upload_template.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

// Parse CSV content to array of objects
const parseCSVContent = (csvContent) => {
  try {
    const lines = csvContent.split('\n').filter(line => line.trim());

    if (lines.length < 2) {
      throw new Error('CSV file must contain at least a header row and one data row');
    }

    // Parse header row
    const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));

    // Validate headers
    const requiredHeaders = ['Full Name', 'Student ID', 'Year of Birth'];
    const missingHeaders = requiredHeaders.filter(required =>
      !headers.some(header => header.toLowerCase() === required.toLowerCase())
    );

    if (missingHeaders.length > 0) {
      throw new Error(`Missing required columns: ${missingHeaders.join(', ')}`);
    }

    // Find column indices
    const getColumnIndex = (columnName) => {
      return headers.findIndex(header =>
        header.toLowerCase() === columnName.toLowerCase()
      );
    };

    const fullNameIndex = getColumnIndex('Full Name');
    const studentIdIndex = getColumnIndex('Student ID');
    const yearOfBirthIndex = getColumnIndex('Year of Birth');
    const emailIndex = getColumnIndex('Email');

    // Parse data rows
    const students = [];
    const errors = [];

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) {continue;}

      try {
        // Parse CSV line (handle quoted values)
        const values = parseCSVLine(line);

        if (values.length < Math.max(fullNameIndex, studentIdIndex, yearOfBirthIndex) + 1) {
          errors.push({
            row: i + 1,
            error: 'Insufficient columns in row'
          });
          continue;
        }

        const student = {
          id: Date.now() + i, // Temporary ID for React keys
          fullName: values[fullNameIndex]?.trim() || '',
          studentId: values[studentIdIndex]?.trim().toUpperCase() || '',
          yearOfBirth: values[yearOfBirthIndex]?.trim() || '',
          email: emailIndex >= 0 ? (values[emailIndex]?.trim() || '') : '',
          password: '', // Will be generated later
          rowNumber: i + 1
        };

        students.push(student);
      } catch (error) {
        errors.push({
          row: i + 1,
          error: error.message
        });
      }
    }

    return {
      students,
      errors,
      totalRows: lines.length - 1
    };

  } catch (error) {
    throw new Error(`Failed to parse CSV: ${error.message}`);
  }
};

// Parse a single CSV line handling quoted values
const parseCSVLine = (line) => {
  const values = [];
  let current = '';
  let inQuotes = false;

  for (let i = 0; i < line.length; i++) {
    const char = line[i];

    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      values.push(current);
      current = '';
    } else {
      current += char;
    }
  }

  values.push(current);
  return values.map(value => value.replace(/"/g, ''));
};

// Validate CSV file before processing
const validateCSVFile = (file) => {
  const errors = [];

  if (!file) {
    errors.push('Please select a file');
    return errors;
  }

  // Check file type
  if (file.type !== 'text/csv' && !file.name.toLowerCase().endsWith('.csv')) {
    errors.push('File must be a CSV file');
  }

  // Check file size (5MB limit)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    errors.push('File size must be less than 5MB');
  }

  // Check if file is empty
  if (file.size === 0) {
    errors.push('File cannot be empty');
  }

  return errors;
};

// Read CSV file content
const readCSVFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const content = event.target.result;
        resolve(content);
      } catch (error) {
        reject(new Error(`Failed to read file content: ${error.message}`));
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsText(file);
  });
};

// Validate parsed students data
const validateCSVStudents = (students) => {
  const validationResults = {
    validStudents: [],
    invalidStudents: [],
    duplicateIds: []
  };

  // Check for duplicate student IDs within the CSV
  const studentIds = students.map(s => s.studentId).filter(id => id);
  const duplicateIds = studentIds.filter((id, index) =>
    studentIds.indexOf(id) !== index
  );

  students.forEach((student, index) => {
    const errors = validateStudentForm(student);

    if (Object.keys(errors).length === 0) {
      validationResults.validStudents.push(student);
    } else {
      validationResults.invalidStudents.push({
        ...student,
        errors,
        index
      });
    }
  });

  validationResults.duplicateIds = [...new Set(duplicateIds)];

  return validationResults;
};

// Generate corrected CSV for failed entries
const generateCorrectedCSV = (invalidStudents) => {
  const headers = CSV_HEADERS.join(',');
  const rows = invalidStudents.map(student => {
    const errorComments = Object.values(student.errors)
      .flat()
      .join('; ');

    return [
      student.fullName,
      student.studentId,
      student.yearOfBirth,
      student.email,
      `"Errors: ${errorComments}"`
    ].join(',');
  });

  return headers + ',Errors\n' + rows.join('\n');
};

// Download corrected CSV file
const downloadCorrectedCSV = (invalidStudents) => {
  const csvContent = generateCorrectedCSV(invalidStudents);
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'student_upload_errors.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

// Merge CSV students with existing inline students
const mergeStudentsData = (existingStudents, csvStudents) => {
  const existingIds = new Set(existingStudents.map(s => s.studentId.toUpperCase()));

  // Filter out CSV students that already exist
  const newStudents = csvStudents.filter(csvStudent =>
    !existingIds.has(csvStudent.studentId.toUpperCase())
  );

  const duplicates = csvStudents.filter(csvStudent =>
    existingIds.has(csvStudent.studentId.toUpperCase())
  );

  return {
    mergedStudents: [...existingStudents, ...newStudents],
    newStudents,
    duplicates,
    totalAdded: newStudents.length
  };
};

// Export students data to CSV
const exportStudentsToCSV = (students, filename = 'students_export.csv') => {
  const headers = ['Full Name', 'Student ID', 'Username', 'Email', 'Year of Birth', 'Status'];
  const rows = students.map(student => [
    `${student.first_name} ${student.last_name}`,
    student.student_id,
    student.username,
    student.email,
    student.year_of_birth || '',
    student.is_active ? 'Active' : 'Inactive'
  ]);

  const csvContent = headers.join(',') + '\n' +
    rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

export { downloadCorrectedCSV as a, validateCSVStudents as b, downloadCSVTemplate as d, exportStudentsToCSV as e, mergeStudentsData as m, parseCSVContent as p, readCSVFile as r, validateCSVFile as v };
