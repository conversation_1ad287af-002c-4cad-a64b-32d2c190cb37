const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Database operations
  query: (query, params) => ipcRenderer.invoke('db:query', query, params),
  run: (query, params) => ipcRenderer.invoke('db:run', query, params),

  // User management
  users: {
    create: (userData) => ipcRenderer.invoke('users:create', userData),
    authenticate: (credentials) => ipcRenderer.invoke('users:authenticate', credentials),
    getAll: (filters) => ipcRenderer.invoke('users:getAll', filters),
  },

  // Admin management
  admin: {
    updateUser: (userId, userData, updatedBy) => ipcRenderer.invoke('admin:updateUser', userId, userData, updatedBy),
    resetPassword: (userId, newPassword, resetBy) => ipcRenderer.invoke('admin:resetPassword', userId, newPassword, resetBy),
    deactivateUser: (userId, deactivatedBy) => ipcRenderer.invoke('admin:deactivateUser', userId, deactivatedBy),
    activateUser: (userId, activatedBy) => ipcRenderer.invoke('admin:activateUser', userId, activatedBy),
    generatePassword: () => ipcRenderer.invoke('admin:generatePassword'),
    getAuditLog: (filters) => ipcRenderer.invoke('admin:getAuditLog', filters),
  },

  // Batch management
  batches: {
    create: (batchData) => ipcRenderer.invoke('batches:create', batchData),
    getAll: (filters) => ipcRenderer.invoke('batches:getAll', filters),
    createWithStudents: (batchData, studentsData, createdBy) => ipcRenderer.invoke('batches:createWithStudents', batchData, studentsData, createdBy),
    checkNameUnique: (name, teacherId, excludeBatchId) => ipcRenderer.invoke('batches:checkNameUnique', name, teacherId, excludeBatchId),
    checkStudentIdsUnique: (studentIds, institutionId) => ipcRenderer.invoke('batches:checkStudentIdsUnique', studentIds, institutionId),
    getDetails: (batchId, teacherId) => ipcRenderer.invoke('batches:getDetails', batchId, teacherId),
    update: (batchId, batchData, updatedBy, teacherId) => ipcRenderer.invoke('batches:update', batchId, batchData, updatedBy, teacherId),
    addStudents: (batchId, studentsData, addedBy, teacherId) => ipcRenderer.invoke('batches:addStudents', batchId, studentsData, addedBy, teacherId),
    removeStudent: (batchId, studentId, removedBy, teacherId) => ipcRenderer.invoke('batches:removeStudent', batchId, studentId, removedBy, teacherId),
    archive: (batchId, archivedBy, teacherId) => ipcRenderer.invoke('batches:archive', batchId, archivedBy, teacherId),
    activate: (batchId, activatedBy, teacherId) => ipcRenderer.invoke('batches:activate', batchId, activatedBy, teacherId),
    getStatistics: (teacherId) => ipcRenderer.invoke('batches:getStatistics', teacherId),
  },

  // Enhanced Assessment management
  assessments: {
    createWithQuestions: (assessmentData, questionsData, createdBy) => ipcRenderer.invoke('assessments:createWithQuestions', assessmentData, questionsData, createdBy),
    checkNameUnique: (name, institutionId, excludeAssessmentId) => ipcRenderer.invoke('assessments:checkNameUnique', name, institutionId, excludeAssessmentId),
    getAll: (filters) => ipcRenderer.invoke('assessments:getAll', filters),
    getDetails: (assessmentId, institutionId) => ipcRenderer.invoke('assessments:getDetails', assessmentId, institutionId),
    publish: (assessmentId, publishedBy, institutionId) => ipcRenderer.invoke('assessments:publish', assessmentId, publishedBy, institutionId),
    assignToBatches: (assessmentId, batchIds, assignedBy, institutionId) => ipcRenderer.invoke('assessments:assignToBatches', assessmentId, batchIds, assignedBy, institutionId),
    delete: (assessmentId, deletedBy, institutionId) => ipcRenderer.invoke('assessments:delete', assessmentId, deletedBy, institutionId),
    // Assessment access functions
    getUserAccessibleBatches: (userId, userRole, institutionId) => ipcRenderer.invoke('assessments:getUserAccessibleBatches', userId, userRole, institutionId),
    getAssessmentsForBatch: (batchId, userId, userRole) => ipcRenderer.invoke('assessments:getAssessmentsForBatch', batchId, userId, userRole),
    getBatchStudentsWithProgress: (batchId, assessmentId, userId, userRole) => ipcRenderer.invoke('assessments:getBatchStudentsWithProgress', batchId, assessmentId, userId, userRole),
    getStudentAssessmentHistory: (studentId, assessmentId, batchId) => ipcRenderer.invoke('assessments:getStudentAssessmentHistory', studentId, assessmentId, batchId),
    canStudentAccessAssessment: (studentId, assessmentId) => ipcRenderer.invoke('assessments:canStudentAccessAssessment', studentId, assessmentId),
    // Student management functions
    generateAssessmentForm: (assessmentId, teacherId, institutionId) => ipcRenderer.invoke('assessments:generateAssessmentForm', assessmentId, teacherId, institutionId),
    uploadStudentResponse: (assessmentId, studentId, responses, uploadedBy, institutionId) => ipcRenderer.invoke('assessments:uploadStudentResponse', assessmentId, studentId, responses, uploadedBy, institutionId),
    getStudentStatistics: (studentId, assessmentId, teacherId, institutionId) => ipcRenderer.invoke('assessments:getStudentStatistics', studentId, assessmentId, teacherId, institutionId),
    // Assessment processing functions
    getBatchCompletionStatus: (batchId, assessmentId, teacherId, institutionId) => ipcRenderer.invoke('assessments:getBatchCompletionStatus', batchId, assessmentId, teacherId, institutionId),
    processForPeerReview: (assessmentId, batchId, processedBy, institutionId) => ipcRenderer.invoke('assessments:processForPeerReview', assessmentId, batchId, processedBy, institutionId),
    canProcess: (assessmentId, batchId, teacherId) => ipcRenderer.invoke('assessments:canProcess', assessmentId, batchId, teacherId),
    getBatchSubmissionStatus: (batchId, assessmentId, teacherId) => ipcRenderer.invoke('assessments:getBatchSubmissionStatus', batchId, assessmentId, teacherId),
    // Assessment statistics functions
    getStatistics: (assessmentId, batchId, teacherId) => ipcRenderer.invoke('assessments:getStatistics', assessmentId, batchId, teacherId),
  },

  // Settings API
  settings: {
    getUserSettings: (userId) => ipcRenderer.invoke('settings:getUserSettings', userId),
    updateUserSetting: (userId, settingKey, settingValue) => ipcRenderer.invoke('settings:updateUserSetting', userId, settingKey, settingValue),
    getApplicationSettings: () => ipcRenderer.invoke('settings:getApplicationSettings'),
    updateApplicationSetting: (settingKey, settingValue, description) => ipcRenderer.invoke('settings:updateApplicationSetting', settingKey, settingValue, description),
    get: (key) => ipcRenderer.invoke('settings:get', key),
    set: (key, value) => ipcRenderer.invoke('settings:set', key, value),
  },

  // Institution API
  institution: {
    getDetails: (institutionId) => ipcRenderer.invoke('institution:getDetails', institutionId),
    updateDetails: (institutionId, institutionData, detailsData, updatedBy) => ipcRenderer.invoke('institution:updateDetails', institutionId, institutionData, detailsData, updatedBy),
    get: () => ipcRenderer.invoke('institution:get'),
    update: (institutionData, updatedBy) => ipcRenderer.invoke('institution:update', institutionData, updatedBy),
    uploadLogo: (logoData, updatedBy) => ipcRenderer.invoke('institution:uploadLogo', logoData, updatedBy),
    removeLogo: (updatedBy) => ipcRenderer.invoke('institution:removeLogo', updatedBy),
    getLogoPath: (logoFileName) => ipcRenderer.invoke('institution:getLogoPath', logoFileName),
  },

  // Dashboard API
  dashboard: {
    getStatistics: (userId, userRole, institutionId) => ipcRenderer.invoke('dashboard:getStatistics', userId, userRole, institutionId),
  },

  // Peer review operations
  reviews: {
    submit: (reviewData) => ipcRenderer.invoke('reviews:submit', reviewData),
    getStats: (filters) => ipcRenderer.invoke('reviews:getStats', filters),
  },

  // File operations
  file: {
    showSaveDialog: (options) => ipcRenderer.invoke('file:showSaveDialog', options),
  },

  // PDF generation
  pdf: {
    generateReport: (reportData) => ipcRenderer.invoke('pdf:generateReport', reportData),
  },

  // Offline application - no auto-updater API needed
  updater: {
    getVersion: () => ipcRenderer.invoke('updater:getVersion'),
  },

  // Logging API
  logger: {
    writeLogs: (logs) => ipcRenderer.invoke('logger:writeLogs', logs),
  },

  // Configuration API
  config: {
    get: (key) => ipcRenderer.invoke('config:get', key),
    set: (key, value) => ipcRenderer.invoke('config:set', key, value),
    getAll: () => ipcRenderer.invoke('config:getAll'),
    setAll: (config) => ipcRenderer.invoke('config:setAll', config),
    clear: () => ipcRenderer.invoke('config:clear'),
  },

  // Safe storage API
  safeStorage: {
    isEncryptionAvailable: () => ipcRenderer.invoke('safeStorage:isEncryptionAvailable'),
    encryptString: (plainText) => ipcRenderer.invoke('safeStorage:encryptString', plainText),
    decryptString: (encryptedData) => ipcRenderer.invoke('safeStorage:decryptString', encryptedData),
  },

  // First-time setup API
  setup: {
    checkStatus: () => ipcRenderer.invoke('setup:checkStatus'),
    createSuperAdmin: (setupData) => ipcRenderer.invoke('setup:createSuperAdmin', setupData),
  },

  // Backup system API
  backup: {
    create: (backupOptions) => ipcRenderer.invoke('backup:create', backupOptions),
    restore: (restoreOptions) => ipcRenderer.invoke('backup:restore', restoreOptions),
    list: (location) => ipcRenderer.invoke('backup:list', location),
    delete: (backupPath) => ipcRenderer.invoke('backup:delete', backupPath),
    verify: (backupPath) => ipcRenderer.invoke('backup:verify', backupPath),
  },

  // Menu system API
  menu: {
    setUserContext: (user) => ipcRenderer.invoke('menu:setUserContext', user),
    updateMenu: () => ipcRenderer.invoke('menu:updateMenu'),
  },

  // Offline application - no external links

  // General invoke method for any IPC call
  invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),

  // Event listeners for menu actions
  on: (channel, callback) => {
    const subscription = (event, ...args) => callback(...args);
    ipcRenderer.on(channel, subscription);
    return () => ipcRenderer.removeListener(channel, subscription);
  },

  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
});
