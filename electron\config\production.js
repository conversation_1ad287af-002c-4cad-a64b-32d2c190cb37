/**
 * Production configuration for Peer Review System
 */

const path = require('path');
const os = require('os');

const productionConfig = {
  // Application settings
  app: {
    name: 'Peer Review System',
    version: '1.0.0',
    author: 'ACPL',
    description: 'Comprehensive offline Windows desktop application for peer review assessments',

    // Window settings
    window: {
      width: 1200,
      height: 800,
      minWidth: 1024,
      minHeight: 768,
      show: false, // Don't show until ready
      webSecurity: true,
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      allowRunningInsecureContent: false,
      experimentalFeatures: false
    },

    // Offline application - no auto-updater needed
  },

  // Database configuration
  database: {
    // Use app data directory for production
    path: path.join(os.homedir(), 'AppData', 'Local', 'PeerReviewSystem', 'data', 'peer_review.db'),

    // Connection settings
    options: {
      busyTimeout: 30000,
      synchronous: 'NORMAL',
      journalMode: 'WAL',
      cacheSize: -64000, // 64MB cache
      tempStore: 'MEMORY',
      mmapSize: 268435456, // 256MB mmap

      // Security settings
      foreignKeys: true,
      recursiveTriggers: true,
      secureDelete: true
    },

    // Backup settings
    backup: {
      enabled: true,
      location: path.join(os.homedir(), 'Documents', 'PeerReviewBackups'),
      retention: {
        daily: 7,
        weekly: 4,
        monthly: 12
      },
      compression: true,
      encryption: {
        algorithm: 'aes-256-cbc',
        keyDerivation: 'scrypt',
        iterations: 100000
      }
    }
  },

  // Security configuration
  security: {
    // Password hashing
    bcrypt: {
      rounds: 8 // Optimized for performance while maintaining security
    },

    // JWT settings
    jwt: {
      expiresIn: '4h',
      algorithm: 'HS256',
      issuer: 'peer-review-system',
      audience: 'peer-review-users'
    },

    // Session management
    session: {
      timeout: 14400000, // 4 hours in milliseconds
      renewThreshold: 3600000, // 1 hour before expiry
      maxConcurrentSessions: 1
    },

    // Content Security Policy
    csp: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", 'data:', 'blob:'],
      fontSrc: ["'self'"],
      connectSrc: ["'self'"],
      mediaSrc: ["'none'"],
      objectSrc: ["'none'"],
      childSrc: ["'none'"],
      frameSrc: ["'none'"],
      workerSrc: ["'none'"],
      manifestSrc: ["'none'"]
    }
  },

  // Logging configuration
  logging: {
    enabled: true,
    level: 'error', // Only log errors in production
    location: path.join(os.homedir(), 'AppData', 'Local', 'PeerReviewSystem', 'logs'),

    // File rotation
    rotation: {
      maxSize: '10MB',
      maxFiles: 10,
      datePattern: 'YYYY-MM-DD'
    },

    // What to log
    categories: {
      database: true,
      authentication: true,
      backup: true,
      errors: true,
      performance: false, // Disabled in production
      debug: false // Disabled in production
    }
  },

  // Performance settings
  performance: {
    // Memory management
    memory: {
      maxOldSpaceSize: 512, // MB
      maxSemiSpaceSize: 64   // MB
    },

    // Database optimization
    database: {
      connectionPoolSize: 5,
      queryTimeout: 30000,
      transactionTimeout: 60000,
      batchSize: 1000
    },

    // File operations
    files: {
      maxFileSize: 50 * 1024 * 1024, // 50MB
      allowedTypes: ['.pdf', '.jpg', '.jpeg', '.png', '.csv', '.xlsx'],
      uploadTimeout: 300000 // 5 minutes
    }
  },

  // Feature flags
  features: {
    // Core features
    firstTimeSetup: true,
    backupRestore: true,
    userManagement: true,
    assessmentCreation: true,
    peerReview: true,
    reporting: true,

    // Advanced features
    bulkOperations: true,
    dataExport: true,
    auditLogging: true,

    // Production features only
    debugMode: false,
    devTools: false,
    hotReload: false,
    mockData: false
  },

  // Error handling
  errorHandling: {
    // Global error handling
    captureUnhandledRejections: true,
    captureUncaughtExceptions: true,

    // User-friendly error messages
    showTechnicalDetails: false,
    enableErrorReporting: true,

    // Recovery options
    autoRestart: {
      enabled: true,
      maxAttempts: 3,
      delay: 5000 // 5 seconds
    }
  },

  // Offline application - no update configuration needed

  // Paths
  paths: {
    userData: path.join(os.homedir(), 'AppData', 'Local', 'PeerReviewSystem'),
    documents: path.join(os.homedir(), 'Documents'),
    temp: path.join(os.tmpdir(), 'PeerReviewSystem'),
    logs: path.join(os.homedir(), 'AppData', 'Local', 'PeerReviewSystem', 'logs'),
    backups: path.join(os.homedir(), 'Documents', 'PeerReviewBackups'),
    exports: path.join(os.homedir(), 'Documents', 'PeerReviewExports')
  },

  // Environment
  environment: 'production',
  isDevelopment: false,
  isProduction: true,

  // Build information
  build: {
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch
  }
};

module.exports = productionConfig;
