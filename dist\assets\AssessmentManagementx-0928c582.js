import { r as reactExports, j as jsxDevRuntimeExports } from './chunk-2ef8e52b.js';
import { b as useDatabase, u as useAuth } from './main-56ca96d9.js';
import { y as Plus, A as AlertCircle, C as CheckCircle, F as FileText, U as Users, g as Settings, w as Calendar, T as Trash2 } from './chunk-028772a4.js';
import './chunk-03d61bd9.js';

const AssessmentManagement = () => {
  const { getAllAssessments, createAssessment, getAllBatches } = useDatabase();
  const { user } = useAuth();
  const [assessments, setAssessments] = reactExports.useState([]);
  const [batches, setBatches] = reactExports.useState([]);
  const [loading, setLoading] = reactExports.useState(true);
  const [showCreateModal, setShowCreateModal] = reactExports.useState(false);
  const [message, setMessage] = reactExports.useState({ type: "", text: "" });
  const [newAssessment, setNewAssessment] = reactExports.useState({
    title: "",
    description: "",
    batchId: "",
    criteria: [
      { name: "Communication Skills", weight: 25, maxScore: 10 },
      { name: "Technical Knowledge", weight: 25, maxScore: 10 },
      { name: "Teamwork", weight: 25, maxScore: 10 },
      { name: "Problem Solving", weight: 25, maxScore: 10 }
    ],
    scoringSystem: {
      type: "points",
      // points, percentage, letter
      maxScore: 100,
      passingScore: 60
    },
    startDate: "",
    endDate: ""
  });
  reactExports.useEffect(() => {
    loadData();
  }, []);
  const loadData = async () => {
    try {
      setLoading(true);
      const [assessmentData, batchData] = await Promise.all([
        getAllAssessments(),
        getAllBatches()
      ]);
      setAssessments(assessmentData);
      setBatches(batchData);
    } catch {
      setMessage({ type: "error", text: "Failed to load data" });
    } finally {
      setLoading(false);
    }
  };
  const handleCreateAssessment = async (e) => {
    e.preventDefault();
    try {
      await createAssessment({
        ...newAssessment,
        createdBy: user.id,
        maxScore: newAssessment.scoringSystem.maxScore
      });
      setMessage({ type: "success", text: "Assessment created successfully" });
      setShowCreateModal(false);
      resetForm();
      loadData();
    } catch (error) {
      setMessage({ type: "error", text: error.message });
    }
  };
  const resetForm = () => {
    setNewAssessment({
      title: "",
      description: "",
      batchId: "",
      criteria: [
        { name: "Communication Skills", weight: 25, maxScore: 10 },
        { name: "Technical Knowledge", weight: 25, maxScore: 10 },
        { name: "Teamwork", weight: 25, maxScore: 10 },
        { name: "Problem Solving", weight: 25, maxScore: 10 }
      ],
      scoringSystem: {
        type: "points",
        maxScore: 100,
        passingScore: 60
      },
      startDate: "",
      endDate: ""
    });
  };
  const addCriterion = () => {
    setNewAssessment((prev) => ({
      ...prev,
      criteria: [...prev.criteria, { name: "", weight: 0, maxScore: 10 }]
    }));
  };
  const updateCriterion = (index, field, value) => {
    setNewAssessment((prev) => ({
      ...prev,
      criteria: prev.criteria.map(
        (criterion, i) => i === index ? { ...criterion, [field]: value } : criterion
      )
    }));
  };
  const removeCriterion = (index) => {
    setNewAssessment((prev) => ({
      ...prev,
      criteria: prev.criteria.filter((_, i) => i !== index)
    }));
  };
  const getTotalWeight = () => {
    return newAssessment.criteria.reduce((sum, criterion) => sum + (parseFloat(criterion.weight) || 0), 0);
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-2xl font-bold text-gray-900", children: "Assessment Management" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 136,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: "Create and manage peer review assessments" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 137,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 135,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: () => setShowCreateModal(true),
          className: "btn-primary flex items-center space-x-2",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Plus, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 143,
              columnNumber: 11
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Create Assessment" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 144,
              columnNumber: 11
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 139,
          columnNumber: 9
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
      lineNumber: 134,
      columnNumber: 7
    }, globalThis),
    message.text && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `rounded-md p-4 ${message.type === "error" ? "bg-red-50" : "bg-green-50"}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: message.type === "error" ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5 text-red-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 154,
        columnNumber: 17
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-green-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 156,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 152,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: `text-sm ${message.type === "error" ? "text-red-800" : "text-green-800"}`, children: message.text }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 160,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 159,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
      lineNumber: 151,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
      lineNumber: 150,
      columnNumber: 9
    }, globalThis),
    loading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-8", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 171,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-gray-500", children: "Loading assessments..." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 172,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
      lineNumber: 170,
      columnNumber: 9
    }, globalThis) : assessments.length > 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg overflow-hidden", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-6 py-4 border-b border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "h-5 w-5 mr-2" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 178,
          columnNumber: 15
        }, globalThis),
        "Assessments (",
        assessments.length,
        ")"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 177,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 176,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "divide-y divide-gray-200", children: assessments.map((assessment) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6 hover:bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900", children: assessment.title }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 188,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600 mt-1", children: assessment.description }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 189,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-3 flex items-center space-x-6 text-sm text-gray-500", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-4 w-4 mr-1" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 193,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: [
                "Batch: ",
                assessment.batch_name
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 194,
                columnNumber: 25
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 192,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Settings, { className: "h-4 w-4 mr-1" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 197,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: [
                "Max Score: ",
                assessment.max_score
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 198,
                columnNumber: 25
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 196,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Calendar, { className: "h-4 w-4 mr-1" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 201,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: [
                "Created: ",
                new Date(assessment.created_at).toLocaleDateString()
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 202,
                columnNumber: 25
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 200,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 191,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 187,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${assessment.is_active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`, children: assessment.is_active ? "Active" : "Inactive" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 208,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: "text-red-600 hover:text-red-900", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Trash2, { className: "h-4 w-4" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 214,
            columnNumber: 23
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 213,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 207,
          columnNumber: 19
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 186,
        columnNumber: 17
      }, globalThis) }, assessment.id, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 185,
        columnNumber: 15
      }, globalThis)) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 183,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
      lineNumber: 175,
      columnNumber: 9
    }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-12", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 224,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No assessments found" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 225,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "Get started by creating your first assessment." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 226,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
      lineNumber: 223,
      columnNumber: 9
    }, globalThis),
    showCreateModal && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-3", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "Create New Assessment" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 237,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("form", { onSubmit: handleCreateAssessment, className: "space-y-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Assessment Title *" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 243,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "text",
                required: true,
                className: "input",
                value: newAssessment.title,
                onChange: (e) => setNewAssessment((prev) => ({ ...prev, title: e.target.value })),
                placeholder: "Enter assessment title"
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 246,
                columnNumber: 21
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 242,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Select Batch *" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 257,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "select",
              {
                required: true,
                className: "input",
                value: newAssessment.batchId,
                onChange: (e) => setNewAssessment((prev) => ({ ...prev, batchId: e.target.value })),
                children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "", children: "Select a batch" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                    lineNumber: 266,
                    columnNumber: 23
                  }, globalThis),
                  batches.map((batch) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: batch.id, children: [
                    batch.name,
                    " (",
                    batch.student_count,
                    " students)"
                  ] }, batch.id, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                    lineNumber: 268,
                    columnNumber: 25
                  }, globalThis))
                ]
              },
              void 0,
              true,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 260,
                columnNumber: 21
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 256,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 241,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Description" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 277,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "textarea",
            {
              className: "input",
              rows: 3,
              value: newAssessment.description,
              onChange: (e) => setNewAssessment((prev) => ({ ...prev, description: e.target.value })),
              placeholder: "Enter assessment description"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 280,
              columnNumber: 19
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 276,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mb-3", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700", children: [
              "Assessment Criteria (Total Weight: ",
              getTotalWeight(),
              "%)"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 292,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                type: "button",
                onClick: addCriterion,
                className: "btn-outline text-sm",
                children: "Add Criterion"
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 295,
                columnNumber: 21
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 291,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-3", children: newAssessment.criteria.map((criterion, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3 p-3 border rounded-md", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "text",
                placeholder: "Criterion name",
                className: "input flex-1",
                value: criterion.name,
                onChange: (e) => updateCriterion(index, "name", e.target.value)
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 307,
                columnNumber: 25
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "number",
                placeholder: "Weight %",
                className: "input w-24",
                value: criterion.weight,
                onChange: (e) => updateCriterion(index, "weight", parseFloat(e.target.value) || 0)
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 314,
                columnNumber: 25
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "number",
                placeholder: "Max Score",
                className: "input w-24",
                value: criterion.maxScore,
                onChange: (e) => updateCriterion(index, "maxScore", parseFloat(e.target.value) || 0)
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 321,
                columnNumber: 25
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                type: "button",
                onClick: () => removeCriterion(index),
                className: "text-red-600 hover:text-red-900",
                children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Trash2, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                  lineNumber: 333,
                  columnNumber: 27
                }, globalThis)
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 328,
                columnNumber: 25
              },
              globalThis
            )
          ] }, index, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 306,
            columnNumber: 23
          }, globalThis)) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 304,
            columnNumber: 19
          }, globalThis),
          getTotalWeight() !== 100 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-orange-600 mt-2", children: "Warning: Total weight should equal 100%" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 340,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 290,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Start Date" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 349,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "datetime-local",
                className: "input",
                value: newAssessment.startDate,
                onChange: (e) => setNewAssessment((prev) => ({ ...prev, startDate: e.target.value }))
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 352,
                columnNumber: 21
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 348,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "End Date" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 361,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "datetime-local",
                className: "input",
                value: newAssessment.endDate,
                onChange: (e) => setNewAssessment((prev) => ({ ...prev, endDate: e.target.value }))
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
                lineNumber: 364,
                columnNumber: 21
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
            lineNumber: 360,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 347,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end space-x-3 pt-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              type: "button",
              onClick: () => setShowCreateModal(false),
              className: "btn-outline",
              children: "Cancel"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 374,
              columnNumber: 19
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              type: "submit",
              className: "btn-primary",
              disabled: getTotalWeight() !== 100,
              children: "Create Assessment"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
              lineNumber: 381,
              columnNumber: 19
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
          lineNumber: 373,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
        lineNumber: 239,
        columnNumber: 15
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
      lineNumber: 236,
      columnNumber: 13
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
      lineNumber: 235,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
      lineNumber: 234,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/AssessmentManagement.jsx",
    lineNumber: 132,
    columnNumber: 5
  }, globalThis);
};

export { AssessmentManagement as default };
