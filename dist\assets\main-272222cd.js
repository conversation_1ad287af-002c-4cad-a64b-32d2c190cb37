import{r as a,j as e,u as R,a as ae,O as ne,R as k,H as K,b as z,c as P,N as q,d as ce}from"./chunk-81a058b1.js";import{C as me,A as Y,a as ue,I as Z,R as pe,E as de,X as _,B as ee,S as be,b as he,c as Ne,G as Ae,d as xe,H as fe,U as ve,e as ge,F as we,f as G,g as se,L as ye,M as Ee,h as je,i as Pe,K as H,j as Ce,k as Le,l as De,m as Ve}from"./chunk-0b87a8e8.js";import"./chunk-81a949b4.js";(function(){const u=document.createElement("link").relList;if(u&&u.supports&&u.supports("modulepreload"))return;for(const p of document.querySelectorAll('link[rel="modulepreload"]'))l(p);new MutationObserver(p=>{for(const m of p)if(m.type==="childList")for(const h of m.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&l(h)}).observe(document,{childList:!0,subtree:!0});function n(p){const m={};return p.integrity&&(m.integrity=p.integrity),p.referrerPolicy&&(m.referrerPolicy=p.referrerPolicy),p.crossOrigin==="use-credentials"?m.credentials="include":p.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function l(p){if(p.ep)return;p.ep=!0;const m=n(p);fetch(p.href,m)}})();const Se="modulepreload",Te=function(i,u){return new URL(i,u).href},J={},L=function(u,n,l){if(!n||n.length===0)return u();const p=document.getElementsByTagName("link");return Promise.all(n.map(m=>{if(m=Te(m,l),m in J)return;J[m]=!0;const h=m.endsWith(".css"),N=h?'[rel="stylesheet"]':"";if(!!l)for(let w=p.length-1;w>=0;w--){const d=p[w];if(d.href===m&&(!h||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${m}"]${N}`))return;const x=document.createElement("link");if(x.rel=h?"stylesheet":Se,h||(x.as="script",x.crossOrigin=""),x.href=m,document.head.appendChild(x),h)return new Promise((w,d)=>{x.addEventListener("load",w),x.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${m}`)))})})).then(()=>u()).catch(m=>{const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=m,window.dispatchEvent(h),!h.defaultPrevented)throw m})},te=a.createContext(),W=()=>{const i=a.useContext(te);if(!i)throw new Error("useAuth must be used within an AuthProvider");return i},Ie=({children:i})=>{const u={id:1,username:"admin",password:"password",first_name:"System",last_name:"Administrator",email:"<EMAIL>",role:"super_admin",institution_id:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},[n,l]=a.useState(null),[p,m]=a.useState(!0);a.useEffect(()=>{(async()=>{try{const b=localStorage.getItem("selectedRole");if(b){const A={...u,role:b,first_name:h(b),last_name:"User"};l(A),localStorage.setItem("peer_review_user",JSON.stringify(A))}else{const A=localStorage.getItem("peer_review_user");if(A)try{const y=JSON.parse(A);l(y)}catch{localStorage.removeItem("peer_review_user")}}}catch{}finally{m(!1)}})()},[]);const h=d=>{switch(d){case"super_admin":return"Administrator";case"admin":return"Administrator";case"teacher":return"Teacher";case"student":return"Student";default:return"User"}},N=async d=>{try{if(m(!0),d&&(d.username==="admin"||d.password==="password")){const A=n||u;return l(A),localStorage.setItem("peer_review_user",JSON.stringify(A)),{success:!0,user:A}}const b=n||u;return l(b),localStorage.setItem("peer_review_user",JSON.stringify(b)),{success:!0,user:b}}catch(b){return{success:!1,error:b.message}}finally{m(!1)}},g=a.useCallback(()=>{l(null),localStorage.removeItem("peer_review_user"),localStorage.removeItem("selectedRole")},[]),w={user:n,login:N,logout:g,updateUser:d=>{l(d),localStorage.setItem("peer_review_user",JSON.stringify(d))},loading:p,isAuthenticated:!!n,isAdmin:n?.role==="admin"||n?.role==="super_admin",isSuperAdmin:n?.role==="super_admin",isTeacher:n?.role==="teacher",isStudent:n?.role==="student"};return e.jsxDEV(te.Provider,{value:w,children:i},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/AuthContext.jsx",lineNumber:145,columnNumber:5},globalThis)},re=a.createContext(),Me=()=>typeof window<"u"&&window.electronAPI,c=()=>{if(Me())return window.electronAPI;throw new Error("Electron API not available. Please ensure you are running the desktop application.")},Us=()=>{const i=a.useContext(re);if(!i)throw new Error("useDatabase must be used within a DatabaseProvider");return i},ke=({children:i})=>{const le={createUser:async t=>{try{return await c().users.create(t)}catch(s){throw new Error(`Failed to create user: ${s.message}`)}},getAllUsers:async(t={})=>{try{return await c().users.getAll(t)}catch(s){throw new Error(`Failed to get users: ${s.message}`)}},updateUser:async(t,s,r)=>{try{return await c().admin.updateUser(t,s,r)}catch(o){throw new Error(`Failed to update user: ${o.message}`)}},resetUserPassword:async(t,s,r)=>{try{return await c().admin.resetPassword(t,s,r)}catch(o){throw new Error(`Failed to reset password: ${o.message}`)}},deactivateUser:async(t,s)=>{try{return await c().admin.deactivateUser(t,s)}catch(r){throw new Error(`Failed to deactivate user: ${r.message}`)}},activateUser:async(t,s)=>{try{return await c().admin.activateUser(t,s)}catch(r){throw new Error(`Failed to activate user: ${r.message}`)}},generateSecurePassword:async()=>{try{return await c().admin.generatePassword()}catch(t){throw new Error(`Failed to generate password: ${t.message}`)}},getAuditLog:async(t={})=>{try{return await c().admin.getAuditLog(t)}catch(s){throw new Error(`Failed to get audit log: ${s.message}`)}},createBatch:async t=>{try{return await c().batches.create(t)}catch(s){throw new Error(`Failed to create batch: ${s.message}`)}},getAllBatches:async(t={})=>{try{return await c().batches.getAll(t)}catch(s){throw new Error(`Failed to get batches: ${s.message}`)}},createBatchWithStudents:async(t,s,r)=>{try{return await c().batches.createWithStudents(t,s,r)}catch(o){throw new Error(`Failed to create batch with students: ${o.message}`)}},checkBatchNameUnique:async(t,s,r)=>{try{return await c().batches.checkNameUnique(t,s,r)}catch(o){throw new Error(`Failed to check batch name uniqueness: ${o.message}`)}},checkStudentIdsUnique:async(t,s)=>{try{return await c().batches.checkStudentIdsUnique(t,s)}catch(r){throw new Error(`Failed to check student ID uniqueness: ${r.message}`)}},getBatchDetails:async(t,s)=>{try{return await c().batches.getDetails(t,s)}catch(r){throw new Error(`Failed to get batch details: ${r.message}`)}},updateBatch:async(t,s,r,o)=>{try{return await c().batches.update(t,s,r,o)}catch(v){throw new Error(`Failed to update batch: ${v.message}`)}},addStudentsToBatch:async(t,s,r,o)=>{try{return await c().batches.addStudents(t,s,r,o)}catch(v){throw new Error(`Failed to add students to batch: ${v.message}`)}},removeStudentFromBatch:async(t,s,r,o)=>{try{return await c().batches.removeStudent(t,s,r,o)}catch(v){throw new Error(`Failed to remove student from batch: ${v.message}`)}},archiveBatch:async(t,s,r)=>{try{return await c().batches.archive(t,s,r)}catch(o){throw new Error(`Failed to archive batch: ${o.message}`)}},activateBatch:async(t,s,r)=>{try{return await c().batches.activate(t,s,r)}catch(o){throw new Error(`Failed to activate batch: ${o.message}`)}},getBatchStatistics:async t=>{try{return await c().batches.getStatistics(t)}catch(s){throw new Error(`Failed to get batch statistics: ${s.message}`)}},createAssessmentWithQuestions:async(t,s,r)=>{try{return await c().assessments.createWithQuestions(t,s,r)}catch(o){throw new Error(`Failed to create assessment: ${o.message}`)}},checkAssessmentNameUnique:async(t,s,r)=>{try{return await c().assessments.checkNameUnique(t,s,r)}catch(o){throw new Error(`Failed to check assessment name uniqueness: ${o.message}`)}},getAllAssessments:async(t={})=>{try{return await c().assessments.getAll(t)}catch(s){throw new Error(`Failed to get assessments: ${s.message}`)}},getAssessmentDetails:async(t,s)=>{try{return await c().assessments.getDetails(t,s)}catch(r){throw new Error(`Failed to get assessment details: ${r.message}`)}},publishAssessment:async(t,s,r)=>{try{return await c().assessments.publish(t,s,r)}catch(o){throw new Error(`Failed to publish assessment: ${o.message}`)}},assignAssessmentToBatches:async(t,s,r,o)=>{try{return await c().assessments.assignToBatches(t,s,r,o)}catch(v){throw new Error(`Failed to assign assessment to batches: ${v.message}`)}},deleteAssessment:async(t,s,r)=>{try{return await c().assessments.delete(t,s,r)}catch(o){throw new Error(`Failed to delete assessment: ${o.message}`)}},getUserAccessibleBatches:async(t,s,r)=>{try{return await c().assessments.getUserAccessibleBatches(t,s,r)}catch(o){throw new Error(`Failed to get user accessible batches: ${o.message}`)}},getAssessmentsForBatch:async(t,s,r)=>{try{return await c().assessments.getAssessmentsForBatch(t,s,r)}catch(o){throw new Error(`Failed to get assessments for batch: ${o.message}`)}},getBatchStudentsWithProgress:async(t,s,r,o)=>{try{return await c().assessments.getBatchStudentsWithProgress(t,s,r,o)}catch(v){throw new Error(`Failed to get batch students with progress: ${v.message}`)}},getStudentAssessmentHistory:async(t,s,r)=>{try{return await c().assessments.getStudentAssessmentHistory(t,s,r)}catch(o){throw new Error(`Failed to get student assessment history: ${o.message}`)}},canStudentAccessAssessment:async(t,s)=>{try{return await c().assessments.canStudentAccessAssessment(t,s)}catch(r){throw new Error(`Failed to check student assessment access: ${r.message}`)}},generateAssessmentForm:async(t,s,r)=>{try{return await c().assessments.generateAssessmentForm(t,s,r)}catch(o){throw new Error(`Failed to generate assessment form: ${o.message}`)}},uploadStudentResponse:async(t,s,r,o,v)=>{try{return await c().assessments.uploadStudentResponse(t,s,r,o,v)}catch(O){throw new Error(`Failed to upload student response: ${O.message}`)}},getStudentStatistics:async(t,s,r,o)=>{try{return await c().assessments.getStudentStatistics(t,s,r,o)}catch(v){throw new Error(`Failed to get student statistics: ${v.message}`)}},getBatchCompletionStatus:async(t,s,r,o)=>{try{return await c().assessments.getBatchCompletionStatus(t,s,r,o)}catch(v){throw new Error(`Failed to get batch completion status: ${v.message}`)}},processAssessmentForPeerReview:async(t,s,r,o)=>{try{return await c().assessments.processForPeerReview(t,s,r,o)}catch(v){throw new Error(`Failed to process assessment for peer review: ${v.message}`)}},canProcessAssessment:async(t,s,r)=>{try{return await c().assessments.canProcess(t,s,r)}catch(o){throw new Error(`Failed to check if assessment can be processed: ${o.message}`)}},getBatchSubmissionStatus:async(t,s,r)=>{try{return await c().assessments.getBatchSubmissionStatus(t,s,r)}catch(o){throw new Error(`Failed to get batch submission status: ${o.message}`)}},getAssessmentStatistics:async(t,s,r)=>{try{return await c().assessments.getStatistics(t,s,r)}catch(o){throw new Error(`Failed to get assessment statistics: ${o.message}`)}},getUserSettings:async t=>{try{return await c().settings.getUserSettings(t)}catch(s){throw new Error(`Failed to get user settings: ${s.message}`)}},updateUserSetting:async(t,s,r)=>{try{return await c().settings.updateUserSetting(t,s,r)}catch(o){throw new Error(`Failed to update user setting: ${o.message}`)}},getInstitutionDetails:async t=>{try{return await c().institution.getDetails(t)}catch(s){throw new Error(`Failed to get institution details: ${s.message}`)}},updateInstitutionDetails:async(t,s,r,o)=>{try{return await c().institution.updateDetails(t,s,r,o)}catch(v){throw new Error(`Failed to update institution details: ${v.message}`)}},getDashboardStatistics:async(t,s,r)=>{try{return await c().dashboard.getStatistics(t,s,r)}catch(o){throw new Error(`Failed to get dashboard statistics: ${o.message}`)}},submitReview:async t=>{try{return await c().reviews.submit(t)}catch(s){throw new Error(`Failed to submit review: ${s.message}`)}},getReviewStats:async(t={})=>{try{return await c().reviews.getStats(t)}catch(s){throw new Error(`Failed to get review stats: ${s.message}`)}},getSetting:async t=>{try{return await c().settings.get(t)}catch(s){throw new Error(`Failed to get setting: ${s.message}`)}},setSetting:async(t,s)=>{try{return await c().settings.set(t,s)}catch(r){throw new Error(`Failed to set setting: ${r.message}`)}},getInstitution:async()=>{try{return await c().institution.get()}catch(t){throw new Error(`Failed to get institution: ${t.message}`)}},updateInstitution:async(t,s)=>{try{return await c().institution.update(t,s)}catch(r){throw new Error(`Failed to update institution: ${r.message}`)}},uploadInstitutionLogo:async(t,s)=>{try{return await c().institution.uploadLogo(t,s)}catch(r){throw new Error(`Failed to upload logo: ${r.message}`)}},removeInstitutionLogo:async t=>{try{return await c().institution.removeLogo(t)}catch(s){throw new Error(`Failed to remove logo: ${s.message}`)}},getInstitutionLogoPath:async t=>{try{return await c().institution.getLogoPath(t)}catch(s){throw new Error(`Failed to get logo path: ${s.message}`)}},generatePDFReport:async t=>{try{return await c().pdf.generateReport(t)}catch(s){throw new Error(`Failed to generate PDF report: ${s.message}`)}},showSaveDialog:async t=>{try{return await c().file.showSaveDialog(t)}catch(s){throw new Error(`Failed to show save dialog: ${s.message}`)}}};return e.jsxDEV(re.Provider,{value:le,children:i},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/DatabaseContext.jsx",lineNumber:632,columnNumber:5},globalThis)},S={SUCCESS:"success",ERROR:"error",WARNING:"warning",INFO:"info"},oe={[S.SUCCESS]:{icon:me,bgColor:"bg-green-50",borderColor:"border-green-200",textColor:"text-green-800",iconColor:"text-green-400",defaultDuration:5e3},[S.ERROR]:{icon:Y,bgColor:"bg-red-50",borderColor:"border-red-200",textColor:"text-red-800",iconColor:"text-red-400",defaultDuration:8e3},[S.WARNING]:{icon:ue,bgColor:"bg-yellow-50",borderColor:"border-yellow-200",textColor:"text-yellow-800",iconColor:"text-yellow-400",defaultDuration:6e3},[S.INFO]:{icon:Z,bgColor:"bg-blue-50",borderColor:"border-blue-200",textColor:"text-blue-800",iconColor:"text-blue-400",defaultDuration:5e3}},ie=a.createContext(),_e=()=>{const i=a.useContext(ie);if(!i)throw new Error("useMessage must be used within a MessageProvider");return i},Fe=({message:i,onDismiss:u,onAction:n})=>{const l=oe[i.type],p=l.icon,m=a.useRef();a.useEffect(()=>(i.autoDismiss&&i.duration>0&&(m.current=setTimeout(()=>{u(i.id)},i.duration)),()=>{m.current&&clearTimeout(m.current)}),[i.id,i.autoDismiss,i.duration,u]);const h=N=>{N.callback&&N.callback(),N.dismissAfter&&u(i.id),n&&n(i.id,N)};return e.jsxDEV("div",{className:`rounded-md p-4 mb-3 border ${l.bgColor} ${l.borderColor} shadow-sm animate-in slide-in-from-right duration-300`,children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:e.jsxDEV(p,{className:`h-5 w-5 ${l.iconColor}`},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:94,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:93,columnNumber:9},globalThis),e.jsxDEV("div",{className:"ml-3 flex-1",children:e.jsxDEV("div",{className:`text-sm font-medium ${l.textColor}`,children:[i.title&&e.jsxDEV("h4",{className:"font-semibold mb-1",children:i.title},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:99,columnNumber:15},globalThis),e.jsxDEV("p",{children:i.text},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:101,columnNumber:13},globalThis),i.details&&e.jsxDEV("div",{className:"mt-2 text-xs opacity-75",children:Array.isArray(i.details)?e.jsxDEV("ul",{className:"list-disc list-inside space-y-1",children:i.details.map((N,g)=>e.jsxDEV("li",{children:N},g,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:108,columnNumber:23},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:106,columnNumber:19},globalThis):e.jsxDEV("p",{children:i.details},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:112,columnNumber:19},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:104,columnNumber:15},globalThis),i.actions&&i.actions.length>0&&e.jsxDEV("div",{className:"mt-3 flex flex-wrap gap-2",children:i.actions.map((N,g)=>e.jsxDEV("button",{onClick:()=>h(N),className:`inline-flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors ${N.primary?`bg-${i.type==="error"?"red":i.type==="success"?"green":i.type==="warning"?"yellow":"blue"}-600 text-white hover:bg-${i.type==="error"?"red":i.type==="success"?"green":i.type==="warning"?"yellow":"blue"}-700`:"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"}`,disabled:N.loading,children:[N.loading&&e.jsxDEV(pe,{className:"h-3 w-3 mr-1 animate-spin"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:130,columnNumber:40},globalThis),N.icon&&!N.loading&&e.jsxDEV(N.icon,{className:"h-3 w-3 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:131,columnNumber:56},globalThis),N.label,N.external&&e.jsxDEV(de,{className:"h-3 w-3 ml-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:133,columnNumber:41},globalThis)]},g,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:120,columnNumber:19},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:118,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:97,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:96,columnNumber:9},globalThis),i.dismissible!==!1&&e.jsxDEV("div",{className:"ml-4 flex-shrink-0",children:e.jsxDEV("button",{onClick:()=>u(i.id),className:`inline-flex rounded-md p-1.5 ${l.textColor} hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-gray-500`,children:[e.jsxDEV("span",{className:"sr-only",children:"Dismiss"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:147,columnNumber:15},globalThis),e.jsxDEV(_,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:148,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:143,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:142,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:92,columnNumber:7},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:91,columnNumber:5},globalThis)},$e=({messages:i,onDismiss:u,onAction:n,position:l="top-right"})=>{if(i.length===0)return null;const p={"top-right":"fixed top-4 right-4 z-50 max-w-md","top-left":"fixed top-4 left-4 z-50 max-w-md","bottom-right":"fixed bottom-4 right-4 z-50 max-w-md","bottom-left":"fixed bottom-4 left-4 z-50 max-w-md","top-center":"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md","bottom-center":"fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md"};return e.jsxDEV("div",{className:p[l],children:i.map(m=>e.jsxDEV(Fe,{message:m,onDismiss:u,onAction:n},m.id,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:173,columnNumber:9},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:171,columnNumber:5},globalThis)},Re=({children:i,maxMessages:u=5,position:n="top-right"})=>{const[l,p]=a.useState([]),m=a.useRef(0),h=a.useCallback((b,A,y={})=>{const f=++m.current,E=oe[b],D={id:f,type:b,text:A,title:y.title,details:y.details,actions:y.actions,autoDismiss:y.autoDismiss!==!1,duration:y.duration||E.defaultDuration,dismissible:y.dismissible!==!1,persistent:y.persistent||!1,timestamp:new Date().toISOString(),...y};return p(T=>[D,...T].filter((I,M)=>I.persistent||M<u)),f},[u]),N=a.useCallback(b=>{p(A=>A.filter(y=>y.id!==b))},[]),g=a.useCallback(()=>{p([])},[]),x=a.useCallback((b,A)=>{p(y=>y.map(f=>f.id===b?{...f,...A}:f))},[]),w=a.useCallback((b,A)=>{},[]),d={messages:l,showMessage:h,dismissMessage:N,dismissAll:g,updateMessage:x,showSuccess:(b,A)=>h(S.SUCCESS,b,A),showError:(b,A)=>h(S.ERROR,b,A),showWarning:(b,A)=>h(S.WARNING,b,A),showInfo:(b,A)=>h(S.INFO,b,A)};return e.jsxDEV(ie.Provider,{value:d,children:[i,e.jsxDEV($e,{messages:l,onDismiss:N,onAction:w,position:n},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:254,columnNumber:7},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MessageContext.jsx",lineNumber:252,columnNumber:5},globalThis)},We=a.createContext(),Be=({children:i})=>{const u=R(),{user:n}=W(),{showMessage:l}=_e();a.useEffect(()=>{window.electronAPI&&n&&window.electronAPI.invoke("menu:setUserContext",n)},[n]);const p=a.useCallback(f=>{u(f)},[u]),m=a.useCallback(f=>{switch(f){case"save-assessment":window.dispatchEvent(new CustomEvent("menu:save-assessment"));break;case"show-search":window.dispatchEvent(new CustomEvent("menu:show-search"));break}},[]),h=a.useCallback(async({type:f,filePath:E})=>{try{switch(l("Exporting data...","info"),f){case"assessment-results":await Ue(E);break;case"student-data":await Oe(E);break;case"reports":await Ke(E);break;default:throw new Error(`Unknown export type: ${f}`)}l("Data exported successfully!","success")}catch(D){l(`Export failed: ${D.message}`,"error")}},[l]),N=a.useCallback(async({type:f,filePath:E})=>{try{switch(l("Importing data...","info"),f){case"student-data":await ze(E);break;case"assessment-data":await qe(E);break;default:throw new Error(`Unknown import type: ${f}`)}l("Data imported successfully!","success")}catch(D){l(`Import failed: ${D.message}`,"error")}},[l]),g=a.useCallback(async({filePath:f})=>{try{l("Creating backup...","info");const E=await window.electronAPI.invoke("backup:create",{filePath:f});if(E.success)l("Backup created successfully!","success");else throw new Error(E.error||"Backup creation failed")}catch(E){l(`Backup failed: ${E.message}`,"error")}},[l]),x=a.useCallback(async({filePath:f})=>{try{l("Restoring backup...","info");const E=await window.electronAPI.invoke("backup:restore",{filePath:f});if(E.success)l("Backup restored successfully! Please restart the application.","success");else throw new Error(E.error||"Backup restoration failed")}catch(E){l(`Restore failed: ${E.message}`,"error")}},[l]),w=a.useCallback(()=>{u("/tutorial")},[u]),d=a.useCallback(f=>{window.dispatchEvent(new CustomEvent("menu:show-shortcuts",{detail:f}))},[]),b=a.useCallback(async()=>{try{const f=await window.electronAPI.updater.getVersion();l(`Current version: ${f}. This is an offline application with no automatic updates.`,"info")}catch(f){l(`Version check failed: ${f.message}`,"error")}},[l]),A=a.useCallback(f=>{window.dispatchEvent(new CustomEvent("menu:show-about",{detail:f}))},[]);a.useEffect(()=>{if(!window.electronAPI)return;const f=(V,j)=>p(j),E=(V,j)=>m(j),D=(V,j)=>h(j),T=(V,j)=>N(j),F=(V,j)=>g(j),$=(V,j)=>x(j),I=()=>w(),M=(V,j)=>d(j),B=()=>b(),U=(V,j)=>A(j);return window.electronAPI.on("menu:navigate",f),window.electronAPI.on("menu:action",E),window.electronAPI.on("menu:export-data",D),window.electronAPI.on("menu:import-data",T),window.electronAPI.on("menu:create-backup",F),window.electronAPI.on("menu:restore-backup",$),window.electronAPI.on("menu:show-tutorial",I),window.electronAPI.on("menu:show-shortcuts",M),window.electronAPI.on("menu:check-updates",B),window.electronAPI.on("menu:show-about",U),()=>{window.electronAPI.removeAllListeners("menu:navigate"),window.electronAPI.removeAllListeners("menu:action"),window.electronAPI.removeAllListeners("menu:export-data"),window.electronAPI.removeAllListeners("menu:import-data"),window.electronAPI.removeAllListeners("menu:create-backup"),window.electronAPI.removeAllListeners("menu:restore-backup"),window.electronAPI.removeAllListeners("menu:show-tutorial"),window.electronAPI.removeAllListeners("menu:show-shortcuts"),window.electronAPI.removeAllListeners("menu:check-updates"),window.electronAPI.removeAllListeners("menu:show-about")}},[p,m,h,N,g,x,w,d,b,A]);const y={handleMenuNavigation:p,handleMenuAction:m,handleExportData:h,handleImportData:N,handleCreateBackup:g,handleRestoreBackup:x,handleShowTutorial:w,handleShowShortcuts:d,handleCheckUpdates:b,handleShowAbout:A};return e.jsxDEV(We.Provider,{value:y,children:i},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/contexts/MenuContext.jsx",lineNumber:243,columnNumber:5},globalThis)};async function Ue(i){}async function Oe(i){}async function Ke(i){}async function ze(i){}async function qe(i){}const Ge=()=>{const i=R(),u=[{id:"super_admin",title:"Administrator",description:"Full system access with assessment creation, user management, and system administration",icon:Ne,color:"purple",features:["Create Assessments","Manage Users","System Settings","Institution Management","Full Access"],path:"/dashboard?role=super_admin"},{id:"teacher",title:"Teacher",description:"Manage batches, conduct assessments, and view student progress",icon:Ae,color:"green",features:["Batch Management","Take Assessments","Student Progress","Reports"],path:"/dashboard?role=teacher"},{id:"student",title:"Student",description:"Participate in peer review assessments and view results",icon:xe,color:"blue",features:["Take Assessments","View Results","Peer Reviews","Progress Tracking"],path:"/dashboard?role=student"}],n=l=>{localStorage.setItem("selectedRole",l.id),i(l.path)};return e.jsxDEV("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsxDEV("div",{className:"max-w-6xl w-full",children:[e.jsxDEV("div",{className:"text-center mb-12",children:[e.jsxDEV("div",{className:"flex justify-center mb-6",children:e.jsxDEV("div",{className:"flex items-center space-x-3",children:[e.jsxDEV(ee,{className:"h-16 w-16 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:61,columnNumber:15},globalThis),e.jsxDEV("span",{className:"text-4xl font-bold text-gray-900",children:"Peer Review System"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:62,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:60,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:59,columnNumber:11},globalThis),e.jsxDEV("div",{className:"flex justify-center mb-6",children:e.jsxDEV("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center",children:e.jsxDEV(be,{className:"h-10 w-10 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:67,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:66,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:65,columnNumber:11},globalThis),e.jsxDEV("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Welcome to Peer Review System"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:70,columnNumber:11},globalThis),e.jsxDEV("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"A comprehensive offline desktop application for peer review assessments in educational institutions. Choose your role to get started."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:73,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:58,columnNumber:9},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:u.map(l=>{const p=l.icon,m={purple:"from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700",blue:"from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700",green:"from-green-500 to-green-600 hover:from-green-600 hover:to-green-700",orange:"from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700"};return e.jsxDEV("button",{onClick:()=>n(l),className:`
                  relative p-8 rounded-2xl shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl
                  bg-gradient-to-br ${m[l.color]} text-white text-left group
                `,children:e.jsxDEV("div",{className:"flex items-start space-x-4",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:e.jsxDEV("div",{className:"w-16 h-16 bg-white bg-opacity-20 rounded-xl flex items-center justify-center",children:e.jsxDEV(p,{className:"h-8 w-8 text-white"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:102,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:101,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:100,columnNumber:19},globalThis),e.jsxDEV("div",{className:"flex-1 min-w-0",children:[e.jsxDEV("h3",{className:"text-2xl font-bold text-white mb-2",children:l.title},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:106,columnNumber:21},globalThis),e.jsxDEV("p",{className:"text-white text-opacity-90 mb-4 leading-relaxed",children:l.description},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:109,columnNumber:21},globalThis),e.jsxDEV("div",{className:"space-y-2",children:l.features.map((h,N)=>e.jsxDEV("div",{className:"flex items-center space-x-2",children:[e.jsxDEV("div",{className:"w-1.5 h-1.5 bg-white rounded-full"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:115,columnNumber:27},globalThis),e.jsxDEV("span",{className:"text-sm text-white text-opacity-80",children:h},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:116,columnNumber:27},globalThis)]},N,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:114,columnNumber:25},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:112,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:105,columnNumber:19},globalThis),e.jsxDEV("div",{className:"flex-shrink-0",children:e.jsxDEV(he,{className:"h-6 w-6 text-white text-opacity-60 group-hover:text-opacity-100 transition-opacity"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:122,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:121,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:99,columnNumber:17},globalThis)},l.id,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:91,columnNumber:15},globalThis)})},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:80,columnNumber:9},globalThis),e.jsxDEV("div",{className:"text-center",children:e.jsxDEV("p",{className:"text-gray-500 text-sm",children:"Select your role to access the appropriate dashboard and features"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:132,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:131,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:56,columnNumber:7},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/WelcomeScreen.jsx",lineNumber:55,columnNumber:5},globalThis)},He=()=>{const[i,u]=a.useState(!1),{user:n,logout:l,isAdmin:p,isSuperAdmin:m,isTeacher:h}=W(),N=R(),g=ae(),x=()=>{l(),N("/")},d=[{name:"Dashboard",href:"/dashboard",icon:fe,show:!0},{name:"Admin Panel",href:"/admin",icon:ve,show:p},{name:"Batch Management",href:"/batches",icon:ge,show:h||p},{name:"Create Assessment",href:"/create-assessment",icon:we,show:m},{name:"Assessment Management",href:"/assessments",icon:G,show:m},{name:"Take Assessment",href:"/take-assessment",icon:G,show:n?.role==="teacher"||n?.role==="student"},{name:"Settings",href:"/settings",icon:se,show:!0}].filter(b=>b.show);return e.jsxDEV("div",{className:"flex h-screen bg-gray-100",children:[e.jsxDEV("div",{className:`${i?"translate-x-0":"-translate-x-full"} fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`,children:[e.jsxDEV("div",{className:"flex items-center justify-between h-16 px-4 border-b",children:[e.jsxDEV("div",{className:"flex items-center space-x-2",children:[e.jsxDEV(ee,{className:"h-8 w-8 text-primary-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:81,columnNumber:13},globalThis),e.jsxDEV("span",{className:"text-xl font-bold text-gray-900",children:"Peer Review"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:82,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:80,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:()=>u(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",children:e.jsxDEV(_,{className:"h-6 w-6"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:88,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:84,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:79,columnNumber:9},globalThis),e.jsxDEV("nav",{className:"mt-5 px-2",children:e.jsxDEV("div",{className:"space-y-1",children:d.map(b=>{const A=b.icon,y=g.pathname===b.href;return e.jsxDEV("button",{onClick:()=>{N(b.href),u(!1)},className:`${y?"bg-primary-100 border-primary-500 text-primary-700":"border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900"} group flex items-center px-2 py-2 text-sm font-medium rounded-md border-l-4 w-full text-left transition-colors duration-150`,children:[e.jsxDEV(A,{className:`${y?"text-primary-500":"text-gray-400 group-hover:text-gray-500"} mr-3 h-6 w-6`},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:111,columnNumber:19},globalThis),b.name]},b.name,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:99,columnNumber:17},globalThis)})},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:93,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:92,columnNumber:9},globalThis),e.jsxDEV("div",{className:"absolute bottom-0 left-0 right-0 p-4 border-t",children:[e.jsxDEV("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsxDEV("div",{className:"flex-shrink-0",children:e.jsxDEV("div",{className:"h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center",children:e.jsxDEV("span",{className:"text-white font-medium",children:[n?.first_name?.[0],n?.last_name?.[0]]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:128,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:127,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:126,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex-1 min-w-0",children:[e.jsxDEV("p",{className:"text-sm font-medium text-gray-900 truncate",children:[n?.first_name," ",n?.last_name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:134,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-xs text-gray-500 truncate capitalize",children:n?.role?.replace("_"," ")},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:137,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:133,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:125,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:x,className:"flex items-center w-full px-2 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150",children:[e.jsxDEV(ye,{className:"mr-3 h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:146,columnNumber:13},globalThis),"Sign out"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:142,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:124,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:78,columnNumber:7},globalThis),e.jsxDEV("div",{className:"flex-1 flex flex-col overflow-hidden",children:[e.jsxDEV("header",{className:"bg-white shadow-sm border-b border-gray-200",children:e.jsxDEV("div",{className:"flex items-center justify-between h-16 px-4",children:[e.jsxDEV("button",{onClick:()=>u(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",children:e.jsxDEV(Ee,{className:"h-6 w-6"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:161,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:157,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex-1 lg:ml-0",children:e.jsxDEV("h1",{className:"text-2xl font-semibold text-gray-900",children:d.find(b=>b.href===g.pathname)?.name||"Dashboard"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:165,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:164,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:156,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:155,columnNumber:9},globalThis),e.jsxDEV("main",{className:"flex-1 overflow-y-auto bg-gray-50",children:e.jsxDEV("div",{className:"py-6",children:e.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxDEV(ne,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:176,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:175,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:174,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:173,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:153,columnNumber:7},globalThis),i&&e.jsxDEV("div",{className:"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden",onClick:()=>u(!1)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:184,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/Layout.jsx",lineNumber:76,columnNumber:5},globalThis)},C=({size:i="medium",text:u="Loading..."})=>{const n={small:"h-4 w-4",medium:"h-8 w-8",large:"h-12 w-12"};return e.jsxDEV("div",{className:"flex flex-col items-center justify-center min-h-screen",children:[e.jsxDEV("div",{className:`animate-spin rounded-full border-b-2 border-primary-600 ${n[i]}`},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/LoadingSpinner.jsx",lineNumber:12,columnNumber:7},globalThis),u&&e.jsxDEV("p",{className:"mt-4 text-sm text-gray-600",children:u},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/LoadingSpinner.jsx",lineNumber:14,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/LoadingSpinner.jsx",lineNumber:11,columnNumber:5},globalThis)},Je=()=>{const[i,u]=a.useState(!1),[n,l]=a.useState(null),[p,m]=a.useState(null);return a.useEffect(()=>{const h=()=>{typeof window<"u"&&window.electronAPI?(l(window.electronAPI),u(!0),m(null)):m("Electron API not available. Please ensure you are running the desktop application.")};h();const N=setTimeout(h,1e3);return()=>clearTimeout(N)},[]),{isElectronReady:i,electronAPI:n,error:p,isElectron:!!n}},Os=()=>typeof window<"u"&&window.electronAPI?window.electronAPI:null,Qe=({children:i})=>{const{isElectronReady:u,error:n,isElectron:l}=Je();return!u&&!n?e.jsxDEV(C,{text:"Initializing application...",size:"large"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:12,columnNumber:7},globalThis):n||!l?e.jsxDEV("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:e.jsxDEV("div",{className:"max-w-md w-full bg-white shadow-lg rounded-lg p-6",children:[e.jsxDEV("div",{className:"flex items-center mb-4",children:[e.jsxDEV(Y,{className:"h-8 w-8 text-red-500 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:25,columnNumber:13},globalThis),e.jsxDEV("h1",{className:"text-xl font-bold text-gray-900",children:"Application Error"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:26,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:24,columnNumber:11},globalThis),e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("p",{className:"text-gray-600",children:n||"The Peer Review System requires the desktop application to function properly."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:30,columnNumber:13},globalThis),e.jsxDEV("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4",children:[e.jsxDEV("h3",{className:"text-sm font-medium text-yellow-800 mb-2",children:"Troubleshooting Steps:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:35,columnNumber:15},globalThis),e.jsxDEV("ul",{className:"text-sm text-yellow-700 space-y-1",children:[e.jsxDEV("li",{children:"• Ensure you're running the Electron desktop application"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:39,columnNumber:17},globalThis),e.jsxDEV("li",{children:"• Try restarting the application"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:40,columnNumber:17},globalThis),e.jsxDEV("li",{children:"• Check that all dependencies are properly installed"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:41,columnNumber:17},globalThis),e.jsxDEV("li",{children:"• Restart your computer if the issue persists"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:42,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:38,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:34,columnNumber:13},globalThis),e.jsxDEV("button",{onClick:()=>window.location.reload(),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Retry"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:46,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:29,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:23,columnNumber:9},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/ElectronWrapper.jsx",lineNumber:22,columnNumber:7},globalThis):i},Xe=()=>{const[i,u]=a.useState(!1),[n,l]=a.useState(null);a.useEffect(()=>{const m=h=>{l(h.detail),u(!0)};return window.addEventListener("menu:show-about",m),()=>{window.removeEventListener("menu:show-about",m)}},[]);const p=()=>{u(!1),l(null)};return!i||!n?null:e.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxDEV("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[e.jsxDEV("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[e.jsxDEV("div",{className:"flex items-center space-x-3",children:[e.jsxDEV("div",{className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center",children:e.jsxDEV(Z,{className:"w-6 h-6 text-white"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:53,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:52,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h2",{className:"text-xl font-semibold text-gray-900",children:"About"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:56,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600",children:n.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:57,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:55,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:51,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:p,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:e.jsxDEV(_,{className:"w-5 h-5 text-gray-500"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:64,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:60,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:50,columnNumber:9},globalThis),e.jsxDEV("div",{className:"p-6 space-y-6",children:[e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:n.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:72,columnNumber:13},globalThis),e.jsxDEV("p",{className:"text-lg text-gray-600 mb-4",children:["Version ",n.version]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:75,columnNumber:13},globalThis),e.jsxDEV("p",{className:"text-gray-700 max-w-lg mx-auto",children:n.description},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:76,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:71,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsxDEV("h4",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[e.jsxDEV(je,{className:"w-5 h-5 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:84,columnNumber:15},globalThis),"Developer"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:83,columnNumber:13},globalThis),e.jsxDEV("div",{className:"space-y-2",children:[e.jsxDEV("p",{className:"text-gray-700",children:e.jsxDEV("strong",{children:n.developer.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:89,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:88,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Offline Desktop Application Developer"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:91,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:87,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:82,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsxDEV("h4",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[e.jsxDEV(Pe,{className:"w-5 h-5 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:100,columnNumber:15},globalThis),"Company"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:99,columnNumber:13},globalThis),e.jsxDEV("div",{className:"space-y-2",children:[e.jsxDEV("p",{className:"text-gray-700",children:e.jsxDEV("strong",{children:n.company.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:105,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:104,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Offline Desktop Application Solutions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:107,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:103,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:98,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsxDEV("h4",{className:"text-lg font-semibold text-gray-900 mb-3",children:"System Information"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:115,columnNumber:13},globalThis),e.jsxDEV("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-gray-600",children:"Electron Version"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:120,columnNumber:17},globalThis),e.jsxDEV("p",{className:"font-mono text-gray-900",children:n.electron},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:121,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:119,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-gray-600",children:"Node.js Version"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:124,columnNumber:17},globalThis),e.jsxDEV("p",{className:"font-mono text-gray-900",children:n.node},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:125,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:123,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-gray-600",children:"Chrome Version"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:128,columnNumber:17},globalThis),e.jsxDEV("p",{className:"font-mono text-gray-900",children:n.chrome},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:129,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:127,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("p",{className:"text-gray-600",children:"License"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:132,columnNumber:17},globalThis),e.jsxDEV("p",{className:"font-mono text-gray-900",children:n.license},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:133,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:131,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:118,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:114,columnNumber:11},globalThis),e.jsxDEV("div",{className:"text-center text-sm text-gray-600 border-t border-gray-200 pt-4",children:[e.jsxDEV("p",{children:n.copyright},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:140,columnNumber:13},globalThis),e.jsxDEV("p",{className:"mt-1",children:"All rights reserved."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:141,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:139,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:69,columnNumber:9},globalThis),e.jsxDEV("div",{className:"flex justify-end p-6 border-t border-gray-200",children:e.jsxDEV("button",{onClick:p,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Close"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:147,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:146,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:48,columnNumber:7},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/AboutModal.jsx",lineNumber:47,columnNumber:5},globalThis)},Ye=()=>{const[i,u]=a.useState(!1),[n,l]=a.useState([]);a.useEffect(()=>{const g=x=>{l(x.detail||[]),u(!0)};return window.addEventListener("menu:show-shortcuts",g),()=>{window.removeEventListener("menu:show-shortcuts",g)}},[]);const p=()=>{u(!1),l([])},m=g=>{const x={file:{name:"File Operations",icon:Ce,shortcuts:[]},edit:{name:"Edit Operations",icon:se,shortcuts:[]},view:{name:"View & Navigation",icon:Le,shortcuts:[]},search:{name:"Search & Find",icon:De,shortcuts:[]},help:{name:"Help & Support",icon:Ve,shortcuts:[]}};return g.forEach(w=>{const d=w.action.toLowerCase();d.includes("new")||d.includes("open")||d.includes("save")||d.includes("print")?x.file.shortcuts.push(w):d.includes("undo")||d.includes("redo")||d.includes("cut")||d.includes("copy")||d.includes("paste")||d.includes("preferences")?x.edit.shortcuts.push(w):d.includes("dashboard")||d.includes("assessment")||d.includes("student")||d.includes("report")||d.includes("zoom")||d.includes("full screen")||d.includes("refresh")?x.view.shortcuts.push(w):d.includes("find")||d.includes("search")?x.search.shortcuts.push(w):x.help.shortcuts.push(w)}),x},h=g=>g.replace("CmdOrCtrl","Ctrl").replace("Plus","+");if(!i)return null;const N=m(n);return e.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxDEV("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[e.jsxDEV("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[e.jsxDEV("div",{className:"flex items-center space-x-3",children:[e.jsxDEV("div",{className:"w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center",children:e.jsxDEV(H,{className:"w-6 h-6 text-white"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:80,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:79,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h2",{className:"text-xl font-semibold text-gray-900",children:"Keyboard Shortcuts"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:83,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600",children:"Quick access to all features"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:84,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:82,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:78,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:p,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:e.jsxDEV(_,{className:"w-5 h-5 text-gray-500"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:91,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:87,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:77,columnNumber:9},globalThis),e.jsxDEV("div",{className:"p-6",children:[e.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:Object.entries(N).map(([g,x])=>{if(x.shortcuts.length===0)return null;const w=x.icon;return e.jsxDEV("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsxDEV(w,{className:"w-5 h-5 mr-2 text-gray-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:106,columnNumber:21},globalThis),x.name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:105,columnNumber:19},globalThis),e.jsxDEV("div",{className:"space-y-3",children:x.shortcuts.map((d,b)=>e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("span",{className:"text-gray-700 text-sm",children:d.action},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:112,columnNumber:25},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-1",children:h(d.shortcut).split("+").map((A,y)=>e.jsxDEV(k.Fragment,{children:[y>0&&e.jsxDEV("span",{className:"text-gray-400 text-xs",children:"+"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:119,columnNumber:33},globalThis),e.jsxDEV("kbd",{className:"px-2 py-1 bg-white border border-gray-300 rounded text-xs font-mono text-gray-800 shadow-sm",children:A.trim()},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:121,columnNumber:31},globalThis)]},y,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:117,columnNumber:29},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:115,columnNumber:25},globalThis)]},b,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:111,columnNumber:23},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:109,columnNumber:19},globalThis)]},g,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:104,columnNumber:17},globalThis)})},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:97,columnNumber:11},globalThis),e.jsxDEV("div",{className:"mt-6 bg-blue-50 rounded-lg p-4",children:[e.jsxDEV("h4",{className:"text-md font-semibold text-blue-900 mb-2",children:"Tips"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:137,columnNumber:13},globalThis),e.jsxDEV("ul",{className:"text-sm text-blue-800 space-y-1",children:[e.jsxDEV("li",{children:["• Use ",e.jsxDEV("kbd",{className:"px-1 py-0.5 bg-blue-100 rounded text-xs",children:"Ctrl"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:139,columnNumber:25},globalThis)," key combinations for quick access to common functions"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:139,columnNumber:15},globalThis),e.jsxDEV("li",{children:["• Function keys like ",e.jsxDEV("kbd",{className:"px-1 py-0.5 bg-blue-100 rounded text-xs",children:"F11"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:140,columnNumber:40},globalThis)," provide instant access to view options"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:140,columnNumber:15},globalThis),e.jsxDEV("li",{children:["• Number keys ",e.jsxDEV("kbd",{className:"px-1 py-0.5 bg-blue-100 rounded text-xs",children:"Ctrl+1-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:141,columnNumber:33},globalThis)," quickly navigate between main sections"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:141,columnNumber:15},globalThis),e.jsxDEV("li",{children:"• Most shortcuts work globally throughout the application"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:142,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:138,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:136,columnNumber:11},globalThis),n.length===0&&e.jsxDEV("div",{className:"text-center py-8",children:[e.jsxDEV(H,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:149,columnNumber:15},globalThis),e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Shortcuts Available"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:150,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600",children:"Keyboard shortcuts will be displayed here when available."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:151,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:148,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:96,columnNumber:9},globalThis),e.jsxDEV("div",{className:"flex justify-between items-center p-6 border-t border-gray-200",children:[e.jsxDEV("p",{className:"text-sm text-gray-600",children:["Press ",e.jsxDEV("kbd",{className:"px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs font-mono",children:"Ctrl+?"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:159,columnNumber:19},globalThis)," to open this dialog anytime"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:158,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:p,className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:"Close"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:161,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:157,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:75,columnNumber:7},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/modals/KeyboardShortcutsModal.jsx",lineNumber:74,columnNumber:5},globalThis)},Ze=a.lazy(()=>L(()=>import("./Dashboardx-1fc741b2.js"),["./Dashboardx-1fc741b2.js","./chunk-81a058b1.js","./chunk-81a949b4.js","./chunk-ca279186.js","./chunk-0b87a8e8.js"],import.meta.url)),es=a.lazy(()=>L(()=>import("./AdminPanelx-3c0da2c3.js"),["./AdminPanelx-3c0da2c3.js","./chunk-81a058b1.js","./chunk-81a949b4.js","./chunk-c39a4323.js","./chunk-0b87a8e8.js"],import.meta.url)),Q=a.lazy(()=>L(()=>import("./BatchManagementx-db44a41a.js"),["./BatchManagementx-db44a41a.js","./chunk-81a058b1.js","./chunk-81a949b4.js","./chunk-e7a6d730.js","./chunk-c39a4323.js","./chunk-0b87a8e8.js"],import.meta.url)),ss=a.lazy(()=>L(()=>import("./CreateBatchx-d12177d3.js"),["./CreateBatchx-d12177d3.js","./chunk-81a058b1.js","./chunk-81a949b4.js","./chunk-c39a4323.js","./chunk-0b87a8e8.js","./chunk-e7a6d730.js"],import.meta.url)),ts=a.lazy(()=>L(()=>import("./CreateAssessmentx-fb64f20e.js"),["./CreateAssessmentx-fb64f20e.js","./chunk-81a058b1.js","./chunk-81a949b4.js","./chunk-0b87a8e8.js"],import.meta.url)),rs=a.lazy(()=>L(()=>import("./AssessmentManagementx-beb77c04.js"),["./AssessmentManagementx-beb77c04.js","./chunk-81a058b1.js","./chunk-81a949b4.js","./chunk-0b87a8e8.js"],import.meta.url)),X=a.lazy(()=>L(()=>import("./TakeAssessmentx-ac7a4aa0.js"),["./TakeAssessmentx-ac7a4aa0.js","./chunk-81a058b1.js","./chunk-81a949b4.js","./chunk-0b87a8e8.js","./chunk-ca279186.js"],import.meta.url)),os=a.lazy(()=>L(()=>import("./Settingsx-e2be7243.js"),["./Settingsx-e2be7243.js","./chunk-81a058b1.js","./chunk-81a949b4.js","./chunk-112aed37.js","./chunk-0b87a8e8.js"],import.meta.url)),is=a.lazy(()=>L(()=>import("./Institutionx-f3bf6612.js"),["./Institutionx-f3bf6612.js","./chunk-81a058b1.js","./chunk-81a949b4.js","./chunk-0b87a8e8.js"],import.meta.url)),ls=a.lazy(()=>L(()=>import("./Tutorialx-032930ae.js"),["./Tutorialx-032930ae.js","./chunk-81a058b1.js","./chunk-81a949b4.js","./chunk-0b87a8e8.js"],import.meta.url));function as(){const{user:i,loading:u}=W();return u?e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:35,columnNumber:12},this):i?e.jsxDEV(K,{children:e.jsxDEV(Be,{children:[e.jsxDEV(z,{children:[e.jsxDEV(P,{path:"/",element:e.jsxDEV(He,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:53,columnNumber:36},this),children:[e.jsxDEV(P,{index:!0,element:e.jsxDEV(q,{to:"/dashboard",replace:!0},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:54,columnNumber:35},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:54,columnNumber:13},this),e.jsxDEV(P,{path:"dashboard",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:58,columnNumber:37},this),children:e.jsxDEV(Ze,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:59,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:58,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:55,columnNumber:13},this),e.jsxDEV(P,{path:"admin",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:67,columnNumber:37},this),children:e.jsxDEV(es,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:68,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:67,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:64,columnNumber:13},this),e.jsxDEV(P,{path:"batches",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:76,columnNumber:37},this),children:e.jsxDEV(Q,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:77,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:76,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:73,columnNumber:13},this),e.jsxDEV(P,{path:"create-batch",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:85,columnNumber:37},this),children:e.jsxDEV(ss,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:86,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:85,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:82,columnNumber:13},this),e.jsxDEV(P,{path:"batch-management",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:94,columnNumber:37},this),children:e.jsxDEV(Q,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:95,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:94,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:91,columnNumber:13},this),e.jsxDEV(P,{path:"create-assessment",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:103,columnNumber:37},this),children:e.jsxDEV(ts,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:104,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:103,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:100,columnNumber:13},this),e.jsxDEV(P,{path:"assessments",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:112,columnNumber:37},this),children:e.jsxDEV(rs,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:113,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:112,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:109,columnNumber:13},this),e.jsxDEV(P,{path:"take-assessment",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:121,columnNumber:37},this),children:e.jsxDEV(X,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:122,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:121,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:118,columnNumber:13},this),e.jsxDEV(P,{path:"test",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:130,columnNumber:37},this),children:e.jsxDEV(X,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:131,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:130,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:127,columnNumber:13},this),e.jsxDEV(P,{path:"settings",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:139,columnNumber:37},this),children:e.jsxDEV(os,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:140,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:139,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:136,columnNumber:13},this),e.jsxDEV(P,{path:"institution",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:148,columnNumber:37},this),children:e.jsxDEV(is,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:149,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:148,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:145,columnNumber:13},this),e.jsxDEV(P,{path:"tutorial",element:e.jsxDEV(a.Suspense,{fallback:e.jsxDEV(C,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:157,columnNumber:37},this),children:e.jsxDEV(ls,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:158,columnNumber:19},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:157,columnNumber:17},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:154,columnNumber:13},this)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:53,columnNumber:11},this),e.jsxDEV(P,{path:"*",element:e.jsxDEV(q,{to:"/dashboard",replace:!0},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:164,columnNumber:36},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:164,columnNumber:11},this)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:52,columnNumber:9},this),e.jsxDEV(Xe,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:168,columnNumber:9},this),e.jsxDEV(Ye,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:169,columnNumber:9},this)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:51,columnNumber:7},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:50,columnNumber:5},this):e.jsxDEV(K,{children:e.jsxDEV(z,{children:e.jsxDEV(P,{path:"*",element:e.jsxDEV(Ge,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:43,columnNumber:36},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:43,columnNumber:11},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:42,columnNumber:9},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:41,columnNumber:7},this)}function ns(){return e.jsxDEV(Qe,{children:e.jsxDEV(ke,{children:e.jsxDEV(Ie,{children:e.jsxDEV(Re,{children:e.jsxDEV("div",{className:"min-h-screen bg-gray-50",children:e.jsxDEV(as,{},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:182,columnNumber:15},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:181,columnNumber:13},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:180,columnNumber:11},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:179,columnNumber:9},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:178,columnNumber:7},this)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/App.jsx",lineNumber:177,columnNumber:5},this)}ce.createRoot(document.getElementById("root")).render(k.createElement(k.StrictMode,null,k.createElement(ns,null)));export{_e as a,Us as b,Os as g,W as u};
