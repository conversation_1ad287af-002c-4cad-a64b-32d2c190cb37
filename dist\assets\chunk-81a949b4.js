var Pe={exports:{}},Se={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(f,u){var h=f.length;f.push(u);e:for(;0<h;){var b=h-1>>>1,y=f[b];if(0<r(y,u))f[b]=u,f[h]=y,h=b;else break e}}function n(f){return f.length===0?null:f[0]}function a(f){if(f.length===0)return null;var u=f[0],h=f.pop();if(h!==u){f[0]=h;e:for(var b=0,y=f.length,V=y>>>1;b<V;){var Y=2*(b+1)-1,G=f[Y],O=Y+1,Z=f[O];if(0>r(G,h))O<y&&0>r(Z,G)?(f[b]=Z,f[O]=h,b=O):(f[b]=G,f[Y]=h,b=Y);else if(O<y&&0>r(Z,h))f[b]=Z,f[O]=h,b=O;else break e}}return u}function r(f,u){var h=f.sortIndex-u.sortIndex;return h!==0?h:f.id-u.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,o=s.now();e.unstable_now=function(){return s.now()-o}}var l=[],c=[],m=1,d=null,g=3,k=!1,P=!1,v=!1,p=typeof setTimeout=="function"?setTimeout:null,w=typeof clearTimeout=="function"?clearTimeout:null,x=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function M(f){for(var u=n(c);u!==null;){if(u.callback===null)a(c);else if(u.startTime<=f)a(c),u.sortIndex=u.expirationTime,t(l,u);else break;u=n(c)}}function F(f){if(v=!1,M(f),!P)if(n(l)!==null)P=!0,z($);else{var u=n(c);u!==null&&J(F,u.startTime-f)}}function $(f,u){P=!1,v&&(v=!1,w(L),L=-1),k=!0;var h=g;try{for(M(u),d=n(l);d!==null&&(!(d.expirationTime>u)||f&&!ue());){var b=d.callback;if(typeof b=="function"){d.callback=null,g=d.priorityLevel;var y=b(d.expirationTime<=u);u=e.unstable_now(),typeof y=="function"?d.callback=y:d===n(l)&&a(l),M(u)}else a(l);d=n(l)}if(d!==null)var V=!0;else{var Y=n(c);Y!==null&&J(F,Y.startTime-u),V=!1}return V}finally{d=null,g=h,k=!1}}var C=!1,B=null,L=-1,oe=5,ce=-1;function ue(){return!(e.unstable_now()-ce<oe)}function Q(){if(B!==null){var f=e.unstable_now();ce=f;var u=!0;try{u=B(!0,f)}finally{u?H():(C=!1,B=null)}}else C=!1}var H;if(typeof x=="function")H=function(){x(Q)};else if(typeof MessageChannel<"u"){var he=new MessageChannel,Ye=he.port2;he.port1.onmessage=Q,H=function(){Ye.postMessage(null)}}else H=function(){p(Q,0)};function z(f){B=f,C||(C=!0,H())}function J(f,u){L=p(function(){f(e.unstable_now())},u)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(f){f.callback=null},e.unstable_continueExecution=function(){P||k||(P=!0,z($))},e.unstable_forceFrameRate=function(f){0>f||125<f||(oe=0<f?Math.floor(1e3/f):5)},e.unstable_getCurrentPriorityLevel=function(){return g},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(f){switch(g){case 1:case 2:case 3:var u=3;break;default:u=g}var h=g;g=u;try{return f()}finally{g=h}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(f,u){switch(f){case 1:case 2:case 3:case 4:case 5:break;default:f=3}var h=g;g=f;try{return u()}finally{g=h}},e.unstable_scheduleCallback=function(f,u,h){var b=e.unstable_now();switch(typeof h=="object"&&h!==null?(h=h.delay,h=typeof h=="number"&&0<h?b+h:b):h=b,f){case 1:var y=-1;break;case 2:y=250;break;case 5:y=1073741823;break;case 4:y=1e4;break;default:y=5e3}return y=h+y,f={id:m++,callback:u,priorityLevel:f,startTime:h,expirationTime:y,sortIndex:-1},h>b?(f.sortIndex=h,t(c,f),n(l)===null&&f===n(c)&&(v?(w(L),L=-1):v=!0,J(F,h-b))):(f.sortIndex=y,t(l,f),P||k||(P=!0,z($))),f},e.unstable_shouldYield=ue,e.unstable_wrapCallback=function(f){var u=g;return function(){var h=g;g=u;try{return f.apply(this,arguments)}finally{g=h}}}})(Se);Pe.exports=Se;var St=Pe.exports;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function j(){return j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},j.apply(this,arguments)}var I;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(I||(I={}));const de="popstate";function _t(e){e===void 0&&(e={});function t(r,i){let{pathname:s="/",search:o="",hash:l=""}=A(r.location.hash.substr(1));return!s.startsWith("/")&&!s.startsWith(".")&&(s="/"+s),ne("",{pathname:s,search:o,hash:l},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){let s=r.document.querySelector("base"),o="";if(s&&s.getAttribute("href")){let l=r.location.href,c=l.indexOf("#");o=c===-1?l:l.slice(0,c)}return o+"#"+(typeof i=="string"?i:_e(i))}function a(r,i){ie(r.pathname.charAt(0)==="/","relative pathnames are not supported in hash history.push("+JSON.stringify(i)+")")}return Te(t,n,a,e)}function W(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function ie(e,t){if(!e)try{throw new Error(t)}catch{}}function Oe(){return Math.random().toString(36).substr(2,8)}function ge(e,t){return{usr:e.state,key:e.key,idx:t}}function ne(e,t,n,a){return n===void 0&&(n=null),j({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?A(t):t,{state:n,key:t&&t.key||a||Oe()})}function _e(e){let{pathname:t="/",search:n="",hash:a=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),a&&a!=="#"&&(t+=a.charAt(0)==="#"?a:"#"+a),t}function A(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}function Te(e,t,n,a){a===void 0&&(a={});let{window:r=document.defaultView,v5Compat:i=!1}=a,s=r.history,o=I.Pop,l=null,c=m();c==null&&(c=0,s.replaceState(j({},s.state,{idx:c}),""));function m(){return(s.state||{idx:null}).idx}function d(){o=I.Pop;let p=m(),w=p==null?null:p-c;c=p,l&&l({action:o,location:v.location,delta:w})}function g(p,w){o=I.Push;let x=ne(v.location,p,w);n&&n(x,p),c=m()+1;let M=ge(x,c),F=v.createHref(x);try{s.pushState(M,"",F)}catch($){if($ instanceof DOMException&&$.name==="DataCloneError")throw $;r.location.assign(F)}i&&l&&l({action:o,location:v.location,delta:1})}function k(p,w){o=I.Replace;let x=ne(v.location,p,w);n&&n(x,p),c=m();let M=ge(x,c),F=v.createHref(x);s.replaceState(M,"",F),i&&l&&l({action:o,location:v.location,delta:0})}function P(p){let w=r.location.origin!=="null"?r.location.origin:r.location.href,x=typeof p=="string"?p:_e(p);return x=x.replace(/ $/,"%20"),W(w,"No window.location.(origin|href) available to create URL for href: "+x),new URL(x,w)}let v={get action(){return o},get location(){return e(r,s)},listen(p){if(l)throw new Error("A history only accepts one active listener");return r.addEventListener(de,d),l=p,()=>{r.removeEventListener(de,d),l=null}},createHref(p){return t(r,p)},createURL:P,encodeLocation(p){let w=P(p);return{pathname:w.pathname,search:w.search,hash:w.hash}},push:g,replace:k,go(p){return s.go(p)}};return v}var pe;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(pe||(pe={}));function kt(e,t,n){return n===void 0&&(n="/"),Ie(e,t,n,!1)}function Ie(e,t,n,a){let r=typeof t=="string"?A(t):t,i=qe(r.pathname||"/",n);if(i==null)return null;let s=ke(e);We(s);let o=null;for(let l=0;o==null&&l<s.length;++l){let c=Ne(i);o=Ve(s[l],c,a)}return o}function ke(e,t,n,a){t===void 0&&(t=[]),n===void 0&&(n=[]),a===void 0&&(a="");let r=(i,s,o)=>{let l={relativePath:o===void 0?i.path||"":o,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};l.relativePath.startsWith("/")&&(W(l.relativePath.startsWith(a),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+a+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(a.length));let c=D([a,l.relativePath]),m=n.concat(l);i.children&&i.children.length>0&&(W(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),ke(i.children,t,m,c)),!(i.path==null&&!i.index)&&t.push({path:c,score:Ce(c,i.index),routesMeta:m})};return e.forEach((i,s)=>{var o;if(i.path===""||!((o=i.path)!=null&&o.includes("?")))r(i,s);else for(let l of Fe(i.path))r(i,s,l)}),t}function Fe(e){let t=e.split("/");if(t.length===0)return[];let[n,...a]=t,r=n.endsWith("?"),i=n.replace(/\?$/,"");if(a.length===0)return r?[i,""]:[i];let s=Fe(a.join("/")),o=[];return o.push(...s.map(l=>l===""?i:[i,l].join("/"))),r&&o.push(...s),o.map(l=>e.startsWith("/")&&l===""?"/":l)}function We(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Be(t.routesMeta.map(a=>a.childrenIndex),n.routesMeta.map(a=>a.childrenIndex)))}const Le=/^:[\w-]+$/,He=3,Xe=2,je=1,Ae=10,Ue=-2,me=e=>e==="*";function Ce(e,t){let n=e.split("/"),a=n.length;return n.some(me)&&(a+=Ue),t&&(a+=Xe),n.filter(r=>!me(r)).reduce((r,i)=>r+(Le.test(i)?He:i===""?je:Ae),a)}function Be(e,t){return e.length===t.length&&e.slice(0,-1).every((a,r)=>a===t[r])?e[e.length-1]-t[t.length-1]:0}function Ve(e,t,n){n===void 0&&(n=!1);let{routesMeta:a}=e,r={},i="/",s=[];for(let o=0;o<a.length;++o){let l=a[o],c=o===a.length-1,m=i==="/"?t:t.slice(i.length)||"/",d=be({path:l.relativePath,caseSensitive:l.caseSensitive,end:c},m),g=l.route;if(!d&&c&&n&&!a[a.length-1].route.index&&(d=be({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},m)),!d)return null;Object.assign(r,d.params),s.push({params:r,pathname:D([i,d.pathname]),pathnameBase:ze(D([i,d.pathnameBase])),route:g}),d.pathnameBase!=="/"&&(i=D([i,d.pathnameBase]))}return s}function be(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,a]=Ze(e.path,e.caseSensitive,e.end),r=t.match(n);if(!r)return null;let i=r[0],s=i.replace(/(.)\/+$/,"$1"),o=r.slice(1);return{params:a.reduce((c,m,d)=>{let{paramName:g,isOptional:k}=m;if(g==="*"){let v=o[d]||"";s=i.slice(0,i.length-v.length).replace(/(.)\/+$/,"$1")}const P=o[d];return k&&!P?c[g]=void 0:c[g]=(P||"").replace(/%2F/g,"/"),c},{}),pathname:i,pathnameBase:s,pattern:e}}function Ze(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),ie(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let a=[],r="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,o,l)=>(a.push({paramName:o,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),r+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?r+="\\/*$":e!==""&&e!=="/"&&(r+="(?:(?=\\/|$))"),[new RegExp(r,t?void 0:"i"),a]}function Ne(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return ie(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function qe(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,a=e.charAt(n);return a&&a!=="/"?null:e.slice(n)||"/"}function Ke(e,t){t===void 0&&(t="/");let{pathname:n,search:a="",hash:r=""}=typeof e=="string"?A(e):e;return{pathname:n?n.startsWith("/")?n:De(n,t):t,search:Je(a),hash:Ge(r)}}function De(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(r=>{r===".."?n.length>1&&n.pop():r!=="."&&n.push(r)}),n.length>1?n.join("/"):"/"}function ee(e,t,n,a){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(a)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Qe(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Ft(e,t){let n=Qe(e);return t?n.map((a,r)=>r===n.length-1?a.pathname:a.pathnameBase):n.map(a=>a.pathnameBase)}function Et(e,t,n,a){a===void 0&&(a=!1);let r;typeof e=="string"?r=A(e):(r=j({},e),W(!r.pathname||!r.pathname.includes("?"),ee("?","pathname","search",r)),W(!r.pathname||!r.pathname.includes("#"),ee("#","pathname","hash",r)),W(!r.search||!r.search.includes("#"),ee("#","search","hash",r)));let i=e===""||r.pathname==="",s=i?"/":r.pathname,o;if(s==null)o=n;else{let d=t.length-1;if(!a&&s.startsWith("..")){let g=s.split("/");for(;g[0]==="..";)g.shift(),d-=1;r.pathname=g.join("/")}o=d>=0?t[d]:"/"}let l=Ke(r,o),c=s&&s!=="/"&&s.endsWith("/"),m=(i||s===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(c||m)&&(l.pathname+="/"),l}const D=e=>e.join("/").replace(/\/\/+/g,"/"),ze=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Je=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ge=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Rt(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Ee=["post","put","patch","delete"];new Set(Ee);const et=["get",...Ee];new Set(et);/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function U(e){return e+.5|0}const E=(e,t,n)=>Math.max(Math.min(e,n),t);function X(e){return E(U(e*2.55),0,255)}function R(e){return E(U(e*255),0,255)}function _(e){return E(U(e/2.55)/100,0,1)}function ye(e){return E(U(e*100),0,100)}const S={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ae=[..."0123456789ABCDEF"],tt=e=>ae[e&15],nt=e=>ae[(e&240)>>4]+ae[e&15],N=e=>(e&240)>>4===(e&15),at=e=>N(e.r)&&N(e.g)&&N(e.b)&&N(e.a);function rt(e){var t=e.length,n;return e[0]==="#"&&(t===4||t===5?n={r:255&S[e[1]]*17,g:255&S[e[2]]*17,b:255&S[e[3]]*17,a:t===5?S[e[4]]*17:255}:(t===7||t===9)&&(n={r:S[e[1]]<<4|S[e[2]],g:S[e[3]]<<4|S[e[4]],b:S[e[5]]<<4|S[e[6]],a:t===9?S[e[7]]<<4|S[e[8]]:255})),n}const it=(e,t)=>e<255?t(e):"";function st(e){var t=at(e)?tt:nt;return e?"#"+t(e.r)+t(e.g)+t(e.b)+it(e.a,t):void 0}const lt=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Re(e,t,n){const a=t*Math.min(n,1-n),r=(i,s=(i+e/30)%12)=>n-a*Math.max(Math.min(s-3,9-s,1),-1);return[r(0),r(8),r(4)]}function ft(e,t,n){const a=(r,i=(r+e/60)%6)=>n-n*t*Math.max(Math.min(i,4-i,1),0);return[a(5),a(3),a(1)]}function ot(e,t,n){const a=Re(e,1,.5);let r;for(t+n>1&&(r=1/(t+n),t*=r,n*=r),r=0;r<3;r++)a[r]*=1-t-n,a[r]+=t;return a}function ct(e,t,n,a,r){return e===r?(t-n)/a+(t<n?6:0):t===r?(n-e)/a+2:(e-t)/a+4}function se(e){const n=e.r/255,a=e.g/255,r=e.b/255,i=Math.max(n,a,r),s=Math.min(n,a,r),o=(i+s)/2;let l,c,m;return i!==s&&(m=i-s,c=o>.5?m/(2-i-s):m/(i+s),l=ct(n,a,r,m,i),l=l*60+.5),[l|0,c||0,o]}function le(e,t,n,a){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,n,a)).map(R)}function fe(e,t,n){return le(Re,e,t,n)}function ut(e,t,n){return le(ot,e,t,n)}function ht(e,t,n){return le(ft,e,t,n)}function Me(e){return(e%360+360)%360}function dt(e){const t=lt.exec(e);let n=255,a;if(!t)return;t[5]!==a&&(n=t[6]?X(+t[5]):R(+t[5]));const r=Me(+t[2]),i=+t[3]/100,s=+t[4]/100;return t[1]==="hwb"?a=ut(r,i,s):t[1]==="hsv"?a=ht(r,i,s):a=fe(r,i,s),{r:a[0],g:a[1],b:a[2],a:n}}function gt(e,t){var n=se(e);n[0]=Me(n[0]+t),n=fe(n),e.r=n[0],e.g=n[1],e.b=n[2]}function pt(e){if(!e)return;const t=se(e),n=t[0],a=ye(t[1]),r=ye(t[2]);return e.a<255?`hsla(${n}, ${a}%, ${r}%, ${_(e.a)})`:`hsl(${n}, ${a}%, ${r}%)`}const ve={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},we={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function mt(){const e={},t=Object.keys(we),n=Object.keys(ve);let a,r,i,s,o;for(a=0;a<t.length;a++){for(s=o=t[a],r=0;r<n.length;r++)i=n[r],o=o.replace(i,ve[i]);i=parseInt(we[s],16),e[o]=[i>>16&255,i>>8&255,i&255]}return e}let q;function bt(e){q||(q=mt(),q.transparent=[0,0,0,0]);const t=q[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const yt=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function vt(e){const t=yt.exec(e);let n=255,a,r,i;if(t){if(t[7]!==a){const s=+t[7];n=t[8]?X(s):E(s*255,0,255)}return a=+t[1],r=+t[3],i=+t[5],a=255&(t[2]?X(a):E(a,0,255)),r=255&(t[4]?X(r):E(r,0,255)),i=255&(t[6]?X(i):E(i,0,255)),{r:a,g:r,b:i,a:n}}}function wt(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${_(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}const te=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,T=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function xt(e,t,n){const a=T(_(e.r)),r=T(_(e.g)),i=T(_(e.b));return{r:R(te(a+n*(T(_(t.r))-a))),g:R(te(r+n*(T(_(t.g))-r))),b:R(te(i+n*(T(_(t.b))-i))),a:e.a+n*(t.a-e.a)}}function K(e,t,n){if(e){let a=se(e);a[t]=Math.max(0,Math.min(a[t]+a[t]*n,t===0?360:1)),a=fe(a),e.r=a[0],e.g=a[1],e.b=a[2]}}function $e(e,t){return e&&Object.assign(t||{},e)}function xe(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=R(e[3]))):(t=$e(e,{r:0,g:0,b:0,a:1}),t.a=R(t.a)),t}function Pt(e){return e.charAt(0)==="r"?vt(e):dt(e)}class re{constructor(t){if(t instanceof re)return t;const n=typeof t;let a;n==="object"?a=xe(t):n==="string"&&(a=rt(t)||bt(t)||Pt(t)),this._rgb=a,this._valid=!!a}get valid(){return this._valid}get rgb(){var t=$e(this._rgb);return t&&(t.a=_(t.a)),t}set rgb(t){this._rgb=xe(t)}rgbString(){return this._valid?wt(this._rgb):void 0}hexString(){return this._valid?st(this._rgb):void 0}hslString(){return this._valid?pt(this._rgb):void 0}mix(t,n){if(t){const a=this.rgb,r=t.rgb;let i;const s=n===i?.5:n,o=2*s-1,l=a.a-r.a,c=((o*l===-1?o:(o+l)/(1+o*l))+1)/2;i=1-c,a.r=255&c*a.r+i*r.r+.5,a.g=255&c*a.g+i*r.g+.5,a.b=255&c*a.b+i*r.b+.5,a.a=s*a.a+(1-s)*r.a,this.rgb=a}return this}interpolate(t,n){return t&&(this._rgb=xt(this._rgb,t._rgb,n)),this}clone(){return new re(this.rgb)}alpha(t){return this._rgb.a=R(t),this}clearer(t){const n=this._rgb;return n.a*=1-t,this}greyscale(){const t=this._rgb,n=U(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=n,this}opaquer(t){const n=this._rgb;return n.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return K(this._rgb,2,t),this}darken(t){return K(this._rgb,2,-t),this}saturate(t){return K(this._rgb,1,t),this}desaturate(t){return K(this._rgb,1,-t),this}rotate(t){return gt(this._rgb,t),this}}export{I as A,re as C,qe as a,Rt as b,_t as c,Ft as g,W as i,D as j,kt as m,A as p,Et as r,St as s};
