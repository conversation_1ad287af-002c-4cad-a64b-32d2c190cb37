import{r as it,R as gs}from"./chunk-81a058b1.js";import{C as ps}from"./chunk-81a949b4.js";/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function rt(){}const en=(()=>{let i=0;return()=>i++})();function L(i){return i==null}function z(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function D(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function j(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function J(i,t){return j(i)?i:t}function O(i,t){return typeof i>"u"?t:i}const sn=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100:+i/t,ms=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function I(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function T(i,t,e,s){let n,o,r;if(z(i))if(o=i.length,s)for(n=o-1;n>=0;n--)t.call(e,i[n],n);else for(n=0;n<o;n++)t.call(e,i[n],n);else if(D(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function fe(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function ge(i){if(z(i))return i.map(ge);if(D(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=ge(i[e[n]]);return t}return i}function bs(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function nn(i,t,e,s){if(!bs(i))return;const n=t[i],o=e[i];D(n)&&D(o)?Kt(n,o,s):t[i]=ge(o)}function Kt(i,t,e){const s=z(t)?t:[t],n=s.length;if(!D(i))return i;e=e||{};const o=e.merger||nn;let r;for(let a=0;a<n;++a){if(r=s[a],!D(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],i,r,e)}return i}function $t(i,t){return Kt(i,t,{merger:on})}function on(i,t,e){if(!bs(i))return;const s=t[i],n=e[i];D(s)&&D(n)?$t(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=ge(n))}const ni={"":i=>i,x:i=>i.x,y:i=>i.y};function rn(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function an(i){const t=rn(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function kt(i,t){return(ni[t]||(ni[t]=an(t)))(i)}function je(i){return i.charAt(0).toUpperCase()+i.slice(1)}const Xt=i=>typeof i<"u",ut=i=>typeof i=="function",oi=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function ln(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const H=Math.PI,F=2*H,pe=Number.POSITIVE_INFINITY,cn=H/180,V=H/2,pt=H/4,ri=H*2/3,ct=Math.log10,dt=Math.sign;function he(i,t,e){return Math.abs(i-t)<e}function ai(i){const t=Math.round(i);i=he(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(ct(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function hn(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function dn(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function me(i){return!dn(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function un(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function _s(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function ot(i){return i*(H/180)}function $e(i){return i*(180/H)}function li(i){if(!j(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function xs(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*H&&(o+=F),{angle:o,distance:n}}function fn(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function nt(i){return(i%F+F)%F}function be(i,t,e,s){const n=nt(i),o=nt(t),r=nt(e),a=nt(o-n),l=nt(r-n),c=nt(n-o),h=nt(n-r);return n===o||n===r||s&&o===r||a>l&&c<h}function G(i,t,e){return Math.max(t,Math.min(e,i))}function gn(i){return G(i,-32768,32767)}function yt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function Ye(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const Be=(i,t,e,s)=>Ye(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),pn=(i,t,e)=>Ye(i,e,s=>i[s][t]>=e);function mn(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const ys=["push","pop","shift","splice","unshift"];function bn(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),ys.forEach(e=>{const s="_onData"+je(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function ci(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(ys.forEach(o=>{delete i[o]}),delete i._chartjs)}function vs(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const Ms=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function ks(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Ms.call(window,()=>{s=!1,i.apply(t,e)}))}}function _n(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const Ue=i=>i==="start"?"left":i==="end"?"right":"center",U=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,xn=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t,te=i=>i===0||i===1,hi=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*F/e)),di=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*F/e)+1,Yt={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*V)+1,easeOutSine:i=>Math.sin(i*V),easeInOutSine:i=>-.5*(Math.cos(H*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>te(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>te(i)?i:hi(i,.075,.3),easeOutElastic:i=>te(i)?i:di(i,.075,.3),easeInOutElastic(i){return te(i)?i:i<.5?.5*hi(i*2,.1125,.45):.5+.5*di(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-Yt.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?Yt.easeInBounce(i*2)*.5:Yt.easeOutBounce(i*2-1)*.5+.5};function ws(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function ui(i){return ws(i)?i:new ps(i)}function Ce(i){return ws(i)?i:new ps(i).saturate(.5).darken(.1).hexString()}const yn=["x","y","borderWidth","radius","tension"],vn=["color","borderColor","backgroundColor"];function Mn(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:vn},numbers:{type:"number",properties:yn}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function kn(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const fi=new Map;function wn(i,t){t=t||{};const e=i+JSON.stringify(t);let s=fi.get(e);return s||(s=new Intl.NumberFormat(i,t),fi.set(e,s)),s}function ke(i,t,e){return wn(t,e).format(i)}const Ss={values(i){return z(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=Sn(i,e)}const r=ct(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),ke(i,s,l)},logarithmic(i,t,e){if(i===0)return"0";const s=e[t].significand||i/Math.pow(10,Math.floor(ct(i)));return[1,2,3,5,10,15].includes(s)||t>.8*e.length?Ss.numeric.call(this,i,t,e):""}};function Sn(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var we={formatters:Ss};function Pn(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:we.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const wt=Object.create(null),He=Object.create(null);function Ut(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function Ae(i,t,e){return typeof t=="string"?Kt(Ut(i,t),e):Kt(Ut(i,""),t)}class Cn{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>Ce(n.backgroundColor),this.hoverBorderColor=(s,n)=>Ce(n.borderColor),this.hoverColor=(s,n)=>Ce(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Ae(this,t,e)}get(t){return Ut(this,t)}describe(t,e){return Ae(He,t,e)}override(t,e){return Ae(wt,t,e)}route(t,e,s,n){const o=Ut(this,t),r=Ut(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[n];return D(l)?Object.assign({},c,l):O(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var B=new Cn({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Mn,kn,Pn]);function An(i){return!i||L(i.size)||L(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function _e(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function Dn(i,t,e,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(n=s.data={},o=s.garbageCollect=[],s.font=t),i.save(),i.font=t;let r=0;const a=e.length;let l,c,h,d,u;for(l=0;l<a;l++)if(d=e[l],d!=null&&!z(d))r=_e(i,n,o,r,d);else if(z(d))for(c=0,h=d.length;c<h;c++)u=d[c],u!=null&&!z(u)&&(r=_e(i,n,o,r,u));i.restore();const f=o.length/2;if(f>e.length){for(l=0;l<f;l++)delete n[o[l]];o.splice(0,f)}return r}function mt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function gi(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function pi(i,t,e,s){Ps(i,t,e,s,null)}function Ps(i,t,e,s,n){let o,r,a,l,c,h,d,u;const f=t.pointStyle,g=t.rotation,p=t.radius;let m=(g||0)*cn;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(m),i.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),i.restore();return}if(!(isNaN(p)||p<=0)){switch(i.beginPath(),f){default:n?i.ellipse(e,s,n/2,p,0,0,F):i.arc(e,s,p,0,F),i.closePath();break;case"triangle":h=n?n/2:p,i.moveTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=ri,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=ri,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),i.closePath();break;case"rectRounded":c=p*.516,l=p-c,r=Math.cos(m+pt)*l,d=Math.cos(m+pt)*(n?n/2-c:l),a=Math.sin(m+pt)*l,u=Math.sin(m+pt)*(n?n/2-c:l),i.arc(e-d,s-a,c,m-H,m-V),i.arc(e+u,s-r,c,m-V,m),i.arc(e+d,s+a,c,m,m+V),i.arc(e-u,s+r,c,m+V,m+H),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}m+=pt;case"rectRot":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+u,s-r),i.lineTo(e+d,s+a),i.lineTo(e-u,s+r),i.closePath();break;case"crossRot":m+=pt;case"cross":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r);break;case"star":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r),m+=pt,d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r);break;case"line":r=n?n/2:Math.cos(m)*p,a=Math.sin(m)*p,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function Tt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function Ke(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function Xe(i){i.restore()}function On(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),L(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Ln(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,h),i.lineTo(a,h),i.stroke()}}function Tn(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function St(i,t,e,s,n,o={}){const r=z(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,On(i,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Tn(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),L(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),Ln(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function qt(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,1.5*H,H,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,H,V,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,V,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-V,!0),i.lineTo(e+r.topLeft,s)}const Rn=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,In=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function En(i,t){const e=(""+i).match(Rn);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const zn=i=>+i||0;function qe(i,t){const e={},s=D(t),n=s?Object.keys(t):t,o=D(i)?s?r=>O(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=zn(o(r));return e}function Cs(i){return qe(i,{top:"y",right:"x",bottom:"y",left:"x"})}function vt(i){return qe(i,["topLeft","topRight","bottomLeft","bottomRight"])}function K(i){const t=Cs(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function N(i,t){i=i||{},t=t||B.font;let e=O(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=O(i.style,t.style);s&&!(""+s).match(In)&&(s=void 0);const n={family:O(i.family,t.family),lineHeight:En(O(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:O(i.weight,t.weight),string:""};return n.string=An(n),n}function ee(i,t,e,s){let n=!0,o,r,a;for(o=0,r=i.length;o<r;++o)if(a=i[o],a!==void 0&&(t!==void 0&&typeof a=="function"&&(a=a(t),n=!1),e!==void 0&&z(a)&&(a=a[e%a.length],n=!1),a!==void 0))return s&&!n&&(s.cacheable=!1),a}function Fn(i,t,e){const{min:s,max:n}=i,o=ms(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function Pt(i,t){return Object.assign(Object.create(i),t)}function Ge(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=Ls("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>Ge([a,...i],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete i[0][l],!0},get(a,l){return Ds(a,l,()=>Yn(l,t,i,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(a,l){return bi(a).includes(l)},ownKeys(a){return bi(a)},set(a,l,c){const h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function It(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:As(i,s),setContext:o=>It(i,o,e,s),override:o=>It(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return Ds(o,r,()=>Hn(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function As(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:ut(e)?e:()=>e,isIndexable:ut(s)?s:()=>s}}const Bn=(i,t)=>i?i+je(t):t,Qe=(i,t)=>D(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Ds(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function Hn(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return ut(a)&&r.isScriptable(t)&&(a=Wn(t,a,i,e)),z(a)&&a.length&&(a=Vn(t,a,i,r.isIndexable)),Qe(t,a)&&(a=It(a,n,o&&o[t],r)),a}function Wn(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);a.add(i);let l=t(o,r||s);return a.delete(i),Qe(i,l)&&(l=Je(n._scopes,n,i,l)),l}function Vn(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(D(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=Je(c,n,i,h);t.push(It(d,o,r&&r[i],a))}}return t}function Os(i,t,e){return ut(i)?i(t,e):i}const Nn=(i,t)=>i===!0?t:typeof i=="string"?kt(t,i):void 0;function jn(i,t,e,s,n){for(const o of t){const r=Nn(e,o);if(r){i.add(r);const a=Os(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==s)return a}else if(r===!1&&typeof s<"u"&&e!==s)return null}return!1}function Je(i,t,e,s){const n=t._rootScopes,o=Os(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=mi(a,r,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=mi(a,r,o,l,s),l===null)?!1:Ge(Array.from(a),[""],n,o,()=>$n(t,e,s))}function mi(i,t,e,s,n){for(;e;)e=jn(i,t,e,s,n);return e}function $n(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return z(n)&&D(e)?e:n||{}}function Yn(i,t,e,s){let n;for(const o of t)if(n=Ls(Bn(o,i),e),typeof n<"u")return Qe(i,n)?Je(e,s,i,n):n}function Ls(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function bi(i){let t=i._keys;return t||(t=i._keys=Un(i._scopes)),t}function Un(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}function Ze(){return typeof window<"u"&&typeof document<"u"}function ti(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function xe(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const Se=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function Kn(i,t){return Se(i).getPropertyValue(t)}const Xn=["top","right","bottom","left"];function Mt(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=Xn[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const qn=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function Gn(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if(qn(n,o,i.target))a=n,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function xt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=Se(e),o=n.boxSizing==="border-box",r=Mt(n,"padding"),a=Mt(n,"border","width"),{x:l,y:c,box:h}=Gn(i,e),d=r.left+(h&&a.left),u=r.top+(h&&a.top);let{width:f,height:g}=t;return o&&(f-=r.width+a.width,g-=r.height+a.height),{x:Math.round((l-d)/f*e.width/s),y:Math.round((c-u)/g*e.height/s)}}function Qn(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&ti(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=Se(o),l=Mt(a,"border","width"),c=Mt(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,s=xe(a.maxWidth,o,"clientWidth"),n=xe(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||pe,maxHeight:n||pe}}const ie=i=>Math.round(i*10)/10;function Jn(i,t,e,s){const n=Se(i),o=Mt(n,"margin"),r=xe(n.maxWidth,i,"clientWidth")||pe,a=xe(n.maxHeight,i,"clientHeight")||pe,l=Qn(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const u=Mt(n,"border","width"),f=Mt(n,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=ie(Math.min(c,r,l.maxWidth)),h=ie(Math.min(h,a,l.maxHeight)),c&&!h&&(h=ie(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=ie(Math.floor(h*s))),{width:c,height:h}}function _i(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const Zn=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};Ze()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function xi(i,t){const e=Kn(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}const to=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},eo=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function Rt(i,t,e){return i?to(t,e):eo()}function Ts(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function Rs(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function se(i,t,e){return i.options.clip?i[e]:t[e]}function io(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:se(e,t,"left"),right:se(e,t,"right"),top:se(s,t,"top"),bottom:se(s,t,"bottom")}:t}function so(i,t){const e=t._clip;if(e.disabled)return!1;const s=io(t,i.chartArea);return{left:e.left===!1?0:s.left-(e.left===!0?0:e.left),right:e.right===!1?i.width:s.right+(e.right===!0?0:e.right),top:e.top===!1?0:s.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?i.height:s.bottom+(e.bottom===!0?0:e.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class no{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=Ms.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var at=new no;const yi="transparent",oo={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=ui(i||yi),n=s.valid&&ui(t||yi);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class ro{constructor(t,e,s,n){const o=e[s];n=ee([t.to,n,o,t.from]);const r=ee([t.from,o,n]);this._active=!0,this._fn=t.fn||oo[t.type||typeof r],this._easing=Yt[t.easing]||Yt.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=ee([t.to,e,n,t.from]),this._from=ee([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class Is{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!D(t))return;const e=Object.keys(B.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!D(o))return;const r={};for(const a of e)r[a]=o[a];(z(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=lo(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&ao(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const u=s.get(c);if(d)if(u&&d.active()){d.update(u,h,a);continue}else d.cancel();if(!u||!u.duration){t[c]=h;continue}o[c]=d=new ro(u,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return at.add(this._chart,s),!0}}function ao(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function lo(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function vi(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function co(i,t,e){if(e===!1)return!1;const s=vi(i,e),n=vi(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function ho(i){let t,e,s,n;return D(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function Es(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Mi(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,c;if(t===null)return;let h=!1;for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(h=!0,s.all)continue;break}c=i.values[l],j(c)&&(o||t===0||dt(t)===dt(c))&&(t+=c)}return!h&&!s.all?0:t}function uo(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(i),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[n]:h,[o]:i[h]};return a}function De(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function fo(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function go(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function po(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function ki(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function wi(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=fo(o,r,s),d=t.length;let u;for(let f=0;f<d;++f){const g=t[f],{[l]:p,[c]:m}=g,b=g._stacks||(g._stacks={});u=b[c]=po(n,h,p),u[a]=m,u._top=ki(u,r,!0,s.type),u._bottom=ki(u,r,!1,s.type);const _=u._visualValues||(u._visualValues={});_[a]=m}}function Oe(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function mo(i,t){return Pt(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function bo(i,t,e){return Pt(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function Ft(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const Le=i=>i==="reset"||i==="none",Si=(i,t)=>t?i:Object.assign({},i),_o=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:Es(e,!0),values:null};class ei{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=De(t.vScale,t),this.addElements(),this.options.fill&&this.chart.isPluginEnabled("filler")}updateIndex(t){this.index!==t&&Ft(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,u,f,g)=>d==="x"?u:d==="r"?g:f,o=e.xAxisID=O(s.xAxisID,Oe(t,"x")),r=e.yAxisID=O(s.yAxisID,Oe(t,"y")),a=e.rAxisID=O(s.rAxisID,Oe(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&ci(this._data,this),t._stacked&&Ft(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(D(e)){const n=this._cachedMeta;this._data=uo(e,n)}else if(s!==e){if(s){ci(s,this);const n=this._cachedMeta;Ft(n),n._parsed=[]}e&&Object.isExtensible(e)&&bn(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=De(e.vScale,e),e.stack!==s.stack&&(n=!0,Ft(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&(wi(this,e._parsed),e._stacked=De(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,u;if(this._parsing===!1)s._parsed=n,s._sorted=!0,u=n;else{z(n[t])?u=this.parseArrayData(s,n,t,e):D(n[t])?u=this.parseObjectData(s,n,t,e):u=this.parsePrimitiveData(s,n,t,e);const f=()=>d[a]===null||c&&d[a]<c[a];for(h=0;h<e;++h)s._parsed[h+t]=d=u[h],l&&(f()&&(l=!1),c=d);s._sorted=l}r&&wi(this,u)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,d=new Array(n);let u,f,g;for(u=0,f=n;u<f;++u)g=u+s,d[u]={[a]:h||o.parse(c[g],g),[l]:r.parse(e[g],g)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],a[l]={x:o.parse(d[0],h),y:r.parse(d[1],h)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,u,f;for(h=0,d=n;h<d;++h)u=h+s,f=e[u],c[h]={x:o.parse(kt(f,a),u),y:r.parse(kt(f,l),u)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:Es(n,!0),values:e._stacks[t.axis]._visualValues};return Mi(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=Mi(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=_o(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=go(a);let u,f;function g(){f=n[u];const p=f[a.axis];return!j(f[t.axis])||h>p||d<p}for(u=0;u<r&&!(!g()&&(this.updateRangeFromParsed(c,t,f,l),o));++u);if(o){for(u=r-1;u>=0;--u)if(!g()){this.updateRangeFromParsed(c,t,f,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],j(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=ho(O(this.options.clip,co(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const d=n[h];d.hidden||(d.active&&c?r.push(d):d.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=bo(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=mo(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&Xt(s);if(a)return Si(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],u=c.getOptionScopes(this.getDataset(),h),f=Object.keys(B.elements[t]),g=()=>this.getContext(s,n,e),p=c.resolveNamedOptions(u,f,g,d);return p.$shared&&(p.$shared=l,o[r]=Object.freeze(Si(p,l))),p}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),u=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(u,this.getContext(t,s,e))}const c=new Is(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Le(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){Le(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!Le(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&Ft(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function xo(i,t){if(!i._cache.$bar){const e=i.getMatchingVisibleMetas(t);let s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=vs(s.sort((n,o)=>n-o))}return i._cache.$bar}function yo(i){const t=i.iScale,e=xo(t,i.type);let s=t._length,n,o,r,a;const l=()=>{r===32767||r===-32768||(Xt(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(n=0,o=e.length;n<o;++n)r=t.getPixelForValue(e[n]),l();for(a=void 0,n=0,o=t.ticks.length;n<o;++n)r=t.getPixelForTick(n),l();return s}function vo(i,t,e,s){const n=e.barThickness;let o,r;return L(n)?(o=t.min*e.categoryPercentage,r=e.barPercentage):(o=n*s,r=1),{chunk:o/s,ratio:r,start:t.pixels[i]-o/2}}function Mo(i,t,e,s){const n=t.pixels,o=n[i];let r=i>0?n[i-1]:null,a=i<n.length-1?n[i+1]:null;const l=e.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:e.barPercentage,start:c}}function ko(i,t,e,s){const n=e.parse(i[0],s),o=e.parse(i[1],s),r=Math.min(n,o),a=Math.max(n,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:r,max:a}}function zs(i,t,e,s){return z(i)?ko(i,t,e,s):t[e.axis]=e.parse(i,s),t}function Pi(i,t,e,s){const n=i.iScale,o=i.vScale,r=n.getLabels(),a=n===o,l=[];let c,h,d,u;for(c=e,h=e+s;c<h;++c)u=t[c],d={},d[n.axis]=a||n.parse(r[c],c),l.push(zs(u,d,o,c));return l}function Te(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function wo(i,t,e){return i!==0?dt(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function So(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function Po(i,t,e,s){let n=t.borderSkipped;const o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:h}=So(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=c:(e._bottom||0)===s?n=h:(o[Ci(h,r,a,l)]=!0,n=c)),o[Ci(n,r,a,l)]=!0,i.borderSkipped=o}function Ci(i,t,e,s){return s?(i=Co(i,t,e),i=Ai(i,e,t)):i=Ai(i,t,e),i}function Co(i,t,e){return i===t?e:i===e?t:i}function Ai(i,t,e){return i==="start"?t:i==="end"?e:i}function Ao(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}class Do extends ei{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,s,n){return Pi(t,e,s,n)}parseArrayData(t,e,s,n){return Pi(t,e,s,n)}parseObjectData(t,e,s,n){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,d=[];let u,f,g,p;for(u=s,f=s+n;u<f;++u)p=e[u],g={},g[o.axis]=o.parse(kt(p,c),u),d.push(zs(kt(p,h),g,r,u));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);const o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),r=o._custom,a=Te(r)?"["+r.start+", "+r.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){const o=n==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:u}=this._getSharedOptions(e,n);for(let f=e;f<e+s;f++){const g=this.getParsed(f),p=o||L(g[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),m=this._calculateBarIndexPixels(f,h),b=(g._stacks||{})[a.axis],_={horizontal:c,base:p.base,enableBorderRadius:!b||Te(g._custom)||r===b._top||r===b._bottom,x:c?p.head:m.center,y:c?m.center:p.head,height:c?m.size:Math.abs(p.size),width:c?Math.abs(p.size):m.size};u&&(_.options=d||this.resolveDataElementOptions(f,t[f].active?"active":n));const y=_.options||t[f].options;Po(_,y,b,r),Ao(_,y,h.ratio),this.updateElement(t[f],f,_,n)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=s.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(e),l=a&&a[s.axis],c=h=>{const d=h._parsed.find(f=>f[s.axis]===l),u=d&&d[h.vScale.axis];if(L(u)||isNaN(u))return!0};for(const h of n)if(!(e!==void 0&&c(h))&&((o===!1||r.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&r.push(h.stack),h.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,s){const n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,s=e.iScale,n=[];let o,r;for(o=0,r=e.data.length;o<r;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const a=t.barThickness;return{min:a||yo(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,h=Te(c);let d=l[e.axis],u=0,f=s?this.applyStack(e,l,s):d,g,p;f!==d&&(u=f-d,f=d),h&&(d=c.barStart,f=c.barEnd-c.barStart,d!==0&&dt(d)!==dt(c.barEnd)&&(u=0),u+=d);const m=!L(o)&&!h?o:u;let b=e.getPixelForValue(m);if(this.chart.getDataVisibility(t)?g=e.getPixelForValue(u+f):g=b,p=g-b,Math.abs(p)<r){p=wo(p,e,a)*r,d===a&&(b-=p/2);const _=e.getPixelForDecimal(0),y=e.getPixelForDecimal(1),v=Math.min(_,y),x=Math.max(_,y);b=Math.max(Math.min(b,x),v),g=b+p,s&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(g)-e.getValueForPixel(b))}if(b===e.getPixelForValue(a)){const _=dt(p)*e.getLineWidthForValue(a)/2;b+=_,p-=_}return{size:p,base:b,head:g,center:g+p/2}}_calculateBarIndexPixels(t,e){const s=e.scale,n=this.options,o=n.skipNull,r=O(n.maxBarThickness,1/0);let a,l;if(e.grouped){const c=o?this._getStackCount(t):e.stackCount,h=n.barThickness==="flex"?Mo(t,e,n,c):vo(t,e,n,c),d=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=h.start+h.chunk*d+h.chunk/2,l=Math.min(r,h.chunk*h.ratio)}else a=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(r,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length;let o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}function Oo(i,t,e){let s=1,n=1,o=0,r=0;if(t<F){const a=i,l=a+t,c=Math.cos(a),h=Math.sin(a),d=Math.cos(l),u=Math.sin(l),f=(y,v,x)=>be(y,a,l,!0)?1:Math.max(v,v*e,x,x*e),g=(y,v,x)=>be(y,a,l,!0)?-1:Math.min(v,v*e,x,x*e),p=f(0,c,d),m=f(V,h,u),b=g(H,c,d),_=g(H+V,h,u);s=(p-b)/2,n=(m-_)/2,o=-(p+b)/2,r=-(m+_)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:r}}class Lo extends ei{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const s=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=s;else{let o=l=>+s[l];if(D(s[t])){const{key:l="value"}=this._parsing;o=c=>+kt(s[c],l)}let r,a;for(r=t,a=t+e;r<a;++r)n._parsed[r]=o(r)}}_getRotation(){return ot(this.options.rotation-90)}_getCircumference(){return ot(this.options.circumference)}_getRotationExtents(){let t=F,e=-F;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const n=this.chart.getDatasetMeta(s).controller,o=n._getRotation(),r=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+r)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:s}=e,n=this._cachedMeta,o=n.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(s.width,s.height)-r)/2,0),l=Math.min(sn(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:u,ratioY:f,offsetX:g,offsetY:p}=Oo(d,h,l),m=(s.width-r)/u,b=(s.height-r)/f,_=Math.max(Math.min(m,b)/2,0),y=ms(this.options.radius,_),v=Math.max(y*l,0),x=(y-v)/this._getVisibleDatasetWeightTotal();this.offsetX=g*y,this.offsetY=p*y,n.total=this.calculateTotal(),this.outerRadius=y-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){const s=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/F)}updateElements(t,e,s,n){const o=n==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,h=(a.left+a.right)/2,d=(a.top+a.bottom)/2,u=o&&c.animateScale,f=u?0:this.innerRadius,g=u?0:this.outerRadius,{sharedOptions:p,includeOptions:m}=this._getSharedOptions(e,n);let b=this._getRotation(),_;for(_=0;_<e;++_)b+=this._circumference(_,o);for(_=e;_<e+s;++_){const y=this._circumference(_,o),v=t[_],x={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+y,circumference:y,outerRadius:g,innerRadius:f};m&&(x.options=p||this.resolveDataElementOptions(_,v.active?"active":n)),b+=y,this.updateElement(v,_,x,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let s=0,n;for(n=0;n<e.length;n++){const o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?F*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=ke(e._parsed[t],s.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0;const s=this.chart;let n,o,r,a,l;if(!t){for(n=0,o=s.data.datasets.length;n<o;++n)if(s.isDatasetVisible(n)){r=s.getDatasetMeta(n),t=r.data,a=r.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)l=a.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let s=0,n=t.length;s<n;++s){const o=this.resolveDataElementOptions(s);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(O(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class To extends Lo{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}function bt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class ii{static override(t){Object.assign(ii.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return bt()}parse(){return bt()}format(){return bt()}add(){return bt()}diff(){return bt()}startOf(){return bt()}endOf(){return bt()}}var Ro={_date:ii};function Io(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?pn:Be;if(s){if(n._sharedOptions){const h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const u=c(o,t,e-d),f=c(o,t,e+d);return{lo:u.lo,hi:f.hi}}}}else{const h=c(o,t,e);if(l){const{vScale:d}=n._cachedMeta,{_parsed:u}=i,f=u.slice(0,h.lo+1).reverse().findIndex(p=>!L(p[d.axis]));h.lo-=Math.max(0,f);const g=u.slice(h.hi).findIndex(p=>!L(p[d.axis]));h.hi+=Math.max(0,g)}return h}}return{lo:0,hi:o.length-1}}function Jt(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:d,hi:u}=Io(o[a],t,r,n);for(let f=d;f<=u;++f){const g=h[f];g.skip||s(g,c,f)}}}function Eo(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function Re(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||Jt(i,e,t,function(a,l,c){!n&&!Tt(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function zo(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:d}=xs(r,{x:t.x,y:t.y});be(d,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return Jt(i,e,t,o),n}function Fo(i,t,e,s,n,o){let r=[];const a=Eo(e);let l=Number.POSITIVE_INFINITY;function c(h,d,u){const f=h.inRange(t.x,t.y,n);if(s&&!f)return;const g=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(g))&&!f)return;const m=a(t,g);m<l?(r=[{element:h,datasetIndex:d,index:u}],l=m):m===l&&r.push({element:h,datasetIndex:d,index:u})}return Jt(i,e,t,c),r}function Ie(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?zo(i,t,e,n):Fo(i,t,e,s,n,o)}function Di(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return Jt(i,e,t,(l,c,h)=>{l[r]&&l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var Bo={evaluateInteractionItems:Jt,modes:{index(i,t,e,s){const n=xt(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?Re(i,n,o,s,r):Ie(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=xt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?Re(i,n,o,s,r):Ie(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=i.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(i,t,e,s){const n=xt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Re(i,n,o,s,r)},nearest(i,t,e,s){const n=xt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Ie(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=xt(t,i);return Di(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=xt(t,i);return Di(i,n,"y",e.intersect,s)}}};const Fs=["left","top","right","bottom"];function Bt(i,t){return i.filter(e=>e.pos===t)}function Oi(i,t){return i.filter(e=>Fs.indexOf(e.pos)===-1&&e.box.axis===t)}function Ht(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function Ho(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function Wo(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!Fs.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function Vo(i,t){const e=Wo(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&t.availableHeight)}return e}function No(i){const t=Ho(i),e=Ht(t.filter(c=>c.box.fullSize),!0),s=Ht(Bt(t,"left"),!0),n=Ht(Bt(t,"right")),o=Ht(Bt(t,"top"),!0),r=Ht(Bt(t,"bottom")),a=Oi(t,"x"),l=Oi(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:Bt(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function Li(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function Bs(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function jo(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!D(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&Bs(r,o.getPadding());const a=Math.max(0,t.outerWidth-Li(r,i,"left","right")),l=Math.max(0,t.outerHeight-Li(r,i,"top","bottom")),c=a!==i.w,h=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function $o(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function Yo(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function Nt(i,t,e,s){const n=[];let o,r,a,l,c,h;for(o=0,r=i.length,c=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,Yo(a.horizontal,t));const{same:d,other:u}=jo(t,e,a,s);c|=d&&n.length,h=h||u,l.fullSize||n.push(a)}return c&&Nt(n,t,e,s)||h}function ne(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function Ti(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,c=s[a.stack]||{count:1,placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const d=t.w*h,u=c.size||l.height;Xt(c.start)&&(r=c.start),l.fullSize?ne(l,n.left,r,e.outerWidth-n.right-n.left,u):ne(l,t.left+c.placed,r,d,u),c.start=r,c.placed+=d,r=l.bottom}else{const d=t.h*h,u=c.size||l.width;Xt(c.start)&&(o=c.start),l.fullSize?ne(l,o,n.top,u,e.outerHeight-n.bottom-n.top):ne(l,o,t.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=r}var Z={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=K(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=No(i.boxes),l=a.vertical,c=a.horizontal;T(i.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});const h=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),u=Object.assign({},n);Bs(u,K(s));const f=Object.assign({maxPadding:u,w:o,h:r,x:n.left,y:n.top},n),g=Vo(l.concat(c),d);Nt(a.fullSize,f,d,g),Nt(l,f,d,g),Nt(c,f,d,g)&&Nt(l,f,d,g),$o(f),Ti(a.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,Ti(a.rightAndBottom,f,d,g),i.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},T(a.chartArea,p=>{const m=p.box;Object.assign(m,i.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class Hs{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class Uo extends Hs{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const de="$chartjs",Ko={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Ri=i=>i===null||i==="";function Xo(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[de]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Ri(n)){const o=xi(i,"width");o!==void 0&&(i.width=o)}if(Ri(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=xi(i,"height");o!==void 0&&(i.height=o)}return i}const Ws=Zn?{passive:!0}:!1;function qo(i,t,e){i&&i.addEventListener(t,e,Ws)}function Go(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,Ws)}function Qo(i,t){const e=Ko[i.type]||i.type,{x:s,y:n}=xt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function ye(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function Jo(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||ye(a.addedNodes,s),r=r&&!ye(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function Zo(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||ye(a.removedNodes,s),r=r&&!ye(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const Gt=new Map;let Ii=0;function Vs(){const i=window.devicePixelRatio;i!==Ii&&(Ii=i,Gt.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function tr(i,t){Gt.size||window.addEventListener("resize",Vs),Gt.set(i,t)}function er(i){Gt.delete(i),Gt.size||window.removeEventListener("resize",Vs)}function ir(i,t,e){const s=i.canvas,n=s&&ti(s);if(!n)return;const o=ks((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),tr(i,o),r}function Ee(i,t,e){e&&e.disconnect(),t==="resize"&&er(i)}function sr(i,t,e){const s=i.canvas,n=ks(o=>{i.ctx!==null&&e(Qo(o,i))},i);return qo(s,t,n),n}class nr extends Hs{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Xo(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[de])return!1;const s=e[de].initial;["height","width"].forEach(o=>{const r=s[o];L(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[de],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:Jo,detach:Zo,resize:ir}[e]||sr;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:Ee,detach:Ee,resize:Ee}[e]||Go)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return Jn(t,e,s,n)}isAttached(t){const e=t&&ti(t);return!!(e&&e.isConnected)}}function or(i){return!Ze()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Uo:nr}class Ct{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return me(this.x)&&me(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}function rr(i,t){const e=i.options.ticks,s=ar(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?cr(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return hr(t,c,o,r/n),c;const h=lr(o,t,n);if(r>0){let d,u;const f=r>1?Math.round((l-a)/(r-1)):null;for(oe(t,c,h,L(f)?0:a-f,a),d=0,u=r-1;d<u;d++)oe(t,c,h,o[d],o[d+1]);return oe(t,c,h,l,L(f)?t.length:l+f),c}return oe(t,c,h),c}function ar(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function lr(i,t,e){const s=dr(i),n=t.length/e;if(!s)return Math.max(n,1);const o=hn(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function cr(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function hr(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function oe(i,t,e,s,n){const o=O(s,0),r=Math.min(O(n,i.length),i.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(i[c]),a++,h=Math.round(o+a*e))}function dr(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const ur=i=>i==="left"?"right":i==="right"?"left":i,Ei=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,zi=(i,t)=>Math.min(t||i,i);function Fi(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function fr(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,r-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function gr(i,t){T(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Wt(i){return i.drawTicks?i.tickLength:0}function Bi(i,t){if(!i.display)return 0;const e=N(i.font,t),s=K(i.padding);return(z(i.text)?i.text.length:1)*e.lineHeight+s.height}function pr(i,t){return Pt(i,{scale:t,type:"scale"})}function mr(i,t,e){return Pt(i,{tick:e,index:t,type:"tick"})}function br(i,t,e){let s=Ue(i);return(e&&t!=="right"||!e&&t==="right")&&(s=ur(s)),s}function _r(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,u,f,g;const p=r-n,m=a-o;if(i.isHorizontal()){if(f=U(s,o,a),D(e)){const b=Object.keys(e)[0],_=e[b];g=h[b].getPixelForValue(_)+p-t}else e==="center"?g=(c.bottom+c.top)/2+p-t:g=Ei(i,e,t);u=a-o}else{if(D(e)){const b=Object.keys(e)[0],_=e[b];f=h[b].getPixelForValue(_)-m+t}else e==="center"?f=(c.left+c.right)/2-m+t:f=Ei(i,e,t);g=U(s,r,n),d=e==="left"?-V:V}return{titleX:f,titleY:g,maxWidth:u,rotation:d}}class At extends Ct{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=J(t,Number.POSITIVE_INFINITY),e=J(e,Number.NEGATIVE_INFINITY),s=J(s,Number.POSITIVE_INFINITY),n=J(n,Number.NEGATIVE_INFINITY),{min:J(t,s),max:J(e,n),minDefined:j(t),maxDefined:j(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:J(e,J(s,e)),max:J(s,J(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){I(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Fn(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?Fi(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=rr(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){I(this.options.afterUpdate,[this])}beforeSetDimensions(){I(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){I(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),I(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){I(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=I(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){I(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){I(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=zi(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=G(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/s:f/(s-1),d+6>a&&(a=f/(s-(t.offset?.5:1)),l=this.maxHeight-Wt(t.grid)-e.padding-Bi(t.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),r=$e(Math.min(Math.asin(G((h.highest.height+6)/a,-1,1)),Math.asin(G(l/c,-1,1))-Math.asin(G(u/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){I(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){I(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Bi(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=Wt(o)+l):(t.height=this.maxHeight,t.width=Wt(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=s.padding*2,g=ot(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(a){const b=s.mirror?0:m*d.width+p*u.height;t.height=Math.min(this.maxHeight,t.height+b+f)}else{const b=s.mirror?0:p*d.width+m*u.height;t.width=Math.min(this.maxWidth,t.width+b+f)}this._calculatePadding(c,h,m,p)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;l?c?(u=n*t.width,f=s*e.height):(u=s*t.height,f=n*e.width):o==="start"?f=e.width:o==="end"?u=t.width:o!=="inner"&&(u=t.width/2,f=e.width/2),this.paddingLeft=Math.max((u-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+r)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){I(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)L(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=Fi(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/zi(e,s));let c=0,h=0,d,u,f,g,p,m,b,_,y,v,x;for(d=0;d<e;d+=l){if(g=t[d].label,p=this._resolveTickFontOptions(d),n.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},_=p.lineHeight,y=v=0,!L(g)&&!z(g))y=_e(n,b.data,b.gc,y,g),v=_;else if(z(g))for(u=0,f=g.length;u<f;++u)x=g[u],!L(x)&&!z(x)&&(y=_e(n,b.data,b.gc,y,x),v+=_);r.push(y),a.push(v),c=Math.max(y,c),h=Math.max(v,h)}gr(o,e);const M=r.indexOf(c),k=a.indexOf(h),w=S=>({width:r[S]||0,height:a[S]||0});return{first:w(0),last:w(e-1),widest:w(M),highest:w(k),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return gn(this._alignToPixels?mt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=mr(this.getContext(),t,s))}return this.$context||(this.$context=pr(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=ot(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=Wt(o),f=[],g=a.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(E){return mt(s,E,p)};let _,y,v,x,M,k,w,S,C,P,A,$;if(r==="top")_=b(this.bottom),k=this.bottom-u,S=_-m,P=b(t.top)+m,$=t.bottom;else if(r==="bottom")_=b(this.top),P=t.top,$=b(t.bottom)-m,k=_+m,S=this.top+u;else if(r==="left")_=b(this.right),M=this.right-u,w=_-m,C=b(t.left)+m,A=t.right;else if(r==="right")_=b(this.left),C=t.left,A=b(t.right)-m,M=_+m,w=this.left+u;else if(e==="x"){if(r==="center")_=b((t.top+t.bottom)/2+.5);else if(D(r)){const E=Object.keys(r)[0],W=r[E];_=b(this.chart.scales[E].getPixelForValue(W))}P=t.top,$=t.bottom,k=_+m,S=k+u}else if(e==="y"){if(r==="center")_=b((t.left+t.right)/2);else if(D(r)){const E=Object.keys(r)[0],W=r[E];_=b(this.chart.scales[E].getPixelForValue(W))}M=_-m,w=M-u,C=t.left,A=t.right}const Q=O(n.ticks.maxTicksLimit,d),R=Math.max(1,Math.ceil(d/Q));for(y=0;y<d;y+=R){const E=this.getContext(y),W=o.setContext(E),tt=a.setContext(E),Y=W.lineWidth,Dt=W.color,Zt=tt.dash||[],Ot=tt.dashOffset,Et=W.tickWidth,ft=W.tickColor,zt=W.tickBorderDash||[],gt=W.tickBorderDashOffset;v=fr(this,y,l),v!==void 0&&(x=mt(s,v,Y),c?M=w=C=A=x:k=S=P=$=x,f.push({tx1:M,ty1:k,tx2:w,ty2:S,x1:C,y1:P,x2:A,y2:$,width:Y,color:Dt,borderDash:Zt,borderDashOffset:Ot,tickWidth:Et,tickColor:ft,tickBorderDash:zt,tickBorderDashOffset:gt}))}return this._ticksLength=d,this._borderValue=_,f}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=Wt(s.grid),f=u+h,g=d?-h:f,p=-ot(this.labelRotation),m=[];let b,_,y,v,x,M,k,w,S,C,P,A,$="middle";if(n==="top")M=this.bottom-g,k=this._getXAxisLabelAlignment();else if(n==="bottom")M=this.top+g,k=this._getXAxisLabelAlignment();else if(n==="left"){const R=this._getYAxisLabelAlignment(u);k=R.textAlign,x=R.x}else if(n==="right"){const R=this._getYAxisLabelAlignment(u);k=R.textAlign,x=R.x}else if(e==="x"){if(n==="center")M=(t.top+t.bottom)/2+f;else if(D(n)){const R=Object.keys(n)[0],E=n[R];M=this.chart.scales[R].getPixelForValue(E)+f}k=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")x=(t.left+t.right)/2-f;else if(D(n)){const R=Object.keys(n)[0],E=n[R];x=this.chart.scales[R].getPixelForValue(E)}k=this._getYAxisLabelAlignment(u).textAlign}e==="y"&&(l==="start"?$="top":l==="end"&&($="bottom"));const Q=this._getLabelSizes();for(b=0,_=a.length;b<_;++b){y=a[b],v=y.label;const R=o.setContext(this.getContext(b));w=this.getPixelForTick(b)+o.labelOffset,S=this._resolveTickFontOptions(b),C=S.lineHeight,P=z(v)?v.length:1;const E=P/2,W=R.color,tt=R.textStrokeColor,Y=R.textStrokeWidth;let Dt=k;r?(x=w,k==="inner"&&(b===_-1?Dt=this.options.reverse?"left":"right":b===0?Dt=this.options.reverse?"right":"left":Dt="center"),n==="top"?c==="near"||p!==0?A=-P*C+C/2:c==="center"?A=-Q.highest.height/2-E*C+C:A=-Q.highest.height+C/2:c==="near"||p!==0?A=C/2:c==="center"?A=Q.highest.height/2-E*C:A=Q.highest.height-P*C,d&&(A*=-1),p!==0&&!R.showLabelBackdrop&&(x+=C/2*Math.sin(p))):(M=w,A=(1-P)*C/2);let Zt;if(R.showLabelBackdrop){const Ot=K(R.backdropPadding),Et=Q.heights[b],ft=Q.widths[b];let zt=A-Ot.top,gt=0-Ot.left;switch($){case"middle":zt-=Et/2;break;case"bottom":zt-=Et;break}switch(k){case"center":gt-=ft/2;break;case"right":gt-=ft;break;case"inner":b===_-1?gt-=ft:b>0&&(gt-=ft/2);break}Zt={left:gt,top:zt,width:ft+Ot.width,height:Et+Ot.height,color:R.backdropColor}}m.push({label:v,font:S,textOffset:A,options:{rotation:p,color:W,strokeColor:tt,strokeWidth:Y,textAlign:Dt,textBaseline:$,translation:[x,M],backdrop:Zt}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-ot(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,u;this.isHorizontal()?(c=mt(t,this.left,r)-r/2,h=mt(t,this.right,a)+a/2,d=u=l):(d=mt(t,this.top,r)-r/2,u=mt(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,u),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&Ke(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;St(s,c,0,h,l,a)}n&&Xe(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=N(s.font),r=K(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||D(e)?(l+=r.bottom,z(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:d,rotation:u}=_r(this,l,e,a);St(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:br(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=O(t.grid&&t.grid.z,-1),n=O(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==At.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return N(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class re{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;vr(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,xr(t,r,s),this.override&&B.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in B[n]&&(delete B[n][s],this.override&&delete wt[s])}}function xr(i,t,e){const s=Kt(Object.create(null),[e?B.get(e):{},B.get(t),i.defaults]);B.set(t,s),i.defaultRoutes&&yr(t,i.defaultRoutes),i.descriptors&&B.describe(t,i.descriptors)}function yr(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");B.route(o,n,l,a)})}function vr(i){return"id"in i&&"defaults"in i}class Mr{constructor(){this.controllers=new re(ei,"datasets",!0),this.elements=new re(Ct,"elements"),this.plugins=new re(Object,"plugins"),this.scales=new re(At,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):T(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=je(t);I(s["before"+n],[],s),e[t](s),I(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var st=new Mr;class kr{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(I(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){L(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=O(s.options&&s.options.plugins,{}),o=wr(s);return n===!1&&!e?[]:Pr(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function wr(i){const t={},e=[],s=Object.keys(st.plugins.items);for(let o=0;o<s.length;o++)e.push(st.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function Sr(i,t){return!t&&i===!1?null:i===!0?{}:i}function Pr(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,c=Sr(s[l],n);c!==null&&o.push({plugin:a,options:Cr(i.config,{plugin:a,local:e[l]},c,r)})}return o}function Cr(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function We(i,t){const e=B.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function Ar(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function Dr(i,t){return i===t?"_index_":"_value_"}function Hi(i){if(i==="x"||i==="y"||i==="r")return i}function Or(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function Ve(i,...t){if(Hi(i))return i;for(const e of t){const s=e.axis||Or(e.position)||i.length>1&&Hi(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function Wi(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function Lr(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return Wi(i,"x",e[0])||Wi(i,"y",e[0])}return{}}function Tr(i,t){const e=wt[i.type]||{scales:{}},s=t.scales||{},n=We(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!D(a)||a._proxy)return;const l=Ve(r,a,Lr(r,i),B.scales[a.type]),c=Dr(l,n),h=e.scales||{};o[r]=$t(Object.create(null),[{axis:l},a,h[l],h[c]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||We(a,t),h=(wt[a]||{}).scales||{};Object.keys(h).forEach(d=>{const u=Ar(d,l),f=r[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),$t(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];$t(a,[B.scales[a.type],B.scale])}),o}function Ns(i){const t=i.options||(i.options={});t.plugins=O(t.plugins,{}),t.scales=Tr(i,t)}function js(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function Rr(i){return i=i||{},i.data=js(i.data),Ns(i),i}const Vi=new Map,$s=new Set;function ae(i,t){let e=Vi.get(i);return e||(e=t(),Vi.set(i,e),$s.add(e)),e}const Vt=(i,t,e)=>{const s=kt(t,e);s!==void 0&&i.add(s)};class Ir{constructor(t){this._config=Rr(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=js(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Ns(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return ae(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return ae(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return ae(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return ae(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>Vt(l,t,d))),h.forEach(d=>Vt(l,n,d)),h.forEach(d=>Vt(l,wt[o]||{},d)),h.forEach(d=>Vt(l,B,d)),h.forEach(d=>Vt(l,He,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),$s.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,wt[e]||{},B.datasets[e]||{},{type:e},B,He]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=Ni(this._resolverCache,t,n);let l=r;if(zr(r,e)){o.$shared=!1,s=ut(s)?s():s;const c=this.createResolver(t,s,a);l=It(r,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=Ni(this._resolverCache,t,s);return D(e)?It(o,e,void 0,n):o}}function Ni(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:Ge(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const Er=i=>D(i)&&Object.getOwnPropertyNames(i).some(t=>ut(i[t]));function zr(i,t){const{isScriptable:e,isIndexable:s}=As(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(ut(a)||Er(a))||r&&z(a))return!0}return!1}var Fr="4.4.9";const Br=["top","bottom","left","right","chartArea"];function ji(i,t){return i==="top"||i==="bottom"||Br.indexOf(i)===-1&&t==="x"}function $i(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function Yi(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),I(e&&e.onComplete,[i],t)}function Hr(i){const t=i.chart,e=t.options.animation;I(e&&e.onProgress,[i],t)}function Ys(i){return Ze()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const ue={},Ui=i=>{const t=Ys(i);return Object.values(ue).filter(e=>e.canvas===t).pop()};function Wr(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function Vr(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}let si=class{static defaults=B;static instances=ue;static overrides=wt;static registry=st;static version=Fr;static getChart=Ui;static register(...t){st.add(...t),Ki()}static unregister(...t){st.remove(...t),Ki()}constructor(t,e){const s=this.config=new Ir(e),n=Ys(t),o=Ui(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||or(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;this.id=en(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new kr,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=_n(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],ue[this.id]=this,!(!a||!l)&&(at.listen(this,"complete",Yi),at.listen(this,"progress",Hr),this._initialize(),this.attached&&this.update())}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return L(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return st}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():_i(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return gi(this.canvas,this.ctx),this}stop(){return at.stop(this),this}resize(t,e){at.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,_i(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),I(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};T(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=Ve(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),T(o,r=>{const a=r.options,l=a.id,c=Ve(l,a),h=O(a.type,r.dtype);(a.position===void 0||ji(a.position,c)!==ji(r.dposition))&&(a.position=r.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const u=st.getScale(h);d=new u({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(a,t)}),T(n,(r,a)=>{r||delete s[a]}),T(s,r=>{Z.configure(this,r,r.options),Z.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort($i("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||We(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=st.getController(a),{datasetElementType:c,dataElementType:h}=B.datasets[a];Object.assign(l,{dataElementType:st.getElement(h),datasetElementType:c&&st.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){T(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),u=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(u),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||T(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort($i("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){T(this.scales,t=>{Z.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!oi(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;Wr(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!oi(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Z.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],T(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,ut(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(at.has(this)?this.attached&&!at.running(this)&&at.start(this):(this.draw(),Yi({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s={meta:t,index:t.index,cancelable:!0},n=so(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(n&&Ke(e,n),t.controller.draw(),n&&Xe(e),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return Tt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=Bo.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=Pt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);Xt(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),at.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),gi(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ue[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};T(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){T(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},T(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!fe(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=ln(t),c=Vr(t,this._lastEvent,s,l);s&&(this._lastEvent=null,I(o.onHover,[t,a,this],this),l&&I(o.onClick,[t,a,this],this));const h=!fe(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}};function Ki(){return T(si.instances,i=>i._plugins.invalidate())}function Nr(i,t,e){const{startAngle:s,pixelMargin:n,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=n/a;i.beginPath(),i.arc(o,r,a,s-c,e+c),l>n?(c=n/l,i.arc(o,r,l,e+c,s-c,!0)):i.arc(o,r,n,e+V,s-V),i.closePath(),i.clip()}function jr(i){return qe(i,["outerStart","outerEnd","innerStart","innerEnd"])}function $r(i,t,e,s){const n=jr(i.options.borderRadius),o=(e-t)/2,r=Math.min(o,s*t/2),a=l=>{const c=(e-Math.min(o,l))*s/2;return G(l,0,Math.min(o,c))};return{outerStart:a(n.outerStart),outerEnd:a(n.outerEnd),innerStart:G(n.innerStart,0,r),innerEnd:G(n.innerEnd,0,r)}}function Lt(i,t,e,s){return{x:e+i*Math.cos(t),y:s+i*Math.sin(t)}}function ve(i,t,e,s,n,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,d=Math.max(t.outerRadius+s+e-c,0),u=h>0?h+s+e+c:0;let f=0;const g=n-l;if(s){const R=h>0?h-s:0,E=d>0?d-s:0,W=(R+E)/2,tt=W!==0?g*W/(W+s):g;f=(g-tt)/2}const p=Math.max(.001,g*d-e/H)/d,m=(g-p)/2,b=l+m+f,_=n-m-f,{outerStart:y,outerEnd:v,innerStart:x,innerEnd:M}=$r(t,u,d,_-b),k=d-y,w=d-v,S=b+y/k,C=_-v/w,P=u+x,A=u+M,$=b+x/P,Q=_-M/A;if(i.beginPath(),o){const R=(S+C)/2;if(i.arc(r,a,d,S,R),i.arc(r,a,d,R,C),v>0){const Y=Lt(w,C,r,a);i.arc(Y.x,Y.y,v,C,_+V)}const E=Lt(A,_,r,a);if(i.lineTo(E.x,E.y),M>0){const Y=Lt(A,Q,r,a);i.arc(Y.x,Y.y,M,_+V,Q+Math.PI)}const W=(_-M/u+(b+x/u))/2;if(i.arc(r,a,u,_-M/u,W,!0),i.arc(r,a,u,W,b+x/u,!0),x>0){const Y=Lt(P,$,r,a);i.arc(Y.x,Y.y,x,$+Math.PI,b-V)}const tt=Lt(k,b,r,a);if(i.lineTo(tt.x,tt.y),y>0){const Y=Lt(k,S,r,a);i.arc(Y.x,Y.y,y,b-V,S)}}else{i.moveTo(r,a);const R=Math.cos(S)*d+r,E=Math.sin(S)*d+a;i.lineTo(R,E);const W=Math.cos(C)*d+r,tt=Math.sin(C)*d+a;i.lineTo(W,tt)}i.closePath()}function Yr(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){ve(i,t,e,s,l,n);for(let c=0;c<o;++c)i.fill();isNaN(a)||(l=r+(a%F||F))}return ve(i,t,e,s,l,n),i.fill(),l}function Ur(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u}=l,f=l.borderAlign==="inner";if(!c)return;i.setLineDash(d||[]),i.lineDashOffset=u,f?(i.lineWidth=c*2,i.lineJoin=h||"round"):(i.lineWidth=c,i.lineJoin=h||"bevel");let g=t.endAngle;if(o){ve(i,t,e,s,g,n);for(let p=0;p<o;++p)i.stroke();isNaN(a)||(g=r+(a%F||F))}f&&Nr(i,t,g),o||(ve(i,t,e,s,g,n),i.stroke())}class Wa extends Ct{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>t!=="borderDash"};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,s){const n=this.getProps(["x","y"],s),{angle:o,distance:r}=xs(n,{x:t,y:e}),{startAngle:a,endAngle:l,innerRadius:c,outerRadius:h,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),u=(this.options.spacing+this.options.borderWidth)/2,f=O(d,l-a),g=be(o,a,l)&&a!==l,p=f>=F||g,m=yt(r,c+u,h+u);return p&&m}getCenterPoint(t){const{x:e,y:s,startAngle:n,endAngle:o,innerRadius:r,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:l,spacing:c}=this.options,h=(n+o)/2,d=(r+a+c+l)/2;return{x:e+Math.cos(h)*d,y:s+Math.sin(h)*d}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:e,circumference:s}=this,n=(e.offset||0)/4,o=(e.spacing||0)/2,r=e.circular;if(this.pixelMargin=e.borderAlign==="inner"?.33:0,this.fullCircles=s>F?Math.floor(s/F):0,s===0||this.innerRadius<0||this.outerRadius<0)return;t.save();const a=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(a)*n,Math.sin(a)*n);const l=1-Math.sin(Math.min(H,s||0)),c=n*l;t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,Yr(t,this,c,o,r),Ur(t,this,c,o,r),t.restore()}}function Us(i,t){const{x:e,y:s,base:n,width:o,height:r}=i.getProps(["x","y","base","width","height"],t);let a,l,c,h,d;return i.horizontal?(d=r/2,a=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,a=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:a,top:c,right:l,bottom:h}}function ht(i,t,e,s){return i?0:G(t,e,s)}function Kr(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=Cs(s);return{t:ht(n.top,o.top,0,e),r:ht(n.right,o.right,0,t),b:ht(n.bottom,o.bottom,0,e),l:ht(n.left,o.left,0,t)}}function Xr(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=vt(n),r=Math.min(t,e),a=i.borderSkipped,l=s||D(n);return{topLeft:ht(!l||a.top||a.left,o.topLeft,0,r),topRight:ht(!l||a.top||a.right,o.topRight,0,r),bottomLeft:ht(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:ht(!l||a.bottom||a.right,o.bottomRight,0,r)}}function qr(i){const t=Us(i),e=t.right-t.left,s=t.bottom-t.top,n=Kr(i,e/2,s/2),o=Xr(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function ze(i,t,e,s){const n=t===null,o=e===null,a=i&&!(n&&o)&&Us(i,s);return a&&(n||yt(t,a.left,a.right))&&(o||yt(e,a.top,a.bottom))}function Gr(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function Qr(i,t){i.rect(t.x,t.y,t.w,t.h)}function Fe(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,r=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+r,radius:i.radius}}class Va extends Ct{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:r}=qr(this),a=Gr(r.radius)?qt:Qr;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,Fe(r,e,o)),t.clip(),a(t,Fe(o,-e,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,Fe(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return ze(this,t,e,s)}inXRange(t,e){return ze(this,t,null,e)}inYRange(t,e){return ze(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}const Xi=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},Jr=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class qi extends Ct{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=I(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=N(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=Xi(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let d=t;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((g,p)=>{const m=s+e/2+o.measureText(g.text).width;(p===0||c[c.length-1]+m+2*a>r)&&(d+=h,c[c.length-(p>0?0:1)]=0,f+=h,u++),l[p]={left:0,top:f,row:u,width:m,height:n},c[c.length-1]+=m+a}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let d=a,u=0,f=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:_,itemHeight:y}=Zr(s,e,o,m,n);b>0&&f+y+2*a>h&&(d+=u+a,c.push({width:u,height:f}),g+=u+a,p++,u=f=0),l[b]={left:g,top:f,col:p,width:_,height:y},u=Math.max(u,_),f+=y+a}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=Rt(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=U(s,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=U(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=U(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=U(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Ke(t,this),this._draw(),Xe(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=B.color,l=Rt(t.rtl,this.left,this.width),c=N(r.font),{padding:h}=r,d=c.size,u=d/2;let f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:g,boxHeight:p,itemHeight:m}=Xi(r,d),b=function(M,k,w){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();const S=O(w.lineWidth,1);if(n.fillStyle=O(w.fillStyle,a),n.lineCap=O(w.lineCap,"butt"),n.lineDashOffset=O(w.lineDashOffset,0),n.lineJoin=O(w.lineJoin,"miter"),n.lineWidth=S,n.strokeStyle=O(w.strokeStyle,a),n.setLineDash(O(w.lineDash,[])),r.usePointStyle){const C={radius:p*Math.SQRT2/2,pointStyle:w.pointStyle,rotation:w.rotation,borderWidth:S},P=l.xPlus(M,g/2),A=k+u;Ps(n,C,P,A,r.pointStyleWidth&&g)}else{const C=k+Math.max((d-p)/2,0),P=l.leftForLtr(M,g),A=vt(w.borderRadius);n.beginPath(),Object.values(A).some($=>$!==0)?qt(n,{x:P,y:C,w:g,h:p,radius:A}):n.rect(P,C,g,p),n.fill(),S!==0&&n.stroke()}n.restore()},_=function(M,k,w){St(n,w.text,M,k+m/2,c,{strikethrough:w.hidden,textAlign:l.textAlign(w.textAlign)})},y=this.isHorizontal(),v=this._computeTitleHeight();y?f={x:U(o,this.left+h,this.right-s[0]),y:this.top+h+v,line:0}:f={x:this.left+h,y:U(o,this.top+v+h,this.bottom-e[0].height),line:0},Ts(this.ctx,t.textDirection);const x=m+h;this.legendItems.forEach((M,k)=>{n.strokeStyle=M.fontColor,n.fillStyle=M.fontColor;const w=n.measureText(M.text).width,S=l.textAlign(M.textAlign||(M.textAlign=r.textAlign)),C=g+u+w;let P=f.x,A=f.y;l.setWidth(this.width),y?k>0&&P+C+h>this.right&&(A=f.y+=x,f.line++,P=f.x=U(o,this.left+h,this.right-s[f.line])):k>0&&A+x>this.bottom&&(P=f.x=P+e[f.line].width+h,f.line++,A=f.y=U(o,this.top+v+h,this.bottom-e[f.line].height));const $=l.x(P);if(b($,A,M),P=xn(S,P+g+u,y?P+C:this.right,t.rtl),_(l.x(P),A,M),y)f.x+=C+h;else if(typeof M.text!="string"){const Q=c.lineHeight;f.y+=Ks(M,Q)+h}else f.y+=x}),Rs(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=N(e.font),n=K(e.padding);if(!e.display)return;const o=Rt(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=U(t.align,d,this.right-u);else{const g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+U(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}const f=U(a,d,d+u);r.textAlign=o.textAlign(Ue(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,St(r,e.text,f,h,s)}_computeTitleHeight(){const t=this.options.title,e=N(t.font),s=K(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(yt(t,this.left,this.right)&&yt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],yt(t,n.left,n.left+n.width)&&yt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!ia(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=Jr(n,s);n&&!o&&I(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&I(e.onHover,[t,s,this],this)}else s&&I(e.onClick,[t,s,this],this)}}function Zr(i,t,e,s,n){const o=ta(s,i,t,e),r=ea(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function ta(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function ea(i,t,e){let s=i;return typeof t.text!="string"&&(s=Ks(t,e)),s}function Ks(i,t){const e=i.text?i.text.length:0;return t*e}function ia(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var Na={id:"legend",_element:qi,start(i,t,e){const s=i.legend=new qi({ctx:i.ctx,options:e,chart:i});Z.configure(i,s,e),Z.addBox(i,s)},stop(i){Z.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;Z.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=K(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class Xs extends Ct{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=z(s.text)?s.text.length:1;this._padding=K(s.padding);const o=n*N(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:r}=this,a=r.align;let l=0,c,h,d;return this.isHorizontal()?(h=U(a,s,o),d=e+t,c=o-s):(r.position==="left"?(h=s+t,d=U(a,n,e),l=H*-.5):(h=o-t,d=U(a,e,n),l=H*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=N(e.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);St(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:Ue(e.align),textBaseline:"middle",translation:[r,a]})}}function sa(i,t){const e=new Xs({ctx:i.ctx,options:t,chart:i});Z.configure(i,e,t),Z.addBox(i,e),i.titleBlock=e}var ja={id:"title",_element:Xs,start(i,t,e){sa(i,e)},stop(i){const t=i.titleBlock;Z.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;Z.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const jt={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const a=i[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=i.length;o<r;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=fn(t,c);h<n&&(n=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function et(i,t){return t&&(z(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function lt(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function na(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:i,label:r,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function Gi(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:r,boxHeight:a}=t,l=N(t.bodyFont),c=N(t.titleFont),h=N(t.footerFont),d=o.length,u=n.length,f=s.length,g=K(t.padding);let p=g.height,m=0,b=s.reduce((v,x)=>v+x.before.length+x.lines.length+x.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;p+=f*v+(b-f)*l.lineHeight+(b-1)*t.bodySpacing}u&&(p+=t.footerMarginTop+u*h.lineHeight+(u-1)*t.footerSpacing);let _=0;const y=function(v){m=Math.max(m,e.measureText(v).width+_)};return e.save(),e.font=c.string,T(i.title,y),e.font=l.string,T(i.beforeBody.concat(i.afterBody),y),_=t.displayColors?r+2+t.boxPadding:0,T(s,v=>{T(v.before,y),T(v.lines,y),T(v.after,y)}),_=0,e.font=h.string,T(i.footer,y),e.restore(),m+=g.width,{width:m,height:p}}function oa(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function ra(i,t,e,s){const{x:n,width:o}=s,r=e.caretSize+e.caretPadding;if(i==="left"&&n+o+r>t.width||i==="right"&&n-o-r<0)return!0}function aa(i,t,e,s){const{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=i;let c="center";return s==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),ra(c,i,t,e)&&(c="center"),c}function Qi(i,t,e){const s=e.yAlign||t.yAlign||oa(i,e);return{xAlign:e.xAlign||t.xAlign||aa(i,t,e,s),yAlign:s}}function la(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function ca(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function Ji(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=i,{xAlign:a,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=vt(r);let g=la(t,a);const p=ca(t,l,c);return l==="center"?a==="left"?g+=c:a==="right"&&(g-=c):a==="left"?g-=Math.max(h,u)+n:a==="right"&&(g+=Math.max(d,f)+n),{x:G(g,0,s.width-t.width),y:G(p,0,s.height-t.height)}}function le(i,t,e){const s=K(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function Zi(i){return et([],lt(i))}function ha(i,t,e){return Pt(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function ts(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const qs={beforeTitle:rt,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:rt,beforeBody:rt,beforeLabel:rt,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return L(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:rt,afterBody:rt,beforeFooter:rt,footer:rt,afterFooter:rt};function X(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?qs[t].call(e,s):n}class es extends Ct{static positioners=jt;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new Is(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=ha(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=X(s,"beforeTitle",this,t),o=X(s,"title",this,t),r=X(s,"afterTitle",this,t);let a=[];return a=et(a,lt(n)),a=et(a,lt(o)),a=et(a,lt(r)),a}getBeforeBody(t,e){return Zi(X(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return T(t,o=>{const r={before:[],lines:[],after:[]},a=ts(s,o);et(r.before,lt(X(a,"beforeLabel",this,o))),et(r.lines,X(a,"label",this,o)),et(r.after,lt(X(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return Zi(X(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=X(s,"beforeFooter",this,t),o=X(s,"footer",this,t),r=X(s,"afterFooter",this,t);let a=[];return a=et(a,lt(n)),a=et(a,lt(o)),a=et(a,lt(r)),a}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],r=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(na(this.chart,e[l]));return t.filter&&(a=a.filter((h,d,u)=>t.filter(h,d,u,s))),t.itemSort&&(a=a.sort((h,d)=>t.itemSort(h,d,s))),T(a,h=>{const d=ts(t.callbacks,h);n.push(X(d,"labelColor",this,h)),o.push(X(d,"labelPointStyle",this,h)),r.push(X(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const a=jt[s.position].call(this,n,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=Gi(this,s),c=Object.assign({},a,l),h=Qi(this.chart,s,c),d=Ji(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=vt(a),{x:u,y:f}=t,{width:g,height:p}=e;let m,b,_,y,v,x;return o==="center"?(v=f+p/2,n==="left"?(m=u,b=m-r,y=v+r,x=v-r):(m=u+g,b=m+r,y=v-r,x=v+r),_=m):(n==="left"?b=u+Math.max(l,h)+r:n==="right"?b=u+g-Math.max(c,d)-r:b=this.caretX,o==="top"?(y=f,v=y-r,m=b-r,_=b+r):(y=f+p,v=y+r,m=b+r,_=b-r),x=y),{x1:m,x2:b,x3:_,y1:y,y2:v,y3:x}}drawTitle(t,e,s){const n=this.title,o=n.length;let r,a,l;if(o){const c=Rt(s.rtl,this.x,this.width);for(t.x=le(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",r=N(s.titleFont),a=s.titleSpacing,e.fillStyle=s.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,e,s,n,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=N(o.bodyFont),d=le(this,"left",o),u=n.x(d),f=l<h.lineHeight?(h.lineHeight-l)/2:0,g=e.y+f;if(o.usePointStyle){const p={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=n.leftForLtr(u,c)+c/2,b=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,pi(t,p,m,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,pi(t,p,m,b)}else{t.lineWidth=D(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const p=n.leftForLtr(u,c),m=n.leftForLtr(n.xPlus(u,1),c-2),b=vt(r.borderRadius);Object.values(b).some(_=>_!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,qt(t,{x:p,y:g,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),qt(t,{x:m,y:g+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(p,g,c,l),t.strokeRect(p,g,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=N(s.bodyFont);let u=d.lineHeight,f=0;const g=Rt(s.rtl,this.x,this.width),p=function(w){e.fillText(w,g.x(t.x+f),t.y+u/2),t.y+=u+o},m=g.textAlign(r);let b,_,y,v,x,M,k;for(e.textAlign=r,e.textBaseline="middle",e.font=d.string,t.x=le(this,m,s),e.fillStyle=s.bodyColor,T(this.beforeBody,p),f=a&&m!=="right"?r==="center"?c/2+h:c+2+h:0,v=0,M=n.length;v<M;++v){for(b=n[v],_=this.labelTextColors[v],e.fillStyle=_,T(b.before,p),y=b.lines,a&&y.length&&(this._drawColorBox(e,t,v,g,s),u=Math.max(d.lineHeight,l)),x=0,k=y.length;x<k;++x)p(y[x]),u=d.lineHeight;T(b.after,p)}f=0,u=d.lineHeight,T(this.afterBody,p),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let r,a;if(o){const l=Rt(s.rtl,this.x,this.width);for(t.x=le(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",r=N(s.footerFont),e.fillStyle=s.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:u,bottomLeft:f,bottomRight:g}=vt(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+d,l),r==="top"&&this.drawCaret(t,e,s,n),e.lineTo(a+c-u,l),e.quadraticCurveTo(a+c,l,a+c,l+u),r==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(a+c,l+h-g),e.quadraticCurveTo(a+c,l+h,a+c-g,l+h),r==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(a+f,l+h),e.quadraticCurveTo(a,l+h,a,l+h-f),r==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(a,l+d),e.quadraticCurveTo(a,l,a+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const r=jt[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=Gi(this,t),l=Object.assign({},r,this._size),c=Qi(e,t,l),h=Ji(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=K(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),Ts(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),Rs(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!fe(s,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,s),a=this._positionChanged(r,t),l=e||!fe(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,r=jt[o.position].call(this,t,e);return r!==!1&&(s!==r.x||n!==r.y)}}var $a={id:"tooltip",_element:es,positioners:jt,afterInit(i,t,e){e&&(i.tooltip=new es({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:qs},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const da=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function ua(i,t,e,s){const n=i.indexOf(t);if(n===-1)return da(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const fa=(i,t)=>i===null?null:G(Math.round(i),0,t);function is(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class Ya extends At{static id="category";static defaults={ticks:{callback:is}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(L(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:ua(s,t,O(e,t),this._addedLabels),fa(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return is.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function ga(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=i,f=o||1,g=h-1,{min:p,max:m}=t,b=!L(r),_=!L(a),y=!L(c),v=(m-p)/(d+1);let x=ai((m-p)/g/f)*f,M,k,w,S;if(x<1e-14&&!b&&!_)return[{value:p},{value:m}];S=Math.ceil(m/x)-Math.floor(p/x),S>g&&(x=ai(S*x/g/f)*f),L(l)||(M=Math.pow(10,l),x=Math.ceil(x*M)/M),n==="ticks"?(k=Math.floor(p/x)*x,w=Math.ceil(m/x)*x):(k=p,w=m),b&&_&&o&&un((a-r)/o,x/1e3)?(S=Math.round(Math.min((a-r)/x,h)),x=(a-r)/S,k=r,w=a):y?(k=b?r:k,w=_?a:w,S=c-1,x=(w-k)/S):(S=(w-k)/x,he(S,Math.round(S),x/1e3)?S=Math.round(S):S=Math.ceil(S));const C=Math.max(li(x),li(k));M=Math.pow(10,L(l)?C:l),k=Math.round(k*M)/M,w=Math.round(w*M)/M;let P=0;for(b&&(u&&k!==r?(e.push({value:r}),k<r&&P++,he(Math.round((k+P*x)*M)/M,r,ss(r,v,i))&&P++):k<r&&P++);P<S;++P){const A=Math.round((k+P*x)*M)/M;if(_&&A>a)break;e.push({value:A})}return _&&u&&w!==a?e.length&&he(e[e.length-1].value,a,ss(a,v,i))?e[e.length-1].value=a:e.push({value:a}):(!_||w===a)&&e.push({value:w}),e}function ss(i,t,{horizontal:e,minRotation:s}){const n=ot(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class Me extends At{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return L(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=dt(n),c=dt(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=ga(n,o);return t.bounds==="ticks"&&_s(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return ke(t,this.chart.options.locale,this.options.ticks.format)}}class Ua extends Me{static id="linear";static defaults={ticks:{callback:we.formatters.numeric}};determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=j(t)?t:0,this.max=j(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=ot(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}const Qt=i=>Math.floor(ct(i)),_t=(i,t)=>Math.pow(10,Qt(i)+t);function ns(i){return i/Math.pow(10,Qt(i))===1}function os(i,t,e){const s=Math.pow(10,e),n=Math.floor(i/s);return Math.ceil(t/s)-n}function pa(i,t){const e=t-i;let s=Qt(e);for(;os(i,t,s)>10;)s++;for(;os(i,t,s)<10;)s--;return Math.min(s,Qt(i))}function ma(i,{min:t,max:e}){t=J(i.min,t);const s=[],n=Qt(t);let o=pa(t,e),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((t-l)*r)/r,h=Math.floor((t-l)/a/10)*a*10;let d=Math.floor((c-h)/Math.pow(10,o)),u=J(i.min,Math.round((l+h+d*Math.pow(10,o))*r)/r);for(;u<e;)s.push({value:u,major:ns(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,r=o>=0?1:r),u=Math.round((l+h+d*Math.pow(10,o))*r)/r;const f=J(i.max,u);return s.push({value:f,major:ns(f),significand:d}),s}class Ka extends At{static id="logarithmic";static defaults={ticks:{callback:we.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const s=Me.prototype.parse.apply(this,[t,e]);if(s===0){this._zero=!0;return}return j(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=j(t)?Math.max(0,t):null,this.max=j(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!j(this._userMin)&&(this.min=t===_t(this.min,0)?_t(this.min,-1):_t(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let s=this.min,n=this.max;const o=a=>s=t?s:a,r=a=>n=e?n:a;s===n&&(s<=0?(o(1),r(10)):(o(_t(s,-1)),r(_t(n,1)))),s<=0&&o(_t(n,-1)),n<=0&&r(_t(s,1)),this.min=s,this.max=n}buildTicks(){const t=this.options,e={min:this._userMin,max:this._userMax},s=ma(e,this);return t.bounds==="ticks"&&_s(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":ke(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=ct(t),this._valueRange=ct(this.max)-ct(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(ct(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function Ne(i){const t=i.ticks;if(t.display&&i.display){const e=K(t.backdropPadding);return O(t.font&&t.font.size,B.font.size)+e.height}return 0}function ba(i,t,e){return e=z(e)?e:[e],{w:Dn(i,t.string,e),h:e.length*t.lineHeight}}function rs(i,t,e,s,n){return i===s||i===n?{start:t-e/2,end:t+e/2}:i<s||i>n?{start:t-e,end:t}:{start:t,end:t+e}}function _a(i){const t={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},e=Object.assign({},t),s=[],n=[],o=i._pointLabels.length,r=i.options.pointLabels,a=r.centerPointLabels?H/o:0;for(let l=0;l<o;l++){const c=r.setContext(i.getPointLabelContext(l));n[l]=c.padding;const h=i.getPointPosition(l,i.drawingArea+n[l],a),d=N(c.font),u=ba(i.ctx,d,i._pointLabels[l]);s[l]=u;const f=nt(i.getIndexAngle(l)+a),g=Math.round($e(f)),p=rs(g,h.x,u.w,0,180),m=rs(g,h.y,u.h,90,270);xa(e,t,f,p,m)}i.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),i._pointLabelItems=Ma(i,s,n)}function xa(i,t,e,s,n){const o=Math.abs(Math.sin(e)),r=Math.abs(Math.cos(e));let a=0,l=0;s.start<t.l?(a=(t.l-s.start)/o,i.l=Math.min(i.l,t.l-a)):s.end>t.r&&(a=(s.end-t.r)/o,i.r=Math.max(i.r,t.r+a)),n.start<t.t?(l=(t.t-n.start)/r,i.t=Math.min(i.t,t.t-l)):n.end>t.b&&(l=(n.end-t.b)/r,i.b=Math.max(i.b,t.b+l))}function ya(i,t,e){const s=i.drawingArea,{extra:n,additionalAngle:o,padding:r,size:a}=e,l=i.getPointPosition(t,s+n+r,o),c=Math.round($e(nt(l.angle+V))),h=Sa(l.y,a.h,c),d=ka(c),u=wa(l.x,a.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:u,top:h,right:u+a.w,bottom:h+a.h}}function va(i,t){if(!t)return!0;const{left:e,top:s,right:n,bottom:o}=i;return!(Tt({x:e,y:s},t)||Tt({x:e,y:o},t)||Tt({x:n,y:s},t)||Tt({x:n,y:o},t))}function Ma(i,t,e){const s=[],n=i._pointLabels.length,o=i.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:Ne(o)/2,additionalAngle:r?H/n:0};let c;for(let h=0;h<n;h++){l.padding=e[h],l.size=t[h];const d=ya(i,h,l);s.push(d),a==="auto"&&(d.visible=va(d,c),d.visible&&(c=d))}return s}function ka(i){return i===0||i===180?"center":i<180?"left":"right"}function wa(i,t,e){return e==="right"?i-=t:e==="center"&&(i-=t/2),i}function Sa(i,t,e){return e===90||e===270?i-=t/2:(e>270||e<90)&&(i-=t),i}function Pa(i,t,e){const{left:s,top:n,right:o,bottom:r}=e,{backdropColor:a}=t;if(!L(a)){const l=vt(t.borderRadius),c=K(t.backdropPadding);i.fillStyle=a;const h=s-c.left,d=n-c.top,u=o-s+c.width,f=r-n+c.height;Object.values(l).some(g=>g!==0)?(i.beginPath(),qt(i,{x:h,y:d,w:u,h:f,radius:l}),i.fill()):i.fillRect(h,d,u,f)}}function Ca(i,t){const{ctx:e,options:{pointLabels:s}}=i;for(let n=t-1;n>=0;n--){const o=i._pointLabelItems[n];if(!o.visible)continue;const r=s.setContext(i.getPointLabelContext(n));Pa(e,r,o);const a=N(r.font),{x:l,y:c,textAlign:h}=o;St(e,i._pointLabels[n],l,c+a.lineHeight/2,a,{color:r.color,textAlign:h,textBaseline:"middle"})}}function Gs(i,t,e,s){const{ctx:n}=i;if(e)n.arc(i.xCenter,i.yCenter,t,0,F);else{let o=i.getPointPosition(0,t);n.moveTo(o.x,o.y);for(let r=1;r<s;r++)o=i.getPointPosition(r,t),n.lineTo(o.x,o.y)}}function Aa(i,t,e,s,n){const o=i.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!s||!a||!l||e<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(n.dash||[]),o.lineDashOffset=n.dashOffset,o.beginPath(),Gs(i,e,r,s),o.closePath(),o.stroke(),o.restore())}function Da(i,t,e){return Pt(i,{label:e,index:t,type:"pointLabel"})}class Xa extends Me{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:we.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=K(Ne(this.options)/2),e=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(e,s)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=j(t)&&!isNaN(t)?t:0,this.max=j(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Ne(this.options))}generateTickLabels(t){Me.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,s)=>{const n=I(this.options.pointLabels.callback,[e,s],this);return n||n===0?n:""}).filter((e,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?_a(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,s,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,s,n))}getIndexAngle(t){const e=F/(this._pointLabels.length||1),s=this.options.startAngle||0;return nt(t*e+ot(s))}getDistanceFromCenterForValue(t){if(L(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(L(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const s=e[t];return Da(this.getContext(),t,s)}}getPointPosition(t,e,s=0){const n=this.getIndexAngle(t)-V+s;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:s,right:n,bottom:o}=this._pointLabelItems[t];return{left:e,top:s,right:n,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),Gs(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:s,grid:n,border:o}=e,r=this._pointLabels.length;let a,l,c;if(e.pointLabels.display&&Ca(this,r),n.display&&this.ticks.forEach((h,d)=>{if(d!==0||d===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const u=this.getContext(d),f=n.setContext(u),g=o.setContext(u);Aa(this,f,l,r,g)}}),s.display){for(t.save(),a=r-1;a>=0;a--){const h=s.setContext(this.getPointLabelContext(a)),{color:d,lineWidth:u}=h;!u||!d||(t.lineWidth=u,t.strokeStyle=d,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(e.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,s=e.ticks;if(!s.display)return;const n=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!e.reverse)return;const c=s.setContext(this.getContext(l)),h=N(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const d=K(c.backdropPadding);t.fillRect(-r/2-d.left,-o-h.size/2-d.top,r+d.width,h.size+d.height)}St(t,a.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}const Pe={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},q=Object.keys(Pe);function as(i,t){return i-t}function ls(i,t){if(L(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),j(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(me(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function cs(i,t,e,s){const n=q.length;for(let o=q.indexOf(i);o<n-1;++o){const r=Pe[q[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return q[o]}return q[n-1]}function Oa(i,t,e,s,n){for(let o=q.length-1;o>=q.indexOf(e);o--){const r=q[o];if(Pe[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return q[e?q.indexOf(e):0]}function La(i){for(let t=q.indexOf(i)+1,e=q.length;t<e;++t)if(Pe[q[t]].common)return q[t]}function hs(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=Ye(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function Ta(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function ds(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:Ta(i,s,n,e)}class us extends At{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new Ro._date(t.adapters.date);n.init(e),$t(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:ls(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=j(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=j(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=mn(n,o,r);return this._unit=e.unit||(s.autoSkip?cs(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Oa(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:La(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),ds(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=G(e,0,r),s=G(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||cs(o.minUnit,e,s,this._getLabelCapacity(e)),a=O(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=me(l)||l===!0,h={};let d=e,u,f;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const g=n.ticks.source==="data"&&this.getDataTimestamps();for(u=d,f=0;u<s;u=+t.add(u,a,r),f++)hs(h,u,g);return(u===s||n.bounds==="ticks"||f===1)&&hs(h,u,g),Object.keys(h).sort(as).map(p=>+p)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return I(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],d=c&&a[c],u=s[e],f=c&&d&&u&&u.major;return this._adapter.format(t,n||(f?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=ot(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,ds(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(ls(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return vs(t.sort(as))}}function ce(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=Be(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=Be(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class qa extends us{static id="timeseries";static defaults=us.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=ce(e,this.min),this._tableRange=ce(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(ce(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return ce(this._table,s*this._tableRange+this._minPos,!0)}}const Qs="label";function fs(i,t){typeof i=="function"?i(t):i&&(i.current=t)}function Ra(i,t){const e=i.options;e&&t&&Object.assign(e,t)}function Js(i,t){i.labels=t}function Zs(i,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Qs;const s=[];i.datasets=t.map(n=>{const o=i.datasets.find(r=>r[e]===n[e]);return!o||!n.data||s.includes(o)?{...n}:(s.push(o),Object.assign(o,n),o)})}function Ia(i){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Qs;const e={labels:[],datasets:[]};return Js(e,i.labels),Zs(e,i.datasets,t),e}function Ea(i,t){const{height:e=150,width:s=300,redraw:n=!1,datasetIdKey:o,type:r,data:a,options:l,plugins:c=[],fallbackContent:h,updateMode:d,...u}=i,f=it.useRef(null),g=it.useRef(null),p=()=>{f.current&&(g.current=new si(f.current,{type:r,data:Ia(a,o),options:l&&{...l},plugins:c}),fs(t,g.current))},m=()=>{fs(t,null),g.current&&(g.current.destroy(),g.current=null)};return it.useEffect(()=>{!n&&g.current&&l&&Ra(g.current,l)},[n,l]),it.useEffect(()=>{!n&&g.current&&Js(g.current.config.data,a.labels)},[n,a.labels]),it.useEffect(()=>{!n&&g.current&&a.datasets&&Zs(g.current.config.data,a.datasets,o)},[n,a.datasets]),it.useEffect(()=>{g.current&&(n?(m(),setTimeout(p)):g.current.update(d))},[n,l,a.labels,a.datasets,d]),it.useEffect(()=>{g.current&&(m(),setTimeout(p))},[r]),it.useEffect(()=>(p(),()=>m()),[]),gs.createElement("canvas",{ref:f,role:"img",height:e,width:s,...u},h)}const za=it.forwardRef(Ea);function tn(i,t){return si.register(t),it.forwardRef((e,s)=>gs.createElement(za,{...e,ref:s,type:i}))}const Ga=tn("bar",Do),Qa=tn("pie",To);export{Wa as A,Va as B,si as C,Ua as L,Qa as P,Ya as a,$a as b,Na as c,Ga as d,ja as p};
