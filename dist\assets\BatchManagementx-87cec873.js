import { u as useNavigate, r as reactExports, j as jsxDevRuntimeExports } from './chunk-2ef8e52b.js';
import { b as useDatabase, u as useAuth } from './main-56ca96d9.js';
import { e as exportStudentsToCSV } from './chunk-4660e7bb.js';
import { U as Users, y as Plus, R as RefreshCw, A as AlertCircle, C as CheckCircle, e as UserPlus, O as Archive, m as Search, Q as Filter, V as ArrowUpDown, l as Eye, D as PenSquare, W as Download } from './chunk-028772a4.js';
import './chunk-03d61bd9.js';
import './chunk-8b627984.js';

const BatchManagement = () => {
  const navigate = useNavigate();
  const {
    getAllBatches,
    getBatchDetails,
    getBatchStatistics,
    archiveBatch,
    activateBatch
  } = useDatabase();
  const { user } = useAuth();
  const [batches, setBatches] = reactExports.useState([]);
  const [filteredBatches, setFilteredBatches] = reactExports.useState([]);
  const [statistics, setStatistics] = reactExports.useState(null);
  const [loading, setLoading] = reactExports.useState(true);
  const [message, setMessage] = reactExports.useState({ type: "", text: "" });
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [selectedAcademicYear, setSelectedAcademicYear] = reactExports.useState("all");
  const [selectedCourseType, setSelectedCourseType] = reactExports.useState("all");
  const [selectedStatus, setSelectedStatus] = reactExports.useState("all");
  const [sortBy, setSortBy] = reactExports.useState("created_at");
  const [sortOrder, setSortOrder] = reactExports.useState("desc");
  const [showFilters, setShowFilters] = reactExports.useState(false);
  reactExports.useEffect(() => {
    loadData();
  }, []);
  reactExports.useEffect(() => {
    filterBatches();
  }, [batches, searchTerm, selectedAcademicYear, selectedCourseType, selectedStatus, sortBy, sortOrder]);
  const loadData = async () => {
    try {
      setLoading(true);
      const filters = user.role === "teacher" ? { teacherId: user.id } : {};
      const [batchData, statsData] = await Promise.all([
        getAllBatches(filters),
        getBatchStatistics(user.id)
      ]);
      setBatches(batchData);
      setStatistics(statsData);
    } catch {
      showMessage("error", "Failed to load batch data");
    } finally {
      setLoading(false);
    }
  };
  const filterBatches = () => {
    let filtered = [...batches];
    if (searchTerm) {
      filtered = filtered.filter(
        (batch) => batch.name.toLowerCase().includes(searchTerm.toLowerCase()) || batch.teacher_name.toLowerCase().includes(searchTerm.toLowerCase()) || batch.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    if (selectedAcademicYear !== "all") {
      filtered = filtered.filter((batch) => batch.academic_year === selectedAcademicYear);
    }
    if (selectedCourseType !== "all") {
      filtered = filtered.filter((batch) => batch.course_type === selectedCourseType);
    }
    if (selectedStatus !== "all") {
      filtered = filtered.filter(
        (batch) => selectedStatus === "active" ? batch.is_active : !batch.is_active
      );
    }
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      if (sortBy === "created_at") {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }
      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    setFilteredBatches(filtered);
  };
  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: "", text: "" }), 5e3);
  };
  const handleViewBatch = (batchId) => {
    navigate(`/batch-details/${batchId}`);
  };
  const handleEditBatch = (batchId) => {
    navigate(`/edit-batch/${batchId}`);
  };
  const handleArchiveBatch = async (batchId) => {
    try {
      await archiveBatch(batchId, user.id, user.role === "teacher" ? user.id : null);
      showMessage("success", "Batch archived successfully");
      loadData();
    } catch {
      showMessage("error", "Failed to archive batch");
    }
  };
  const handleActivateBatch = async (batchId) => {
    try {
      await activateBatch(batchId, user.id, user.role === "teacher" ? user.id : null);
      showMessage("success", "Batch activated successfully");
      loadData();
    } catch {
      showMessage("error", "Failed to activate batch");
    }
  };
  const handleExportBatch = async (batch) => {
    try {
      const batchDetails = await getBatchDetails(batch.id, user.role === "teacher" ? user.id : null);
      exportStudentsToCSV(batchDetails.students, `${batch.name}_students.csv`);
      showMessage("success", "Student list exported successfully");
    } catch {
      showMessage("error", "Failed to export student list");
    }
  };
  const getUniqueAcademicYears = () => {
    const years = [...new Set(batches.map((b) => b.academic_year))].filter(Boolean);
    return years.sort();
  };
  const getUniqueCourseTypes = () => {
    const types = [...new Set(batches.map((b) => b.course_type))].filter(Boolean);
    return types.sort();
  };
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("asc");
    }
  };
  if (!user || !["teacher", "admin", "super_admin"].includes(user.role)) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-center min-h-screen", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "mx-auto h-12 w-12 text-red-500" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 198,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "mt-2 text-lg font-medium text-gray-900", children: "Access Denied" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 199,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "You need teacher or admin privileges to manage batches." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 200,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 197,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 196,
      columnNumber: 7
    }, globalThis);
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-2xl font-bold text-gray-900", children: "Batch Management" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 213,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: "Manage your student batches and track progress" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 214,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 212,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => navigate("/create-batch"),
            className: "btn-primary flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Plus, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 221,
                columnNumber: 13
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Create New Batch" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 222,
                columnNumber: 13
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 217,
            columnNumber: 11
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: loadData,
            className: "btn-outline flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RefreshCw, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 228,
                columnNumber: 13
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Refresh" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 229,
                columnNumber: 13
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 224,
            columnNumber: 11
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 216,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 211,
      columnNumber: 7
    }, globalThis),
    message.text && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `rounded-md p-4 ${message.type === "error" ? "bg-red-50" : "bg-green-50"}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: message.type === "error" ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5 text-red-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 240,
        columnNumber: 17
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-green-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 242,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 238,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: `text-sm ${message.type === "error" ? "text-red-800" : "text-green-800"}`, children: message.text }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 246,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 245,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 237,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 236,
      columnNumber: 9
    }, globalThis),
    statistics && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white overflow-hidden shadow rounded-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-5", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-8 w-8 text-blue-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 261,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 260,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-5 w-0 flex-1", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dl", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dt", { className: "text-sm font-medium text-gray-500 truncate", children: "Total Batches" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 265,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dd", { className: "text-lg font-medium text-gray-900", children: statistics.total_batches }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 266,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 264,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 263,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 259,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 258,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 257,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white overflow-hidden shadow rounded-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-5", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-8 w-8 text-green-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 277,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 276,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-5 w-0 flex-1", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dl", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dt", { className: "text-sm font-medium text-gray-500 truncate", children: "Active Batches" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 281,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dd", { className: "text-lg font-medium text-gray-900", children: statistics.active_batches }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 282,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 280,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 279,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 275,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 274,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 273,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white overflow-hidden shadow rounded-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-5", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(UserPlus, { className: "h-8 w-8 text-purple-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 293,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 292,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-5 w-0 flex-1", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dl", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dt", { className: "text-sm font-medium text-gray-500 truncate", children: "Total Students" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 297,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dd", { className: "text-lg font-medium text-gray-900", children: statistics.total_students }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 298,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 296,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 295,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 291,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 290,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 289,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white overflow-hidden shadow rounded-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-5", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Archive, { className: "h-8 w-8 text-gray-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 309,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 308,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-5 w-0 flex-1", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dl", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dt", { className: "text-sm font-medium text-gray-500 truncate", children: "Archived" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 313,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dd", { className: "text-lg font-medium text-gray-900", children: statistics.archived_batches }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 314,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 312,
          columnNumber: 19
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 311,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 307,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 306,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 305,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 256,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg p-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1 max-w-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Search, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 329,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              type: "text",
              placeholder: "Search batches...",
              className: "pl-10 input",
              value: searchTerm,
              onChange: (e) => setSearchTerm(e.target.value)
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 330,
              columnNumber: 15
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 328,
          columnNumber: 13
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 327,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setShowFilters(!showFilters),
            className: `btn-outline flex items-center space-x-2 ${showFilters ? "bg-blue-50 border-blue-300" : ""}`,
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Filter, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 346,
                columnNumber: 15
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Filters" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 347,
                columnNumber: 15
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 342,
            columnNumber: 13
          },
          globalThis
        ) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 341,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 325,
        columnNumber: 9
      }, globalThis),
      showFilters && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 pt-4 border-t border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Academic Year" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 357,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              className: "input",
              value: selectedAcademicYear,
              onChange: (e) => setSelectedAcademicYear(e.target.value),
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "all", children: "All Years" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 365,
                  columnNumber: 19
                }, globalThis),
                getUniqueAcademicYears().map((year) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: year, children: year }, year, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 367,
                  columnNumber: 21
                }, globalThis))
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 360,
              columnNumber: 17
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 356,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Course Type" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 373,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              className: "input",
              value: selectedCourseType,
              onChange: (e) => setSelectedCourseType(e.target.value),
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "all", children: "All Courses" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 381,
                  columnNumber: 19
                }, globalThis),
                getUniqueCourseTypes().map((type) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: type, children: type }, type, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 383,
                  columnNumber: 21
                }, globalThis))
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 376,
              columnNumber: 17
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 372,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Status" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 389,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              className: "input",
              value: selectedStatus,
              onChange: (e) => setSelectedStatus(e.target.value),
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "all", children: "All Status" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 397,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "active", children: "Active" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 398,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "archived", children: "Archived" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 399,
                  columnNumber: 19
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 392,
              columnNumber: 17
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 388,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Sort By" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 404,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              className: "input",
              value: sortBy,
              onChange: (e) => setSortBy(e.target.value),
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "created_at", children: "Created Date" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 412,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "name", children: "Batch Name" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 413,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "student_count", children: "Student Count" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 414,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "academic_year", children: "Academic Year" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                  lineNumber: 415,
                  columnNumber: 19
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 407,
              columnNumber: 17
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 403,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 355,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 354,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 324,
      columnNumber: 7
    }, globalThis),
    loading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-12", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 426,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-gray-500", children: "Loading batches..." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 427,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 425,
      columnNumber: 9
    }, globalThis) : filteredBatches.length > 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg overflow-hidden", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("table", { className: "min-w-full divide-y divide-gray-200", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("thead", { className: "bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "th",
          {
            className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",
            onClick: () => handleSort("name"),
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-1", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Batch Name" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 440,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowUpDown, { className: "h-3 w-3" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 441,
                columnNumber: 23
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 439,
              columnNumber: 21
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 435,
            columnNumber: 19
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Academic Year" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 444,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Course Type" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 447,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "th",
          {
            className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",
            onClick: () => handleSort("student_count"),
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-1", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Students" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 455,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowUpDown, { className: "h-3 w-3" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 456,
                columnNumber: 23
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 454,
              columnNumber: 21
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 450,
            columnNumber: 19
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "th",
          {
            className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",
            onClick: () => handleSort("created_at"),
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-1", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Created" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 464,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowUpDown, { className: "h-3 w-3" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 465,
                columnNumber: 23
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 463,
              columnNumber: 21
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 459,
            columnNumber: 19
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Status" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 468,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Actions" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 471,
          columnNumber: 19
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 434,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 433,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tbody", { className: "bg-white divide-y divide-gray-200", children: filteredBatches.map((batch) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { className: "hover:bg-gray-50", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-gray-900", children: batch.name }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 481,
            columnNumber: 25
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: batch.description || "No description" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 482,
            columnNumber: 25
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 480,
          columnNumber: 23
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 479,
          columnNumber: 21
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: batch.academic_year }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 485,
          columnNumber: 21
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: batch.course_type }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 488,
          columnNumber: 21
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-4 w-4 text-gray-400 mr-1" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
            lineNumber: 493,
            columnNumber: 25
          }, globalThis),
          batch.student_count
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 492,
          columnNumber: 23
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 491,
          columnNumber: 21
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: new Date(batch.created_at).toLocaleDateString() }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 497,
          columnNumber: 21
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${batch.is_active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}`, children: batch.is_active ? "Active" : "Archived" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 501,
          columnNumber: 23
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 500,
          columnNumber: 21
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-6 py-4 whitespace-nowrap text-sm font-medium", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: () => handleViewBatch(batch.id),
              className: "text-blue-600 hover:text-blue-900",
              title: "View Details",
              children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Eye, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 516,
                columnNumber: 27
              }, globalThis)
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 511,
              columnNumber: 25
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: () => handleEditBatch(batch.id),
              className: "text-green-600 hover:text-green-900",
              title: "Edit Batch",
              children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(PenSquare, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 523,
                columnNumber: 27
              }, globalThis)
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 518,
              columnNumber: 25
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: () => handleExportBatch(batch),
              className: "text-purple-600 hover:text-purple-900",
              title: "Export Students",
              children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Download, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 530,
                columnNumber: 27
              }, globalThis)
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 525,
              columnNumber: 25
            },
            globalThis
          ),
          batch.is_active ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: () => handleArchiveBatch(batch.id),
              className: "text-gray-600 hover:text-gray-900",
              title: "Archive Batch",
              children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Archive, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 538,
                columnNumber: 29
              }, globalThis)
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 533,
              columnNumber: 27
            },
            globalThis
          ) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: () => handleActivateBatch(batch.id),
              className: "text-green-600 hover:text-green-900",
              title: "Activate Batch",
              children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
                lineNumber: 546,
                columnNumber: 29
              }, globalThis)
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 541,
              columnNumber: 27
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 510,
          columnNumber: 23
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 509,
          columnNumber: 21
        }, globalThis)
      ] }, batch.id, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 478,
        columnNumber: 19
      }, globalThis)) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 476,
        columnNumber: 15
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 432,
      columnNumber: 13
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 431,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 430,
      columnNumber: 9
    }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-12 bg-white shadow rounded-lg", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 559,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No batches found" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 560,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: searchTerm || selectedAcademicYear !== "all" || selectedCourseType !== "all" || selectedStatus !== "all" ? "Try adjusting your search or filters." : "Get started by creating your first batch." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 561,
        columnNumber: 11
      }, globalThis),
      !searchTerm && selectedAcademicYear === "all" && selectedCourseType === "all" && selectedStatus === "all" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: () => navigate("/create-batch"),
          className: "btn-primary flex items-center space-x-2 mx-auto",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Plus, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 573,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Create First Batch" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
              lineNumber: 574,
              columnNumber: 17
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
          lineNumber: 569,
          columnNumber: 15
        },
        globalThis
      ) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
        lineNumber: 568,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
      lineNumber: 558,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/BatchManagement.jsx",
    lineNumber: 209,
    columnNumber: 5
  }, globalThis);
};

export { BatchManagement as default };
