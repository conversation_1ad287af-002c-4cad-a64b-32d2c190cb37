import{e as b}from"./chunk-c39a4323.js";const f=["Full Name","Student ID","Year of Birth","Email"],C=()=>{const r=f.join(","),e=["<PERSON>,STU001,2000,<EMAIL>","<PERSON>,STU002,1999,<EMAIL>","<PERSON>,STU003,2001,"];return r+`
`+e.join(`
`)},I=()=>{const r=C(),e=new Blob([r],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(n.download!==void 0){const t=URL.createObjectURL(e);n.setAttribute("href",t),n.setAttribute("download","student_upload_template.csv"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(t)}},g=r=>{try{const e=r.split(`
`).filter(i=>i.trim());if(e.length<2)throw new Error("CSV file must contain at least a header row and one data row");const n=e[0].split(",").map(i=>i.trim().replace(/"/g,"")),o=["Full Name","Student ID","Year of Birth"].filter(i=>!n.some(c=>c.toLowerCase()===i.toLowerCase()));if(o.length>0)throw new Error(`Missing required columns: ${o.join(", ")}`);const s=i=>n.findIndex(c=>c.toLowerCase()===i.toLowerCase()),d=s("Full Name"),a=s("Student ID"),u=s("Year of Birth"),h=s("Email"),p=[],m=[];for(let i=1;i<e.length;i++){const c=e[i].trim();if(c)try{const l=S(c);if(l.length<Math.max(d,a,u)+1){m.push({row:i+1,error:"Insufficient columns in row"});continue}const w={id:Date.now()+i,fullName:l[d]?.trim()||"",studentId:l[a]?.trim().toUpperCase()||"",yearOfBirth:l[u]?.trim()||"",email:h>=0&&l[h]?.trim()||"",password:"",rowNumber:i+1};p.push(w)}catch(l){m.push({row:i+1,error:l.message})}}return{students:p,errors:m,totalRows:e.length-1}}catch(e){throw new Error(`Failed to parse CSV: ${e.message}`)}},S=r=>{const e=[];let n="",t=!1;for(let o=0;o<r.length;o++){const s=r[o];s==='"'?t=!t:s===","&&!t?(e.push(n),n=""):n+=s}return e.push(n),e.map(o=>o.replace(/"/g,""))},x=r=>{const e=[];if(!r)return e.push("Please select a file"),e;r.type!=="text/csv"&&!r.name.toLowerCase().endsWith(".csv")&&e.push("File must be a CSV file");const n=5*1024*1024;return r.size>n&&e.push("File size must be less than 5MB"),r.size===0&&e.push("File cannot be empty"),e},U=r=>new Promise((e,n)=>{const t=new FileReader;t.onload=o=>{try{const s=o.target.result;e(s)}catch(s){n(new Error(`Failed to read file content: ${s.message}`))}},t.onerror=()=>{n(new Error("Failed to read file"))},t.readAsText(r)}),j=r=>{const e={validStudents:[],invalidStudents:[],duplicateIds:[]},n=r.map(o=>o.studentId).filter(o=>o),t=n.filter((o,s)=>n.indexOf(o)!==s);return r.forEach((o,s)=>{const d=b(o);Object.keys(d).length===0?e.validStudents.push(o):e.invalidStudents.push({...o,errors:d,index:s})}),e.duplicateIds=[...new Set(t)],e},v=r=>{const e=f.join(","),n=r.map(t=>{const o=Object.values(t.errors).flat().join("; ");return[t.fullName,t.studentId,t.yearOfBirth,t.email,`"Errors: ${o}"`].join(",")});return e+`,Errors
`+n.join(`
`)},L=r=>{const e=v(r),n=new Blob([e],{type:"text/csv;charset=utf-8;"}),t=document.createElement("a");if(t.download!==void 0){const o=URL.createObjectURL(n);t.setAttribute("href",o),t.setAttribute("download","student_upload_errors.csv"),t.style.visibility="hidden",document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(o)}},E=(r,e)=>{const n=new Set(r.map(s=>s.studentId.toUpperCase())),t=e.filter(s=>!n.has(s.studentId.toUpperCase())),o=e.filter(s=>n.has(s.studentId.toUpperCase()));return{mergedStudents:[...r,...t],newStudents:t,duplicates:o,totalAdded:t.length}},R=(r,e="students_export.csv")=>{const n=["Full Name","Student ID","Username","Email","Year of Birth","Status"],t=r.map(a=>[`${a.first_name} ${a.last_name}`,a.student_id,a.username,a.email,a.year_of_birth||"",a.is_active?"Active":"Inactive"]),o=n.join(",")+`
`+t.map(a=>a.map(u=>`"${u}"`).join(",")).join(`
`),s=new Blob([o],{type:"text/csv;charset=utf-8;"}),d=document.createElement("a");if(d.download!==void 0){const a=URL.createObjectURL(s);d.setAttribute("href",a),d.setAttribute("download",e),d.style.visibility="hidden",document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(a)}};export{L as a,j as b,I as d,R as e,E as m,g as p,U as r,x as v};
