import React from 'react';
import { useElectronAPI } from '../hooks/useElectronAPI';
import LoadingSpinner from './LoadingSpinner';
import { AlertCircle } from 'lucide-react';

const ElectronWrapper = ({ children }) => {
  const { isElectronReady, error, isElectron } = useElectronAPI();

  // Show loading while checking for Electron API
  if (!isElectronReady && !error) {
    return (
      <LoadingSpinner
        text="Initializing application..."
        size="large"
      />
    );
  }

  // Show error if Electron API is not available
  if (error || !isElectron) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
          <div className="flex items-center mb-4">
            <AlertCircle className="h-8 w-8 text-red-500 mr-3" />
            <h1 className="text-xl font-bold text-gray-900">Application Error</h1>
          </div>

          <div className="space-y-4">
            <p className="text-gray-600">
              {error || 'The Peer Review System requires the desktop application to function properly.'}
            </p>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <h3 className="text-sm font-medium text-yellow-800 mb-2">
                Troubleshooting Steps:
              </h3>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Ensure you're running the Electron desktop application</li>
                <li>• Try restarting the application</li>
                <li>• Check that all dependencies are properly installed</li>
                <li>• Restart your computer if the issue persists</li>
              </ul>
            </div>

            <button
              onClick={() => window.location.reload()}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render children when Electron is ready
  return children;
};

export default ElectronWrapper;
