import { u as useNavigate, r as reactExports, j as jsxDevRuntimeExports } from './chunk-2ef8e52b.js';
import { u as useAuth, a as useMessage } from './main-ef45ddde.js';
import { C as Chart, a as CategoryScale, L as LinearScale, B as BarElement, A as ArcElement, p as plugin_title, b as plugin_tooltip, c as plugin_legend } from './chunk-87c5b779.js';
import { c as Shield, B as Building, g as Settings, L as LogOut, d as BookOpen, U as Users, G as GraduationCap, C as CheckCircle, b as ArrowRight, n as Activity } from './chunk-0deb1b3d.js';
import './chunk-03d61bd9.js';

Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  plugin_title,
  plugin_tooltip,
  plugin_legend
);
const Dashboard = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { showMessage } = useMessage();
  useDatabase();
  const [stats, setStats] = reactExports.useState(null);
  const [loading, setLoading] = reactExports.useState(true);
  reactExports.useEffect(() => {
    loadDashboardData();
  }, []);
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const electronAPI = window.electronAPI;
      const dashboardStats = await electronAPI.dashboard.getStatistics(
        user.id,
        user.role,
        user.institution_id
      );
      setStats(dashboardStats);
    } catch {
      showMessage("error", "Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };
  const handleLogout = () => {
    if (window.confirm("Are you sure you want to logout?")) {
      logout();
      navigate("/login");
    }
  };
  const getGreeting = () => {
    const hour = (/* @__PURE__ */ new Date()).getHours();
    if (hour < 12) {
      return "Good morning";
    }
    if (hour < 18) {
      return "Good afternoon";
    }
    return "Good evening";
  };
  const navigationCards = [
    {
      title: "Assessment Creation",
      description: "Create and manage peer review assessments",
      icon: BookOpen,
      path: "/assessments",
      color: "blue",
      roles: ["super_admin"]
    },
    {
      title: "Take Assessment",
      description: "Participate in assessments and manage student responses",
      icon: GraduationCap,
      path: "/test",
      color: "green",
      roles: ["teacher", "student"]
    },
    {
      title: "Admin Panel",
      description: "Manage users, batches, and system settings",
      icon: Shield,
      path: "/admin",
      color: "purple",
      roles: ["admin", "super_admin"]
    },
    {
      title: "Institution",
      description: "Manage institution information and settings",
      icon: Building,
      path: "/institution",
      color: "orange",
      roles: ["admin", "super_admin"]
    },
    {
      title: "Settings",
      description: "Configure your preferences and view help",
      icon: Settings,
      path: "/settings",
      color: "gray",
      roles: ["teacher", "student", "admin", "super_admin"]
    }
  ];
  const StatCard = ({ title, value, icon: Icon, color = "blue", loading: loading2 = false }) => {
    const colorClasses = {
      blue: "bg-blue-50 text-blue-600",
      green: "bg-green-50 text-green-600",
      purple: "bg-purple-50 text-purple-600",
      orange: "bg-orange-50 text-orange-600",
      gray: "bg-gray-50 text-gray-600"
    };
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white overflow-hidden shadow rounded-lg", "data-testid": "stats-card", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-5", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `p-3 rounded-md ${colorClasses[color]}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Icon, { className: "h-6 w-6" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 141,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 140,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 139,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-5 w-0 flex-1", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dl", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dt", { className: "text-sm font-medium text-gray-500 truncate", children: title }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 146,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("dd", { className: "text-2xl font-bold text-gray-900", children: loading2 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-pulse bg-gray-200 h-8 w-16 rounded" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 151,
          columnNumber: 21
        }, globalThis) : value }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 149,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 145,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 144,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
      lineNumber: 138,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
      lineNumber: 137,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
      lineNumber: 136,
      columnNumber: 7
    }, globalThis);
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-h-screen bg-gray-50", "data-testid": "dashboard", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center py-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-2xl font-bold text-gray-900", "data-testid": "welcome-message", children: [
          getGreeting(),
          ", ",
          user?.first_name,
          "!"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 173,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Welcome to the Peer Review Assessment System" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 176,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 172,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-4", children: [
        (user.role === "admin" || user.role === "super_admin") && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => navigate("/admin"),
            className: "btn-outline flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Shield, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                lineNumber: 188,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Admin Panel" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                lineNumber: 189,
                columnNumber: 19
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 184,
            columnNumber: 17
          },
          globalThis
        ),
        (user.role === "admin" || user.role === "super_admin") && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => navigate("/institution"),
            className: "btn-outline flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Building, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                lineNumber: 198,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Institution" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                lineNumber: 199,
                columnNumber: 19
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 194,
            columnNumber: 17
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => navigate("/settings"),
            className: "btn-outline flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Settings, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                lineNumber: 207,
                columnNumber: 17
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Settings" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                lineNumber: 208,
                columnNumber: 17
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 203,
            columnNumber: 15
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: handleLogout,
            className: "btn-outline text-red-600 border-red-600 hover:bg-red-50 flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LogOut, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                lineNumber: 215,
                columnNumber: 17
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Logout" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                lineNumber: 216,
                columnNumber: 17
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 211,
            columnNumber: 15
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 182,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
      lineNumber: 171,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
      lineNumber: 170,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
      lineNumber: 169,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8", children: [
      stats && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8", children: [
        user.role === "teacher" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            StatCard,
            {
              title: "My Assessments",
              value: stats.assessments?.total_assessments || 0,
              icon: BookOpen,
              color: "blue",
              loading
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 230,
              columnNumber: 17
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            StatCard,
            {
              title: "My Batches",
              value: stats.batches?.total_batches || 0,
              icon: Users,
              color: "green",
              loading
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 237,
              columnNumber: 17
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            StatCard,
            {
              title: "Total Students",
              value: stats.students?.total_students || 0,
              icon: GraduationCap,
              color: "purple",
              loading
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 244,
              columnNumber: 17
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            StatCard,
            {
              title: "Processed",
              value: stats.assessments?.processed_assessments || 0,
              icon: CheckCircle,
              color: "orange",
              loading
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 251,
              columnNumber: 17
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 229,
          columnNumber: 15
        }, globalThis),
        (user.role === "admin" || user.role === "super_admin") && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            StatCard,
            {
              title: "Total Users",
              value: stats.users?.total_users || 0,
              icon: Users,
              color: "blue",
              loading
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 263,
              columnNumber: 17
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            StatCard,
            {
              title: "Teachers",
              value: stats.users?.total_teachers || 0,
              icon: Shield,
              color: "green",
              loading
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 270,
              columnNumber: 17
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            StatCard,
            {
              title: "Students",
              value: stats.users?.total_students || 0,
              icon: GraduationCap,
              color: "purple",
              loading
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 277,
              columnNumber: 17
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            StatCard,
            {
              title: "Assessments",
              value: stats.assessments?.total_assessments || 0,
              icon: BookOpen,
              color: "orange",
              loading
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 284,
              columnNumber: 17
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 262,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 227,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-8", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-lg font-medium text-gray-900 mb-4", children: "Quick Actions" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 298,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3", children: navigationCards.filter((card) => card.roles.includes(user.role)).map((card) => {
          const Icon = card.icon;
          return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: () => navigate(card.path),
              className: "bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow text-left group",
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `p-3 rounded-md ${card.color === "blue" ? "bg-blue-50 text-blue-600" : card.color === "green" ? "bg-green-50 text-green-600" : card.color === "purple" ? "bg-purple-50 text-purple-600" : card.color === "orange" ? "bg-orange-50 text-orange-600" : "bg-gray-50 text-gray-600"}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Icon, { className: "h-6 w-6" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                    lineNumber: 318,
                    columnNumber: 25
                  }, globalThis) }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                    lineNumber: 311,
                    columnNumber: 23
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowRight, { className: "h-4 w-4 text-gray-400 ml-auto group-hover:text-gray-600 transition-colors" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                    lineNumber: 320,
                    columnNumber: 23
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                  lineNumber: 310,
                  columnNumber: 21
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900", children: card.title }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                    lineNumber: 323,
                    columnNumber: 23
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-gray-500", children: card.description }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                    lineNumber: 324,
                    columnNumber: 23
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                  lineNumber: 322,
                  columnNumber: 21
                }, globalThis)
              ]
            },
            card.path,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 305,
              columnNumber: 19
            },
            globalThis
          );
        }) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 299,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 297,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-4 py-5 sm:p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "Recent Activity" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 335,
          columnNumber: 13
        }, globalThis),
        loading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 338,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-sm text-gray-500", children: "Loading recent activity..." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 339,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 337,
          columnNumber: 15
        }, globalThis) : stats?.recentActivity?.length > 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flow-root", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "-mb-8", children: stats.recentActivity.map((activity, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative pb-8", children: [
          index !== stats.recentActivity.length - 1 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 348,
            columnNumber: 27
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative flex space-x-3", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Activity, { className: "h-4 w-4 text-white" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 353,
              columnNumber: 31
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 352,
              columnNumber: 29
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 351,
              columnNumber: 27
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-w-0 flex-1 pt-1.5 flex justify-between space-x-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-500", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-900", children: activity.action }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                  lineNumber: 359,
                  columnNumber: 33
                }, globalThis),
                " ",
                "on ",
                activity.table_name
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                lineNumber: 358,
                columnNumber: 31
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-400", children: new Date(activity.timestamp).toLocaleString() }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
                lineNumber: 364,
                columnNumber: 31
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 357,
              columnNumber: 29
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
              lineNumber: 356,
              columnNumber: 27
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 350,
            columnNumber: 25
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 346,
          columnNumber: 23
        }, globalThis) }, activity.id || index, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 345,
          columnNumber: 21
        }, globalThis)) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 343,
          columnNumber: 17
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 342,
          columnNumber: 15
        }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-6", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Activity, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 377,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No recent activity" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 378,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "Get started by using the system features above." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
            lineNumber: 379,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
          lineNumber: 376,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 334,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
        lineNumber: 333,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
      lineNumber: 224,
      columnNumber: 7
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Dashboard.jsx",
    lineNumber: 167,
    columnNumber: 5
  }, globalThis);
};

export { Dashboard as default };
