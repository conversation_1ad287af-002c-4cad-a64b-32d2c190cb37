import { getElectronAPI } from '../hooks/useElectronAPI';
import { logger } from './logger';

/**
 * Configuration management system for user preferences and application settings
 */
class ConfigManager {
  constructor() {
    this.electronAPI = getElectronAPI();
    this.cache = new Map();
    this.listeners = new Map();
    this.defaultConfig = this.getDefaultConfig();
    this.isInitialized = false;
  }

  /**
   * Default configuration values
   */
  getDefaultConfig() {
    return {
      // Application settings
      app: {
        theme: 'light',
        language: 'en',
        autoSave: true,
        autoSaveInterval: 30000, // 30 seconds
        confirmDestructiveActions: true,
        showWelcomeScreen: true,
        enableAnalytics: true,
        enableCrashReporting: true
      },

      // User interface settings
      ui: {
        sidebarCollapsed: false,
        tablePageSize: 25,
        showTooltips: true,
        animationsEnabled: true,
        compactMode: false,
        fontSize: 'medium', // small, medium, large
        highContrast: false
      },

      // File and path settings
      paths: {
        defaultExportPath: '',
        backupPath: '',
        logPath: '',
        tempPath: ''
      },

      // PDF generation settings
      pdf: {
        template: 'default',
        includeCharts: true,
        includeStatistics: true,
        paperSize: 'A4',
        orientation: 'portrait',
        margins: {
          top: 20,
          right: 20,
          bottom: 20,
          left: 20
        }
      },

      // Database settings
      database: {
        backupInterval: 24 * 60 * 60 * 1000, // 24 hours
        autoBackup: true,
        maxBackups: 10,
        compressionEnabled: true
      },

      // Security settings
      security: {
        sessionTimeout: 30 * 60 * 1000, // 30 minutes
        passwordComplexity: 'medium',
        twoFactorEnabled: false,
        auditLogging: true,
        encryptBackups: true
      },

      // Offline application - no update settings needed

      // Notification settings
      notifications: {
        enabled: true,
        position: 'top-right',
        duration: 5000,
        sound: false,
        showProgress: true
      }
    };
  }

  /**
   * Initialize configuration manager
   */
  async initialize() {
    try {
      if (this.electronAPI?.config) {
        const storedConfig = await this.electronAPI.config.getAll();
        this.mergeConfig(storedConfig);
      } else {
        // Fallback to localStorage for development
        const storedConfig = this.getFromLocalStorage();
        this.mergeConfig(storedConfig);
      }

      this.isInitialized = true;
      logger.info('Configuration manager initialized');
    } catch (error) {
      logger.error('Failed to initialize configuration manager', error);
      this.isInitialized = true; // Use defaults
    }
  }

  /**
   * Merge stored configuration with defaults
   */
  mergeConfig(storedConfig) {
    const merged = this.deepMerge(this.defaultConfig, storedConfig || {});

    // Cache all configuration values
    Object.entries(merged).forEach(([section, values]) => {
      Object.entries(values).forEach(([key, value]) => {
        this.cache.set(`${section}.${key}`, value);
      });
    });
  }

  /**
   * Deep merge two objects
   */
  deepMerge(target, source) {
    const result = { ...target };

    Object.keys(source).forEach(key => {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    });

    return result;
  }

  /**
   * Get configuration value
   */
  async get(key, defaultValue = null) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (this.cache.has(key)) {
      return this.cache.get(key);
    }

    // Try to get from persistent storage
    try {
      if (this.electronAPI?.config) {
        const value = await this.electronAPI.config.get(key);
        if (value !== undefined) {
          this.cache.set(key, value);
          return value;
        }
      }
    } catch (error) {
      logger.warn('Failed to get config value from storage', { key, error: error.message });
    }

    return defaultValue;
  }

  /**
   * Set configuration value
   */
  async set(key, value) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Update cache
      this.cache.set(key, value);

      // Persist to storage
      if (this.electronAPI?.config) {
        await this.electronAPI.config.set(key, value);
      } else {
        this.saveToLocalStorage();
      }

      // Notify listeners
      this.notifyListeners(key, value);

      logger.debug('Configuration value updated', { key, value });
    } catch (error) {
      logger.error('Failed to set configuration value', error, { key, value });
      throw error;
    }
  }

  /**
   * Get entire configuration section
   */
  async getSection(section) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const sectionConfig = {};
    const prefix = `${section}.`;

    for (const [key, value] of this.cache.entries()) {
      if (key.startsWith(prefix)) {
        const subKey = key.substring(prefix.length);
        sectionConfig[subKey] = value;
      }
    }

    return sectionConfig;
  }

  /**
   * Update entire configuration section
   */
  async setSection(section, values) {
    const promises = Object.entries(values).map(([key, value]) =>
      this.set(`${section}.${key}`, value)
    );

    await Promise.all(promises);
  }

  /**
   * Reset configuration to defaults
   */
  async reset(section = null) {
    try {
      if (section) {
        // Reset specific section
        const defaultValues = this.defaultConfig[section] || {};
        await this.setSection(section, defaultValues);
      } else {
        // Reset all configuration
        this.cache.clear();

        if (this.electronAPI?.config) {
          await this.electronAPI.config.clear();
        } else {
          localStorage.removeItem('peer_review_config');
        }

        this.mergeConfig({});
      }

      logger.info('Configuration reset', { section });
    } catch (error) {
      logger.error('Failed to reset configuration', error, { section });
      throw error;
    }
  }

  /**
   * Export configuration
   */
  async export() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const config = {};

    for (const [key, value] of this.cache.entries()) {
      const parts = key.split('.');
      if (parts.length === 2) {
        const [section, subKey] = parts;
        if (!config[section]) {config[section] = {};}
        config[section][subKey] = value;
      }
    }

    return {
      version: '1.0',
      timestamp: new Date().toISOString(),
      config
    };
  }

  /**
   * Import configuration
   */
  async import(configData) {
    try {
      if (!configData.config) {
        throw new Error('Invalid configuration format');
      }

      // Validate and merge with defaults
      const validatedConfig = this.deepMerge(this.defaultConfig, configData.config);

      // Clear current configuration
      this.cache.clear();

      // Apply imported configuration
      this.mergeConfig(validatedConfig);

      // Persist to storage
      if (this.electronAPI?.config) {
        await this.electronAPI.config.setAll(validatedConfig);
      } else {
        this.saveToLocalStorage();
      }

      logger.info('Configuration imported successfully');
    } catch (error) {
      logger.error('Failed to import configuration', error);
      throw error;
    }
  }

  /**
   * Add configuration change listener
   */
  addListener(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    this.listeners.get(key).add(callback);
  }

  /**
   * Remove configuration change listener
   */
  removeListener(key, callback) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).delete(callback);
    }
  }

  /**
   * Notify listeners of configuration changes
   */
  notifyListeners(key, value) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).forEach(callback => {
        try {
          callback(value, key);
        } catch (error) {
          logger.error('Error in configuration listener', error, { key });
        }
      });
    }
  }

  /**
   * Fallback to localStorage for development
   */
  getFromLocalStorage() {
    try {
      const stored = localStorage.getItem('peer_review_config');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      logger.warn('Failed to load config from localStorage', { error: error.message });
      return {};
    }
  }

  /**
   * Save to localStorage as fallback
   */
  saveToLocalStorage() {
    try {
      const config = {};

      for (const [key, value] of this.cache.entries()) {
        const parts = key.split('.');
        if (parts.length === 2) {
          const [section, subKey] = parts;
          if (!config[section]) {config[section] = {};}
          config[section][subKey] = value;
        }
      }

      localStorage.setItem('peer_review_config', JSON.stringify(config));
    } catch (error) {
      logger.warn('Failed to save config to localStorage', { error: error.message });
    }
  }

  /**
   * Get all configuration values
   */
  async getAll() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    return this.export();
  }
}

// Create singleton instance
export const configManager = new ConfigManager();

// Export convenience functions
export const getConfig = (key, defaultValue) => configManager.get(key, defaultValue);
export const setConfig = (key, value) => configManager.set(key, value);
export const getConfigSection = (section) => configManager.getSection(section);
export const setConfigSection = (section, values) => configManager.setSection(section, values);
export const resetConfig = (section) => configManager.reset(section);
export const exportConfig = () => configManager.export();
export const importConfig = (configData) => configManager.import(configData);
export const addConfigListener = (key, callback) => configManager.addListener(key, callback);
export const removeConfigListener = (key, callback) => configManager.removeListener(key, callback);

export default configManager;
