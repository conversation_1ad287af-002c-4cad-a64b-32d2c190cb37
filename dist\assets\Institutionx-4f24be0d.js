import { u as useNavigate, r as reactExports, j as jsxDevRuntimeExports } from './chunk-2ef8e52b.js';
import { u as useAuth, a as useMessage } from './main-56ca96d9.js';
import { Z as ArrowLeft, B as Building, x as Save, aj as PenLine, L as LogOut, A as AlertCircle, i as Mail, P as Phone, v as MapPin, w as Calendar, j as Globe, a8 as Award } from './chunk-028772a4.js';
import './chunk-03d61bd9.js';

const Institution = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { showMessage } = useMessage();
  const [loading, setLoading] = reactExports.useState(false);
  const [saving, setSaving] = reactExports.useState(false);
  const [editing, setEditing] = reactExports.useState(false);
  const [institutionData, setInstitutionData] = reactExports.useState({
    name: "",
    details: {
      logoPath: "",
      description: "",
      primaryEmail: "",
      contactNumber: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      postalCode: "",
      country: "",
      establishmentDate: "",
      accreditationDetails: "",
      websiteUrl: ""
    }
  });
  const [formData, setFormData] = reactExports.useState({});
  const [errors, setErrors] = reactExports.useState({});
  reactExports.useEffect(() => {
    if (user.role !== "admin" && user.role !== "super_admin") {
      showMessage("error", "Access denied. You do not have permission to access this page.");
      navigate("/dashboard");
      return;
    }
    loadInstitutionData();
  }, [user, navigate]);
  const loadInstitutionData = async () => {
    try {
      setLoading(true);
      const electronAPI = window.electronAPI;
      const data = await electronAPI.institution.getDetails(user.institution_id);
      setInstitutionData(data);
      setFormData({
        name: data.name || "",
        ...data.details
      });
    } catch (error) {
      showMessage("error", `Failed to load institution data: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  const validateForm = () => {
    const newErrors = {};
    if (!formData.name?.trim()) {
      newErrors.name = "Institution name is required";
    }
    if (formData.primaryEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.primaryEmail)) {
      newErrors.primaryEmail = "Please enter a valid email address";
    }
    if (formData.contactNumber && !/^[\+]?[1-9][\d]{0,15}$/.test(formData.contactNumber.replace(/\s/g, ""))) {
      newErrors.contactNumber = "Please enter a valid phone number";
    }
    if (formData.websiteUrl && !/^https?:\/\/.+\..+/.test(formData.websiteUrl)) {
      newErrors.websiteUrl = "Please enter a valid website URL (include http:// or https://)";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleSave = async () => {
    if (!validateForm()) {
      showMessage("error", "Please correct the errors before saving");
      return;
    }
    if (window.confirm("Are you sure you want to save these changes?")) {
      try {
        setSaving(true);
        const electronAPI = window.electronAPI;
        const institutionUpdate = {
          name: formData.name
        };
        const detailsUpdate = {
          logoPath: formData.logoPath,
          description: formData.description,
          primaryEmail: formData.primaryEmail,
          contactNumber: formData.contactNumber,
          addressLine1: formData.addressLine1,
          addressLine2: formData.addressLine2,
          city: formData.city,
          state: formData.state,
          postalCode: formData.postalCode,
          country: formData.country,
          establishmentDate: formData.establishmentDate,
          accreditationDetails: formData.accreditationDetails,
          websiteUrl: formData.websiteUrl
        };
        await electronAPI.institution.updateDetails(
          user.institution_id,
          institutionUpdate,
          detailsUpdate,
          user.id
        );
        showMessage("success", "Institution details updated successfully");
        setEditing(false);
        loadInstitutionData();
      } catch (error) {
        showMessage("error", `Failed to save changes: ${error.message}`);
      } finally {
        setSaving(false);
      }
    }
  };
  const handleCancel = () => {
    setFormData({
      name: institutionData.name || "",
      ...institutionData.details
    });
    setErrors({});
    setEditing(false);
  };
  const handleLogout = () => {
    if (window.confirm("Are you sure you want to logout?")) {
      logout();
      navigate("/login");
    }
  };
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };
  if (loading) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-h-screen bg-gray-50 flex items-center justify-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
        lineNumber: 179,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-4 text-gray-600", children: "Loading institution data..." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
        lineNumber: 180,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
      lineNumber: 178,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
      lineNumber: 177,
      columnNumber: 7
    }, globalThis);
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-h-screen bg-gray-50", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center py-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => navigate(-1),
            className: "btn-outline mr-4 flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowLeft, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 197,
                columnNumber: 17
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Go Back" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 198,
                columnNumber: 17
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 193,
            columnNumber: 15
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Building, { className: "h-8 w-8 text-blue-600 mr-3" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 201,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-2xl font-bold text-gray-900", children: "Institution Management" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 203,
              columnNumber: 19
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Manage your institution's information and settings" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 204,
              columnNumber: 19
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 202,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
          lineNumber: 200,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
        lineNumber: 192,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
        editing ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: handleCancel,
              className: "btn-outline",
              disabled: saving,
              children: "Cancel"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 212,
              columnNumber: 19
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: handleSave,
              disabled: saving,
              className: "btn-primary flex items-center space-x-2",
              children: saving ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 226,
                  columnNumber: 25
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Saving..." }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 227,
                  columnNumber: 25
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 225,
                columnNumber: 23
              }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Save, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 231,
                  columnNumber: 25
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Save Changes" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 232,
                  columnNumber: 25
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 230,
                columnNumber: 23
              }, globalThis)
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 219,
              columnNumber: 19
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
          lineNumber: 211,
          columnNumber: 17
        }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setEditing(true),
            className: "btn-primary flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(PenLine, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 242,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Edit" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 243,
                columnNumber: 19
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 238,
            columnNumber: 17
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: handleLogout,
            className: "btn-outline text-red-600 border-red-600 hover:bg-red-50 flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(LogOut, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 251,
                columnNumber: 17
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Logout" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 252,
                columnNumber: 17
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 247,
            columnNumber: 15
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
        lineNumber: 209,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
      lineNumber: 191,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
      lineNumber: 190,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
      lineNumber: 189,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-6 py-4 border-b border-gray-200", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-lg font-medium text-gray-900", children: "Institution Information" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
          lineNumber: 263,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600", children: "Update your institution's details and contact information" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
          lineNumber: 264,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
        lineNumber: 262,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6 space-y-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-md font-medium text-gray-900 mb-4", children: "Basic Information" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 270,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 gap-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: [
                "Institution Name ",
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-red-500", children: "*" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 274,
                  columnNumber: 38
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 273,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "text",
                  value: formData.name || "",
                  onChange: (e) => handleInputChange("name", e.target.value),
                  disabled: !editing,
                  className: `input ${errors.name ? "border-red-500" : ""}`,
                  placeholder: "Enter institution name"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 276,
                  columnNumber: 19
                },
                globalThis
              ),
              errors.name && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 286,
                  columnNumber: 23
                }, globalThis),
                errors.name
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 285,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 272,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Description" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 293,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "textarea",
                {
                  value: formData.description || "",
                  onChange: (e) => handleInputChange("description", e.target.value),
                  disabled: !editing,
                  rows: 3,
                  className: "input",
                  placeholder: "Enter institution description",
                  maxLength: 500
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 296,
                  columnNumber: 19
                },
                globalThis
              ),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-xs text-gray-500", children: [
                (formData.description || "").length,
                "/500 characters"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 305,
                columnNumber: 19
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 292,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 271,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
          lineNumber: 269,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-md font-medium text-gray-900 mb-4", children: "Contact Information" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 314,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Mail, { className: "h-4 w-4 inline mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 318,
                  columnNumber: 21
                }, globalThis),
                "Primary Email"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 317,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "email",
                  value: formData.primaryEmail || "",
                  onChange: (e) => handleInputChange("primaryEmail", e.target.value),
                  disabled: !editing,
                  className: `input ${errors.primaryEmail ? "border-red-500" : ""}`,
                  placeholder: "Enter primary email"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 321,
                  columnNumber: 19
                },
                globalThis
              ),
              errors.primaryEmail && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 331,
                  columnNumber: 23
                }, globalThis),
                errors.primaryEmail
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 330,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 316,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Phone, { className: "h-4 w-4 inline mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 339,
                  columnNumber: 21
                }, globalThis),
                "Contact Number"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 338,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "tel",
                  value: formData.contactNumber || "",
                  onChange: (e) => handleInputChange("contactNumber", e.target.value),
                  disabled: !editing,
                  className: `input ${errors.contactNumber ? "border-red-500" : ""}`,
                  placeholder: "Enter contact number"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 342,
                  columnNumber: 19
                },
                globalThis
              ),
              errors.contactNumber && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 352,
                  columnNumber: 23
                }, globalThis),
                errors.contactNumber
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 351,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 337,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 315,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
          lineNumber: 313,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-md font-medium text-gray-900 mb-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(MapPin, { className: "h-4 w-4 inline mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 363,
              columnNumber: 17
            }, globalThis),
            "Address Information"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 362,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 gap-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Address Line 1" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 368,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "text",
                  value: formData.addressLine1 || "",
                  onChange: (e) => handleInputChange("addressLine1", e.target.value),
                  disabled: !editing,
                  className: "input",
                  placeholder: "Enter address line 1"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 371,
                  columnNumber: 19
                },
                globalThis
              )
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 367,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Address Line 2" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 382,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "text",
                  value: formData.addressLine2 || "",
                  onChange: (e) => handleInputChange("addressLine2", e.target.value),
                  disabled: !editing,
                  className: "input",
                  placeholder: "Enter address line 2 (optional)"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 385,
                  columnNumber: 19
                },
                globalThis
              )
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 381,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "City" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 397,
                  columnNumber: 21
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "input",
                  {
                    type: "text",
                    value: formData.city || "",
                    onChange: (e) => handleInputChange("city", e.target.value),
                    disabled: !editing,
                    className: "input",
                    placeholder: "Enter city"
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                    lineNumber: 400,
                    columnNumber: 21
                  },
                  globalThis
                )
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 396,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "State/Province" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 411,
                  columnNumber: 21
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "input",
                  {
                    type: "text",
                    value: formData.state || "",
                    onChange: (e) => handleInputChange("state", e.target.value),
                    disabled: !editing,
                    className: "input",
                    placeholder: "Enter state"
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                    lineNumber: 414,
                    columnNumber: 21
                  },
                  globalThis
                )
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 410,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Postal Code" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 425,
                  columnNumber: 21
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "input",
                  {
                    type: "text",
                    value: formData.postalCode || "",
                    onChange: (e) => handleInputChange("postalCode", e.target.value),
                    disabled: !editing,
                    className: "input",
                    placeholder: "Enter postal code"
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                    lineNumber: 428,
                    columnNumber: 21
                  },
                  globalThis
                )
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 424,
                columnNumber: 19
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 395,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Country" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 440,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "text",
                  value: formData.country || "",
                  onChange: (e) => handleInputChange("country", e.target.value),
                  disabled: !editing,
                  className: "input",
                  placeholder: "Enter country"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 443,
                  columnNumber: 19
                },
                globalThis
              )
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 439,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 366,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
          lineNumber: 361,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-md font-medium text-gray-900 mb-4", children: "Additional Information" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 457,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Calendar, { className: "h-4 w-4 inline mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 461,
                  columnNumber: 21
                }, globalThis),
                "Establishment Date"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 460,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "date",
                  value: formData.establishmentDate || "",
                  onChange: (e) => handleInputChange("establishmentDate", e.target.value),
                  disabled: !editing,
                  className: "input"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 464,
                  columnNumber: 19
                },
                globalThis
              )
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 459,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Globe, { className: "h-4 w-4 inline mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 475,
                  columnNumber: 21
                }, globalThis),
                "Website URL"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 474,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  type: "url",
                  value: formData.websiteUrl || "",
                  onChange: (e) => handleInputChange("websiteUrl", e.target.value),
                  disabled: !editing,
                  className: `input ${errors.websiteUrl ? "border-red-500" : ""}`,
                  placeholder: "https://www.example.com"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 478,
                  columnNumber: 19
                },
                globalThis
              ),
              errors.websiteUrl && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                  lineNumber: 488,
                  columnNumber: 23
                }, globalThis),
                errors.websiteUrl
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 487,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 473,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 458,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Award, { className: "h-4 w-4 inline mr-1" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 497,
                columnNumber: 19
              }, globalThis),
              "Accreditation Details"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 496,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "textarea",
              {
                value: formData.accreditationDetails || "",
                onChange: (e) => handleInputChange("accreditationDetails", e.target.value),
                disabled: !editing,
                rows: 3,
                className: "input",
                placeholder: "Enter accreditation details, certifications, etc.",
                maxLength: 1e3
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
                lineNumber: 500,
                columnNumber: 17
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-xs text-gray-500", children: [
              (formData.accreditationDetails || "").length,
              "/1000 characters"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
              lineNumber: 509,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
            lineNumber: 495,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
          lineNumber: 456,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
        lineNumber: 267,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
      lineNumber: 261,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
      lineNumber: 260,
      columnNumber: 7
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/Institution.jsx",
    lineNumber: 187,
    columnNumber: 5
  }, globalThis);
};

export { Institution as default };
