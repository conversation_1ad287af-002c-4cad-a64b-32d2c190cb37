import{j as e,r as u,u as he}from"./chunk-81a058b1.js";import{b as Ne,u as xe}from"./main-272222cd.js";import{s as M,d as fe,h as ye}from"./chunk-c39a4323.js";import{F as J,w as ve,d as Ee,Y as ge,I as je,b as oe,e as W,a as le,C as O,t as re,W as te,U,R as ie,T as ne,o as ce,k as ue,Z as be,X as Ae,_ as Ce,$ as we,a0 as Te,H as Se,A as Pe,a1 as Ve}from"./chunk-0b87a8e8.js";import{d as De,a as Le,v as me,r as Be,p as Ie,b as Fe,m as ke}from"./chunk-e7a6d730.js";import"./chunk-81a949b4.js";const $e=({batchData:m,errors:l,onChange:x,onNext:w,canProceed:T})=>{const v=new Date().getFullYear(),h=()=>{const r=[];for(let f=-2;f<=5;f++){const E=v+f,i=E+1;r.push(`${E}-${i}`)}return r},b=["Course 01","Course 02","Course 03","Course 04"],g=h();return e.jsxDEV("div",{className:"p-6",children:[e.jsxDEV("div",{className:"mb-6",children:[e.jsxDEV("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[e.jsxDEV(J,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:44,columnNumber:11},globalThis),"Batch Information"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:43,columnNumber:9},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Enter the basic information for your new batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:47,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:42,columnNumber:7},globalThis),e.jsxDEV("form",{className:"space-y-6","data-testid":"create-batch-form",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Batch Name *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:55,columnNumber:11},globalThis),e.jsxDEV("input",{type:"text",value:m.name,onChange:r=>x("name",r.target.value),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${l.name?"border-red-500":"border-gray-300"}`,placeholder:"Enter batch name (e.g., CS-2024-A, Engineering Batch 1)",maxLength:50,"data-testid":"batch-name-input"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:58,columnNumber:11},globalThis),l.name&&e.jsxDEV("p",{className:"text-red-500 text-sm mt-1",children:l.name[0]},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:70,columnNumber:13},globalThis),e.jsxDEV("p",{className:"text-xs text-gray-500 mt-1",children:"3-50 characters, letters, numbers, and spaces only"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:72,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:54,columnNumber:9},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2 flex items-center",children:[e.jsxDEV(ve,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:82,columnNumber:15},globalThis),"Academic Year *"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:81,columnNumber:13},globalThis),e.jsxDEV("select",{value:m.academicYear,onChange:r=>x("academicYear",r.target.value),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${l.academicYear?"border-red-500":"border-gray-300"}`,children:[e.jsxDEV("option",{value:"",children:"Select academic year"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:92,columnNumber:15},globalThis),g.map(r=>e.jsxDEV("option",{value:r,children:r},r,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:94,columnNumber:17},globalThis))]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:85,columnNumber:13},globalThis),l.academicYear&&e.jsxDEV("p",{className:"text-red-500 text-sm mt-1",children:l.academicYear[0]},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:98,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:80,columnNumber:11},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2 flex items-center",children:[e.jsxDEV(Ee,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:105,columnNumber:15},globalThis),"Course Type *"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:104,columnNumber:13},globalThis),e.jsxDEV("select",{value:m.courseType,onChange:r=>x("courseType",r.target.value),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${l.courseType?"border-red-500":"border-gray-300"}`,children:[e.jsxDEV("option",{value:"",children:"Select course type"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:115,columnNumber:15},globalThis),b.map(r=>e.jsxDEV("option",{value:r,children:r},r,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:117,columnNumber:17},globalThis))]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:108,columnNumber:13},globalThis),l.courseType&&e.jsxDEV("p",{className:"text-red-500 text-sm mt-1",children:l.courseType[0]},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:121,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:103,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:78,columnNumber:9},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2 flex items-center",children:[e.jsxDEV(ge,{className:"h-4 w-4 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:129,columnNumber:13},globalThis),"Description (Optional)"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:128,columnNumber:11},globalThis),e.jsxDEV("textarea",{value:m.description,onChange:r=>x("description",r.target.value),rows:3,className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${l.description?"border-red-500":"border-gray-300"}`,placeholder:"Brief description of the batch (optional)",maxLength:200},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:132,columnNumber:11},globalThis),l.description&&e.jsxDEV("p",{className:"text-red-500 text-sm mt-1",children:l.description[0]},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:143,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex justify-between items-center mt-1",children:[e.jsxDEV("p",{className:"text-xs text-gray-500",children:"Provide additional context about this batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:146,columnNumber:13},globalThis),e.jsxDEV("p",{className:"text-xs text-gray-500",children:[m.description.length,"/200 characters"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:149,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:145,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:127,columnNumber:9},globalThis),e.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(je,{className:"h-5 w-5 text-blue-600 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:158,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:"Batch Creation Guidelines"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:160,columnNumber:15},globalThis),e.jsxDEV("ul",{className:"text-sm text-blue-800 space-y-1",children:[e.jsxDEV("li",{children:"• Batch names should be unique within your teaching scope"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:164,columnNumber:17},globalThis),e.jsxDEV("li",{children:"• Academic year format: YYYY-YYYY (e.g., 2024-2025)"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:165,columnNumber:17},globalThis),e.jsxDEV("li",{children:"• You can add 1-50 students per batch in the next step"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:166,columnNumber:17},globalThis),e.jsxDEV("li",{children:"• Student accounts will be automatically created"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:167,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:163,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:159,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:157,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:156,columnNumber:9},globalThis),Object.keys(l).length>0&&e.jsxDEV("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:[e.jsxDEV("h4",{className:"text-sm font-medium text-red-900 mb-2",children:"Please fix the following errors:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:176,columnNumber:13},globalThis),e.jsxDEV("ul",{className:"text-sm text-red-800 space-y-1",children:Object.entries(l).map(([r,f])=>f&&f.map((E,i)=>e.jsxDEV("li",{children:["• ",E]},`${r}-${i}`,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:182,columnNumber:19},globalThis)))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:179,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:175,columnNumber:11},globalThis),e.jsxDEV("div",{className:"flex justify-end pt-6 border-t border-gray-200",children:e.jsxDEV("button",{type:"button",onClick:w,disabled:!T,className:`flex items-center space-x-2 px-6 py-2 rounded-md font-medium ${T?"bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,"data-testid":"create-batch-button",children:[e.jsxDEV("span",{children:"Next: Add Students"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:202,columnNumber:13},globalThis),e.jsxDEV(oe,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:203,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:191,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:190,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:52,columnNumber:7},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",lineNumber:41,columnNumber:5},globalThis)},Ye=({studentsData:m,errors:l,onChange:x,onGeneratePassword:w,onPrevious:T,onNext:v,canProceed:h,loading:b})=>{const[g,r]=u.useState({}),[f,E]=u.useState(!1),[i,o]=u.useState(!1),[y,N]=u.useState(null),[c,V]=u.useState(null),[F,S]=u.useState(!1),[k,A]=u.useState([]),[j,$]=u.useState(null),D=u.useRef(null),Y=new Date().getFullYear(),_=(()=>{const t=[];for(let s=Y-100;s<=Y-10;s++)t.push(s);return t.reverse()})(),B=()=>{const t={id:Date.now(),fullName:"",studentId:"",password:"",yearOfBirth:"",email:""};x([...m,t])},H=t=>{const s=m.filter((a,C)=>C!==t);x(s)},P=(t,s,a)=>{const C=[...m];C[t]={...C[t],[s]:a},x(C)},q=async t=>{try{const s=await w();s&&P(t,"password",s)}catch{}},X=t=>{r(s=>({...s,[t]:!s[t]}))},L=()=>{x([])},Z=async()=>{for(let t=0;t<m.length;t++)await q(t)},K=t=>{const s=t.target.files[0];if(!s)return;const a=me(s);if(a.length>0){A(a);return}N(s),A([]),z(s)},Q=t=>{t.preventDefault();const s=t.dataTransfer.files[0];if(!s)return;const a=me(s);if(a.length>0){A(a);return}N(s),A([]),z(s)},ee=t=>{t.preventDefault()},z=async t=>{try{S(!0),A([]);const s=await Be(t),a=Ie(s);a.errors.length>0&&A(a.errors.map(G=>`Row ${G.row}: ${G.error}`));const C=Fe(a.students),pe=await Promise.all(C.validStudents.map(async G=>{const de=await w();return{...G,password:de||"TempPass123!"}}));V({validStudents:pe,invalidStudents:C.invalidStudents,duplicateIds:C.duplicateIds,totalRows:a.totalRows})}catch(s){A([s.message])}finally{S(!1)}},se=()=>{if(!c||c.validStudents.length===0)return;const t=ke(m,c.validStudents);x(t.mergedStudents),$({totalProcessed:c.totalRows,imported:t.totalAdded,duplicates:t.duplicates.length,errors:c.invalidStudents.length}),N(null),V(null),o(!1)},I=()=>{N(null),V(null),A([]),S(!1),$(null),D.current&&(D.current.value="")},R=()=>{I(),o(!1)},d=(t,s)=>l.students[t]&&l.students[t][s]?l.students[t][s][0]:null,n=t=>l.students[t]&&Object.keys(l.students[t]).length>0,p=l.students.filter(t=>t&&Object.keys(t).length>0).length;return e.jsxDEV("div",{className:"p-6",children:[e.jsxDEV("div",{className:"mb-6",children:e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("div",{children:[e.jsxDEV("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[e.jsxDEV(W,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:251,columnNumber:15},globalThis),"Add Students"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:250,columnNumber:13},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Add students to your batch (1-50 students)"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:254,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:249,columnNumber:11},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-2",children:e.jsxDEV("span",{className:`text-sm font-medium ${m.length>50?"text-red-600":m.length===0?"text-gray-500":"text-green-600"}`,children:[m.length,"/50 students"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:260,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:259,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:248,columnNumber:9},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:247,columnNumber:7},globalThis),(l.batch.students||l.batch.duplicateStudentIds||l.batch.duplicateInstitutionIds)&&e.jsxDEV("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-md",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(le,{className:"h-5 w-5 text-red-600 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:274,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-red-900 mb-1",children:"Batch Validation Errors"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:276,columnNumber:15},globalThis),e.jsxDEV("ul",{className:"text-sm text-red-800 space-y-1",children:[l.batch.students&&l.batch.students.map((t,s)=>e.jsxDEV("li",{children:["• ",t]},s,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:279,columnNumber:19},globalThis)),l.batch.duplicateStudentIds&&l.batch.duplicateStudentIds.map((t,s)=>e.jsxDEV("li",{children:["• ",t]},s,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:282,columnNumber:19},globalThis)),l.batch.duplicateInstitutionIds&&l.batch.duplicateInstitutionIds.map((t,s)=>e.jsxDEV("li",{children:["• ",t]},s,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:285,columnNumber:19},globalThis))]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:277,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:275,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:273,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:272,columnNumber:9},globalThis),j&&e.jsxDEV("div",{className:"mb-4 p-4 bg-green-50 border border-green-200 rounded-md",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(O,{className:"h-5 w-5 text-green-600 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:297,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-green-900 mb-1",children:"CSV Import Completed"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:299,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-green-800",children:["Successfully imported ",j.imported," students from ",j.totalProcessed," rows.",j.duplicates>0&&` ${j.duplicates} duplicates skipped.`,j.errors>0&&` ${j.errors} rows had errors.`]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:300,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:298,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:296,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:295,columnNumber:9},globalThis),e.jsxDEV("div",{className:"mb-4 flex flex-wrap items-center gap-3",children:[e.jsxDEV("button",{onClick:B,disabled:m.length>=50,className:"btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsxDEV(W,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:317,columnNumber:11},globalThis),e.jsxDEV("span",{children:"Add Student"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:318,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:312,columnNumber:9},globalThis),e.jsxDEV("button",{onClick:()=>o(!0),disabled:m.length>=50,className:"btn-outline flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsxDEV(re,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:326,columnNumber:11},globalThis),e.jsxDEV("span",{children:"Import CSV"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:327,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:321,columnNumber:9},globalThis),e.jsxDEV("button",{onClick:De,className:"btn-outline flex items-center space-x-2 text-sm",children:[e.jsxDEV(te,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:334,columnNumber:11},globalThis),e.jsxDEV("span",{children:"Download Template"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:335,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:330,columnNumber:9},globalThis),m.length>0&&e.jsxDEV(e.Fragment,{children:[e.jsxDEV("button",{onClick:()=>E(!f),className:"btn-outline flex items-center space-x-2",children:[e.jsxDEV(U,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:344,columnNumber:15},globalThis),e.jsxDEV("span",{children:"Bulk Actions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:345,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:340,columnNumber:13},globalThis),f&&e.jsxDEV("div",{className:"flex items-center space-x-2",children:[e.jsxDEV("button",{onClick:Z,className:"btn-outline flex items-center space-x-2 text-sm",children:[e.jsxDEV(ie,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:354,columnNumber:19},globalThis),e.jsxDEV("span",{children:"Generate All Passwords"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:355,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:350,columnNumber:17},globalThis),e.jsxDEV("button",{onClick:L,className:"btn-outline text-red-600 border-red-300 hover:bg-red-50 flex items-center space-x-2 text-sm",children:[e.jsxDEV(ne,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:362,columnNumber:19},globalThis),e.jsxDEV("span",{children:"Clear All"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:363,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:358,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:349,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:339,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:311,columnNumber:7},globalThis),m.length>0?e.jsxDEV("div",{className:"overflow-x-auto",children:e.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxDEV("thead",{className:"bg-gray-50",children:e.jsxDEV("tr",{children:[e.jsxDEV("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"#"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:377,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Full Name *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:380,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student ID *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:383,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Password *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:386,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Year of Birth *"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:389,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:392,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:395,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:376,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:375,columnNumber:13},globalThis),e.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map((t,s)=>e.jsxDEV("tr",{className:n(s)?"bg-red-50":"",children:[e.jsxDEV("td",{className:"px-3 py-4 whitespace-nowrap text-sm text-gray-900",children:s+1},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:403,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-3 py-4 whitespace-nowrap",children:[e.jsxDEV("input",{type:"text",value:t.fullName,onChange:a=>P(s,"fullName",a.target.value),className:`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${d(s,"fullName")?"border-red-500":"border-gray-300"}`,placeholder:"John Doe",maxLength:50},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:409,columnNumber:21},globalThis),d(s,"fullName")&&e.jsxDEV("p",{className:"text-red-500 text-xs mt-1",children:d(s,"fullName")},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:420,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:408,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-3 py-4 whitespace-nowrap",children:[e.jsxDEV("input",{type:"text",value:t.studentId,onChange:a=>P(s,"studentId",a.target.value.toUpperCase()),className:`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${d(s,"studentId")?"border-red-500":"border-gray-300"}`,placeholder:"STU001",maxLength:20},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:426,columnNumber:21},globalThis),d(s,"studentId")&&e.jsxDEV("p",{className:"text-red-500 text-xs mt-1",children:d(s,"studentId")},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:437,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:425,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-3 py-4 whitespace-nowrap",children:[e.jsxDEV("div",{className:"flex items-center space-x-1",children:[e.jsxDEV("input",{type:g[s]?"text":"password",value:t.password,onChange:a=>P(s,"password",a.target.value),className:`flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${d(s,"password")?"border-red-500":"border-gray-300"}`,placeholder:"Password"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:444,columnNumber:23},globalThis),e.jsxDEV("button",{type:"button",onClick:()=>X(s),className:"p-1 text-gray-400 hover:text-gray-600",children:g[s]?e.jsxDEV(ce,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:458,columnNumber:49},globalThis):e.jsxDEV(ue,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:458,columnNumber:82},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:453,columnNumber:23},globalThis),e.jsxDEV("button",{type:"button",onClick:()=>q(s),className:"p-1 text-blue-600 hover:text-blue-800",title:"Generate Password",children:e.jsxDEV(ie,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:466,columnNumber:25},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:460,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:443,columnNumber:21},globalThis),d(s,"password")&&e.jsxDEV("p",{className:"text-red-500 text-xs mt-1",children:d(s,"password")},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:470,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:442,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-3 py-4 whitespace-nowrap",children:[e.jsxDEV("select",{value:t.yearOfBirth,onChange:a=>P(s,"yearOfBirth",a.target.value),className:`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${d(s,"yearOfBirth")?"border-red-500":"border-gray-300"}`,children:[e.jsxDEV("option",{value:"",children:"Year"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:483,columnNumber:23},globalThis),_.map(a=>e.jsxDEV("option",{value:a,children:a},a,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:485,columnNumber:25},globalThis))]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:476,columnNumber:21},globalThis),d(s,"yearOfBirth")&&e.jsxDEV("p",{className:"text-red-500 text-xs mt-1",children:d(s,"yearOfBirth")},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:489,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:475,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-3 py-4 whitespace-nowrap",children:[e.jsxDEV("input",{type:"email",value:t.email,onChange:a=>P(s,"email",a.target.value),className:`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${d(s,"email")?"border-red-500":"border-gray-300"}`,placeholder:"<EMAIL>"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:495,columnNumber:21},globalThis),d(s,"email")&&e.jsxDEV("p",{className:"text-red-500 text-xs mt-1",children:d(s,"email")},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:505,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:494,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-3 py-4 whitespace-nowrap",children:e.jsxDEV("button",{onClick:()=>H(s),className:"text-red-600 hover:text-red-900 p-1",title:"Remove Student",children:e.jsxDEV(ne,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:516,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:511,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:510,columnNumber:19},globalThis)]},t.id||s,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:402,columnNumber:17},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:400,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:374,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:373,columnNumber:9},globalThis):e.jsxDEV("div",{className:"text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",children:[e.jsxDEV(U,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:526,columnNumber:11},globalThis),e.jsxDEV("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No students added"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:527,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by adding your first student to the batch."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:528,columnNumber:11},globalThis),e.jsxDEV("div",{className:"mt-6",children:e.jsxDEV("button",{onClick:B,className:"btn-primary flex items-center space-x-2 mx-auto",children:[e.jsxDEV(W,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:536,columnNumber:15},globalThis),e.jsxDEV("span",{children:"Add First Student"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:537,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:532,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:531,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:525,columnNumber:9},globalThis),p>0&&e.jsxDEV("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-md",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(le,{className:"h-5 w-5 text-red-600 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:547,columnNumber:13},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-red-900 mb-1",children:[p," student",p>1?"s have":" has"," validation errors"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:549,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-red-800",children:"Please fix the highlighted errors before proceeding."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:552,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:548,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:546,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:545,columnNumber:9},globalThis),e.jsxDEV("div",{className:"flex justify-between pt-6 border-t border-gray-200 mt-6",children:[e.jsxDEV("button",{onClick:T,className:"flex items-center space-x-2 px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsxDEV(be,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:566,columnNumber:11},globalThis),e.jsxDEV("span",{children:"Previous: Batch Info"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:567,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:562,columnNumber:9},globalThis),e.jsxDEV("button",{onClick:v,disabled:!h||b,className:`flex items-center space-x-2 px-6 py-2 rounded-md font-medium ${h&&!b?"bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:[e.jsxDEV("span",{children:b?"Creating...":"Create Batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:579,columnNumber:11},globalThis),!b&&e.jsxDEV(oe,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:580,columnNumber:24},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:570,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:561,columnNumber:7},globalThis),i&&e.jsxDEV("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxDEV("div",{className:"relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white",children:[e.jsxDEV("div",{className:"flex justify-between items-center mb-4",children:[e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.jsxDEV(re,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:590,columnNumber:17},globalThis),"Import Students from CSV"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:589,columnNumber:15},globalThis),e.jsxDEV("button",{onClick:R,className:"text-gray-400 hover:text-gray-600",children:e.jsxDEV(Ae,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:597,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:593,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:588,columnNumber:13},globalThis),!y&&e.jsxDEV("div",{className:"mb-6",children:[e.jsxDEV("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors",onDrop:Q,onDragOver:ee,children:[e.jsxDEV(J,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:609,columnNumber:19},globalThis),e.jsxDEV("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Upload CSV File"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:610,columnNumber:19},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mb-4",children:"Drag and drop your CSV file here, or click to browse"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:613,columnNumber:19},globalThis),e.jsxDEV("input",{ref:D,type:"file",accept:".csv",onChange:K,className:"hidden"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:616,columnNumber:19},globalThis),e.jsxDEV("button",{onClick:()=>D.current?.click(),className:"btn-primary",children:"Choose CSV File"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:623,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:604,columnNumber:17},globalThis),e.jsxDEV("div",{className:"mt-4 text-sm text-gray-600",children:[e.jsxDEV("p",{className:"font-medium mb-2",children:"CSV Format Requirements:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:632,columnNumber:19},globalThis),e.jsxDEV("ul",{className:"list-disc list-inside space-y-1",children:[e.jsxDEV("li",{children:"Required columns: Full Name, Student ID, Year of Birth"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:634,columnNumber:21},globalThis),e.jsxDEV("li",{children:"Optional column: Email"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:635,columnNumber:21},globalThis),e.jsxDEV("li",{children:"Maximum 50 students per file"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:636,columnNumber:21},globalThis),e.jsxDEV("li",{children:"File size limit: 5MB"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:637,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:633,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:631,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:603,columnNumber:15},globalThis),k.length>0&&e.jsxDEV("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-md",children:e.jsxDEV("div",{className:"flex",children:[e.jsxDEV(Ce,{className:"h-5 w-5 text-red-600 mt-0.5 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:647,columnNumber:19},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"text-sm font-medium text-red-900 mb-1",children:"File Errors"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:649,columnNumber:21},globalThis),e.jsxDEV("ul",{className:"text-sm text-red-800 space-y-1",children:k.map((t,s)=>e.jsxDEV("li",{children:["• ",t]},s,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:652,columnNumber:25},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:650,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:648,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:646,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:645,columnNumber:15},globalThis),F&&e.jsxDEV("div",{className:"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md",children:e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:664,columnNumber:19},globalThis),e.jsxDEV("span",{className:"text-sm text-blue-800",children:"Processing CSV file..."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:665,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:663,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:662,columnNumber:15},globalThis),c&&e.jsxDEV("div",{className:"mb-6",children:[e.jsxDEV("h4",{className:"text-md font-medium text-gray-900 mb-4",children:"Import Preview"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:673,columnNumber:17},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[e.jsxDEV("div",{className:"bg-green-50 p-3 rounded-md",children:[e.jsxDEV("div",{className:"text-sm font-medium text-green-900",children:"Valid Students"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:678,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-lg font-bold text-green-600",children:c.validStudents.length},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:679,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:677,columnNumber:19},globalThis),e.jsxDEV("div",{className:"bg-red-50 p-3 rounded-md",children:[e.jsxDEV("div",{className:"text-sm font-medium text-red-900",children:"Invalid Rows"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:682,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-lg font-bold text-red-600",children:c.invalidStudents.length},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:683,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:681,columnNumber:19},globalThis),e.jsxDEV("div",{className:"bg-yellow-50 p-3 rounded-md",children:[e.jsxDEV("div",{className:"text-sm font-medium text-yellow-900",children:"Duplicate IDs"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:686,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-lg font-bold text-yellow-600",children:c.duplicateIds.length},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:687,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:685,columnNumber:19},globalThis),e.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-md",children:[e.jsxDEV("div",{className:"text-sm font-medium text-blue-900",children:"Total Rows"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:690,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-lg font-bold text-blue-600",children:c.totalRows},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:691,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:689,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:676,columnNumber:17},globalThis),c.validStudents.length>0&&e.jsxDEV("div",{className:"mb-4",children:[e.jsxDEV("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:["Valid Students (",c.validStudents.length,")"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:698,columnNumber:21},globalThis),e.jsxDEV("div",{className:"max-h-40 overflow-y-auto border border-gray-200 rounded-md",children:[e.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxDEV("thead",{className:"bg-gray-50",children:e.jsxDEV("tr",{children:[e.jsxDEV("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Name"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:705,columnNumber:29},globalThis),e.jsxDEV("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Student ID"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:706,columnNumber:29},globalThis),e.jsxDEV("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Year"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:707,columnNumber:29},globalThis),e.jsxDEV("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Email"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:708,columnNumber:29},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:704,columnNumber:27},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:703,columnNumber:25},globalThis),e.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:c.validStudents.slice(0,10).map((t,s)=>e.jsxDEV("tr",{children:[e.jsxDEV("td",{className:"px-3 py-2 text-sm text-gray-900",children:t.fullName},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:714,columnNumber:31},globalThis),e.jsxDEV("td",{className:"px-3 py-2 text-sm text-gray-900",children:t.studentId},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:715,columnNumber:31},globalThis),e.jsxDEV("td",{className:"px-3 py-2 text-sm text-gray-900",children:t.yearOfBirth},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:716,columnNumber:31},globalThis),e.jsxDEV("td",{className:"px-3 py-2 text-sm text-gray-900",children:t.email||"-"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:717,columnNumber:31},globalThis)]},s,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:713,columnNumber:29},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:711,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:702,columnNumber:23},globalThis),c.validStudents.length>10&&e.jsxDEV("div",{className:"px-3 py-2 text-xs text-gray-500 bg-gray-50",children:["... and ",c.validStudents.length-10," more students"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:723,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:701,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:697,columnNumber:19},globalThis),c.invalidStudents.length>0&&e.jsxDEV("div",{className:"mb-4",children:[e.jsxDEV("div",{className:"flex items-center justify-between mb-2",children:[e.jsxDEV("h5",{className:"text-sm font-medium text-red-900",children:["Invalid Rows (",c.invalidStudents.length,")"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:735,columnNumber:23},globalThis),e.jsxDEV("button",{onClick:()=>Le(c.invalidStudents),className:"text-xs text-red-600 hover:text-red-800 flex items-center space-x-1",children:[e.jsxDEV(te,{className:"h-3 w-3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:742,columnNumber:25},globalThis),e.jsxDEV("span",{children:"Download Errors"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:743,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:738,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:734,columnNumber:21},globalThis),e.jsxDEV("div",{className:"max-h-32 overflow-y-auto border border-red-200 rounded-md bg-red-50",children:e.jsxDEV("div",{className:"p-3 text-sm text-red-800",children:c.invalidStudents.map((t,s)=>e.jsxDEV("div",{className:"mb-2",children:[e.jsxDEV("strong",{children:["Row ",t.rowNumber,":"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:750,columnNumber:29},globalThis)," ",t.fullName||"Unknown"," -",Object.values(t.errors).flat().join(", ")]},s,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:749,columnNumber:27},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:747,columnNumber:23},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:746,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:733,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:672,columnNumber:15},globalThis),e.jsxDEV("div",{className:"flex justify-end space-x-3",children:[e.jsxDEV("button",{onClick:R,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:763,columnNumber:15},globalThis),y&&!c&&e.jsxDEV("button",{onClick:I,className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700",children:"Reset"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:771,columnNumber:17},globalThis),c&&c.validStudents.length>0&&e.jsxDEV("button",{onClick:se,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:["Import ",c.validStudents.length," Students"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:780,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:762,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:587,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:586,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",lineNumber:246,columnNumber:5},globalThis)},Re=({batchData:m,studentsData:l,creationResult:x,onCreateAnother:w,onGoToDashboard:T})=>{const[v,h]=u.useState(!1),[b,g]=u.useState(null),r=async(i,o=null)=>{try{await navigator.clipboard.writeText(i),g(o),setTimeout(()=>g(null),2e3)}catch{}},f=()=>{let i=`Batch: ${m.name}
`;return i+=`Academic Year: ${m.academicYear}
`,i+=`Course Type: ${m.courseType}

`,i+=`Student Login Credentials:
`,i+=`${"=".repeat(50)}

`,l.forEach((o,y)=>{i+=`${y+1}. ${o.fullName}
`,i+=`   Student ID: ${o.studentId}
`,i+=`   Username: ${o.studentId.toLowerCase()}
`,i+=`   Password: ${o.password}
`,o.email&&(i+=`   Email: ${o.email}
`),i+=`
`}),i+=`
Note: Students must change their password on first login.
`,i+=`Generated on: ${new Date().toLocaleString()}
`,i},E=()=>{const i=f(),o=new Blob([i],{type:"text/plain"}),y=URL.createObjectURL(o),N=document.createElement("a");N.href=y,N.download=`${m.name.replace(/\s+/g,"_")}_credentials.txt`,document.body.appendChild(N),N.click(),document.body.removeChild(N),URL.revokeObjectURL(y)};return e.jsxDEV("div",{className:"p-6",children:[e.jsxDEV("div",{className:"text-center mb-8",children:[e.jsxDEV("div",{className:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4",children:e.jsxDEV(O,{className:"h-8 w-8 text-green-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:78,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:77,columnNumber:9},globalThis),e.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900",children:"Batch Created Successfully!"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:80,columnNumber:9},globalThis),e.jsxDEV("p",{className:"text-gray-600 mt-2",children:['Your batch "',m.name,'" has been created with ',l.length," students"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:81,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:76,columnNumber:7},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxDEV("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsxDEV(J,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:91,columnNumber:13},globalThis),"Batch Information"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:90,columnNumber:11},globalThis),e.jsxDEV("div",{className:"space-y-3",children:[e.jsxDEV("div",{className:"flex justify-between",children:[e.jsxDEV("span",{className:"text-gray-600",children:"Batch Name:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:96,columnNumber:15},globalThis),e.jsxDEV("span",{className:"font-medium",children:m.name},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:97,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:95,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex justify-between",children:[e.jsxDEV("span",{className:"text-gray-600",children:"Academic Year:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:100,columnNumber:15},globalThis),e.jsxDEV("span",{className:"font-medium",children:m.academicYear},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:101,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:99,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex justify-between",children:[e.jsxDEV("span",{className:"text-gray-600",children:"Course Type:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:104,columnNumber:15},globalThis),e.jsxDEV("span",{className:"font-medium",children:m.courseType},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:105,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:103,columnNumber:13},globalThis),m.description&&e.jsxDEV("div",{children:[e.jsxDEV("span",{className:"text-gray-600",children:"Description:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:109,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-800 mt-1",children:m.description},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:110,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:108,columnNumber:15},globalThis),e.jsxDEV("div",{className:"flex justify-between",children:[e.jsxDEV("span",{className:"text-gray-600",children:"Batch ID:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:114,columnNumber:15},globalThis),e.jsxDEV("span",{className:"font-mono text-sm",children:x.batch.id},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:115,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:113,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex justify-between",children:[e.jsxDEV("span",{className:"text-gray-600",children:"Created:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:118,columnNumber:15},globalThis),e.jsxDEV("span",{className:"text-sm",children:new Date().toLocaleString()},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:119,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:117,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:94,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:89,columnNumber:9},globalThis),e.jsxDEV("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsxDEV(U,{className:"h-5 w-5 mr-2 text-green-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:127,columnNumber:13},globalThis),"Student Statistics"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:126,columnNumber:11},globalThis),e.jsxDEV("div",{className:"space-y-3",children:[e.jsxDEV("div",{className:"flex justify-between",children:[e.jsxDEV("span",{className:"text-gray-600",children:"Total Students:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:132,columnNumber:15},globalThis),e.jsxDEV("span",{className:"font-medium text-green-600",children:l.length},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:133,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:131,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex justify-between",children:[e.jsxDEV("span",{className:"text-gray-600",children:"Accounts Created:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:136,columnNumber:15},globalThis),e.jsxDEV("span",{className:"font-medium text-green-600",children:x.students.length},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:137,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:135,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex justify-between",children:[e.jsxDEV("span",{className:"text-gray-600",children:"With Email:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:140,columnNumber:15},globalThis),e.jsxDEV("span",{className:"font-medium",children:l.filter(i=>i.email).length},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:141,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:139,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex justify-between",children:[e.jsxDEV("span",{className:"text-gray-600",children:"Password Reset Required:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:144,columnNumber:15},globalThis),e.jsxDEV("span",{className:"font-medium text-orange-600",children:"All Students"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:145,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:143,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:130,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:125,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:87,columnNumber:7},globalThis),e.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg p-6 mb-6",children:[e.jsxDEV("div",{className:"flex items-center justify-between mb-4",children:[e.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsxDEV(U,{className:"h-5 w-5 mr-2 text-blue-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:155,columnNumber:13},globalThis),"Student Login Credentials"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:154,columnNumber:11},globalThis),e.jsxDEV("div",{className:"flex items-center space-x-2",children:[e.jsxDEV("button",{onClick:()=>h(!v),className:"btn-outline flex items-center space-x-2 text-sm",children:[v?e.jsxDEV(ce,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:163,columnNumber:32},globalThis):e.jsxDEV(ue,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:163,columnNumber:65},globalThis),e.jsxDEV("span",{children:[v?"Hide":"Show"," Passwords"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:164,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:159,columnNumber:13},globalThis),e.jsxDEV("button",{onClick:E,className:"btn-primary flex items-center space-x-2 text-sm",children:[e.jsxDEV(te,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:170,columnNumber:15},globalThis),e.jsxDEV("span",{children:"Download"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:171,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:166,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:158,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:153,columnNumber:9},globalThis),e.jsxDEV("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4",children:e.jsxDEV("div",{className:"flex",children:e.jsxDEV("div",{className:"ml-3",children:[e.jsxDEV("h4",{className:"text-sm font-medium text-yellow-900",children:"Important Security Notice"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:179,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-sm text-yellow-800 mt-1",children:"All students will be required to change their password on first login. Please share these credentials securely with your students."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:180,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:178,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:177,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:176,columnNumber:9},globalThis),e.jsxDEV("div",{className:"overflow-x-auto",children:e.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxDEV("thead",{className:"bg-gray-50",children:e.jsxDEV("tr",{children:[e.jsxDEV("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:192,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student ID"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:195,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Username"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:198,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Password"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:201,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:204,columnNumber:17},globalThis),e.jsxDEV("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:207,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:191,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:190,columnNumber:13},globalThis),e.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:l.map((i,o)=>e.jsxDEV("tr",{className:"hover:bg-gray-50",children:[e.jsxDEV("td",{className:"px-4 py-4 whitespace-nowrap",children:[e.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:i.fullName},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:216,columnNumber:21},globalThis),e.jsxDEV("div",{className:"text-sm text-gray-500",children:["Born: ",i.yearOfBirth]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:217,columnNumber:21},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:215,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-4 py-4 whitespace-nowrap",children:e.jsxDEV("div",{className:"text-sm font-mono text-gray-900",children:i.studentId},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:220,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:219,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-4 py-4 whitespace-nowrap",children:e.jsxDEV("div",{className:"text-sm font-mono text-gray-900",children:i.studentId.toLowerCase()},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:223,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:222,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-4 py-4 whitespace-nowrap",children:e.jsxDEV("div",{className:"flex items-center space-x-2",children:[e.jsxDEV("span",{className:`text-sm font-mono ${v?"text-gray-900":"text-gray-400"}`,children:v?i.password:"••••••••"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:227,columnNumber:23},globalThis),v&&e.jsxDEV("button",{onClick:()=>r(i.password,o),className:"text-gray-400 hover:text-gray-600",title:"Copy password",children:b===o?e.jsxDEV(O,{className:"h-4 w-4 text-green-600"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:237,columnNumber:29},globalThis):e.jsxDEV(we,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:239,columnNumber:29},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:231,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:226,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:225,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-4 py-4 whitespace-nowrap",children:e.jsxDEV("div",{className:"text-sm text-gray-900",children:i.email||"-"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:246,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:245,columnNumber:19},globalThis),e.jsxDEV("td",{className:"px-4 py-4 whitespace-nowrap",children:e.jsxDEV("button",{onClick:()=>r(`Username: ${i.studentId.toLowerCase()}
Password: ${i.password}`,`creds-${o}`),className:"text-blue-600 hover:text-blue-900 text-sm",title:"Copy credentials",children:b===`creds-${o}`?"Copied!":"Copy"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:249,columnNumber:21},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:248,columnNumber:19},globalThis)]},o,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:214,columnNumber:17},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:212,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:189,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:188,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:152,columnNumber:7},globalThis),e.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[e.jsxDEV("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:"Next Steps"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:266,columnNumber:9},globalThis),e.jsxDEV("ul",{className:"text-sm text-blue-800 space-y-2",children:[e.jsxDEV("li",{className:"flex items-start",children:[e.jsxDEV("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5",children:"1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:269,columnNumber:13},globalThis),e.jsxDEV("span",{children:"Download and securely share the login credentials with your students"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:270,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:268,columnNumber:11},globalThis),e.jsxDEV("li",{className:"flex items-start",children:[e.jsxDEV("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5",children:"2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:273,columnNumber:13},globalThis),e.jsxDEV("span",{children:"Inform students that they must change their password on first login"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:274,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:272,columnNumber:11},globalThis),e.jsxDEV("li",{className:"flex items-start",children:[e.jsxDEV("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5",children:"3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:277,columnNumber:13},globalThis),e.jsxDEV("span",{children:"Create assessments and assignments for your new batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:278,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:276,columnNumber:11},globalThis),e.jsxDEV("li",{className:"flex items-start",children:[e.jsxDEV("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5",children:"4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:281,columnNumber:13},globalThis),e.jsxDEV("span",{children:"Monitor student activity and engagement through the dashboard"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:282,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:280,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:267,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:265,columnNumber:7},globalThis),e.jsxDEV("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsxDEV("button",{onClick:w,className:"btn-outline flex items-center justify-center space-x-2 px-6 py-3",children:[e.jsxDEV(Te,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:293,columnNumber:11},globalThis),e.jsxDEV("span",{children:"Create Another Batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:294,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:289,columnNumber:9},globalThis),e.jsxDEV("button",{onClick:T,className:"btn-primary flex items-center justify-center space-x-2 px-6 py-3",children:[e.jsxDEV(Se,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:301,columnNumber:11},globalThis),e.jsxDEV("span",{children:"Go to Dashboard"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:302,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:297,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:288,columnNumber:7},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",lineNumber:74,columnNumber:5},globalThis)},Me=()=>{const m=he(),{createBatchWithStudents:l,checkBatchNameUnique:x,checkStudentIdsUnique:w,generateSecurePassword:T,getInstitution:v}=Ne(),{user:h}=xe(),[b,g]=u.useState({name:"",academicYear:"",courseType:"",description:""}),[r,f]=u.useState([]),[E,i]=u.useState(null),[o,y]=u.useState(1),[N,c]=u.useState(!1),[V,F]=u.useState({batch:{},students:[]}),[S,k]=u.useState({type:"",text:""}),[A,j]=u.useState(!1),[$,D]=u.useState(null),[Y,ae]=u.useState(null),[_,B]=u.useState(null);u.useEffect(()=>{if(!h||!["teacher","admin","super_admin"].includes(h.role)){m("/dashboard");return}H(),P()},[h,m]);const H=async()=>{try{const n=await v();i(n)}catch{L("error","Failed to load institution data")}},P=()=>{try{const n=localStorage.getItem("createBatch_draft"),p=localStorage.getItem("createBatch_students");n&&g(JSON.parse(n)),p&&f(JSON.parse(p));const t=localStorage.getItem("createBatch_lastSaved");t&&B(new Date(t))}catch{}},q=()=>{try{localStorage.setItem("createBatch_draft",JSON.stringify(b)),localStorage.setItem("createBatch_students",JSON.stringify(r)),localStorage.setItem("createBatch_lastSaved",new Date().toISOString()),B(new Date)}catch{}},X=()=>{try{localStorage.removeItem("createBatch_draft"),localStorage.removeItem("createBatch_students"),localStorage.removeItem("createBatch_lastSaved"),B(null)}catch{}};u.useEffect(()=>{Y&&clearTimeout(Y);const n=setTimeout(()=>{(b.name||r.length>0)&&q()},2e3);return ae(n),()=>{n&&clearTimeout(n)}},[b,r]);const L=(n,p)=>{k({type:n,text:p}),setTimeout(()=>k({type:"",text:""}),5e3)},Z=(n,p)=>{g(t=>({...t,[n]:p})),V.batch[n]&&F(t=>({...t,batch:{...t.batch,[n]:void 0}}))},K=n=>{f(n),F(p=>({...p,students:[]}))},Q=async()=>{const n=M(b),p=r.map(s=>M(s)),t=fe(n,p);if(n.name&&h)try{await x(n.name,h.id)||(t.batch.name=["A batch with this name already exists"])}catch{}if(p.length>0&&E)try{const s=p.map(a=>a.studentId).filter(a=>a);if(s.length>0){const a=await w(s,E.id);a.unique||(t.batch.duplicateInstitutionIds=[`Student IDs already exist in institution: ${a.duplicates.join(", ")}`])}}catch{}return F(t),!ye(t.batch)&&t.students.every(s=>!s||Object.keys(s).length===0)},ee=async()=>{if(!await Q()){L("error","Please fix the validation errors before creating the batch");return}j(!0)},z=async()=>{try{c(!0),j(!1);const n=M(b),p=r.map(a=>M(a)),t={...n,teacherId:h.id,institutionId:E.id},s=await l(t,p,h.id);D(s),X(),L("success",`Batch "${n.name}" created successfully with ${p.length} students`),y(3)}catch{L("error","Failed to create batch")}finally{c(!1)}},se=async()=>{try{return await T()}catch{return L("error","Failed to generate password"),null}},I=[{id:1,title:"Batch Information",description:"Enter basic batch details",icon:J},{id:2,title:"Add Students",description:"Add students to the batch",icon:W},{id:3,title:"Summary",description:"Review and confirm",icon:O}],R=()=>b.name&&b.academicYear&&b.courseType,d=()=>r.length>0&&r.length<=50;return!h||!["teacher","admin","super_admin"].includes(h.role)?e.jsxDEV("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxDEV("div",{className:"text-center",children:[e.jsxDEV(U,{className:"mx-auto h-12 w-12 text-red-500"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:305,columnNumber:11},globalThis),e.jsxDEV("h2",{className:"mt-2 text-lg font-medium text-gray-900",children:"Access Denied"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:306,columnNumber:11},globalThis),e.jsxDEV("p",{className:"mt-1 text-sm text-gray-500",children:"You need teacher or admin privileges to create batches."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:307,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:304,columnNumber:9},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:303,columnNumber:7},globalThis):e.jsxDEV("div",{className:"min-h-screen bg-gray-50","data-testid":"create-batch",children:e.jsxDEV("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxDEV("div",{className:"mb-8",children:[e.jsxDEV("div",{className:"flex items-center space-x-4 mb-4",children:e.jsxDEV("button",{onClick:()=>m("/dashboard"),className:"flex items-center text-gray-600 hover:text-gray-900",children:[e.jsxDEV(be,{className:"h-5 w-5 mr-1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:325,columnNumber:15},globalThis),"Back to Dashboard"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:321,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:320,columnNumber:11},globalThis),e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("div",{children:[e.jsxDEV("h1",{className:"text-3xl font-bold text-gray-900",children:"Create New Batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:332,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 mt-1",children:"Create a new student batch with integrated student management"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:333,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:331,columnNumber:13},globalThis),_&&e.jsxDEV("div",{className:"text-sm text-gray-500",children:["Last saved: ",_.toLocaleTimeString()]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:337,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:330,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:319,columnNumber:9},globalThis),S.text&&e.jsxDEV("div",{className:`mb-6 p-4 rounded-md flex items-center space-x-2 ${S.type==="success"?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"}`,children:[S.type==="success"?e.jsxDEV(O,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:352,columnNumber:15},globalThis):e.jsxDEV(Pe,{className:"h-5 w-5"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:354,columnNumber:15},globalThis),e.jsxDEV("span",{children:S.text},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:356,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:346,columnNumber:11},globalThis),e.jsxDEV("div",{className:"mb-8",children:e.jsxDEV("nav",{"aria-label":"Progress",children:e.jsxDEV("ol",{className:"flex items-center",children:I.map((n,p)=>{const t=n.icon,s=o===n.id,a=o>n.id;return e.jsxDEV("li",{className:`${p!==I.length-1?"pr-8 sm:pr-20":""} relative`,children:[p!==I.length-1&&e.jsxDEV("div",{className:"absolute inset-0 flex items-center","aria-hidden":"true",children:e.jsxDEV("div",{className:`h-0.5 w-full ${a?"bg-blue-600":"bg-gray-200"}`},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:373,columnNumber:25},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:372,columnNumber:23},globalThis),e.jsxDEV("div",{className:"relative flex items-center space-x-3",children:[e.jsxDEV("div",{className:`flex h-8 w-8 items-center justify-center rounded-full ${a?"bg-blue-600 text-white":s?"bg-blue-100 text-blue-600 border-2 border-blue-600":"bg-gray-100 text-gray-400"}`,children:e.jsxDEV(t,{className:"h-4 w-4"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:384,columnNumber:25},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:377,columnNumber:23},globalThis),e.jsxDEV("div",{className:"min-w-0",children:[e.jsxDEV("p",{className:`text-sm font-medium ${s?"text-blue-600":"text-gray-500"}`,children:n.title},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:387,columnNumber:25},globalThis),e.jsxDEV("p",{className:"text-xs text-gray-500",children:n.description},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:390,columnNumber:25},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:386,columnNumber:23},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:376,columnNumber:21},globalThis)]},n.id,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:370,columnNumber:19},globalThis)})},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:363,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:362,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:361,columnNumber:9},globalThis),e.jsxDEV("div",{className:"bg-white shadow rounded-lg",children:[o===1&&e.jsxDEV($e,{batchData:b,errors:V.batch,onChange:Z,onNext:()=>R()&&y(2),canProceed:R()},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:403,columnNumber:13},globalThis),o===2&&e.jsxDEV(Ye,{studentsData:r,errors:V,onChange:K,onGeneratePassword:se,onPrevious:()=>y(1),onNext:ee,canProceed:d(),loading:N},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:413,columnNumber:13},globalThis),o===3&&$&&e.jsxDEV(Re,{batchData:b,studentsData:r,creationResult:$,onCreateAnother:()=>{g({name:"",academicYear:"",courseType:"",description:""}),f([]),D(null),y(1)},onGoToDashboard:()=>m("/dashboard")},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:426,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:401,columnNumber:9},globalThis),A&&e.jsxDEV("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxDEV("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white",children:[e.jsxDEV("div",{className:"flex justify-between items-center mb-4",children:e.jsxDEV("h3",{className:"text-lg font-medium text-gray-900",children:"Confirm Batch Creation"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:446,columnNumber:17},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:445,columnNumber:15},globalThis),e.jsxDEV("div",{className:"mb-4",children:[e.jsxDEV("p",{className:"text-sm text-gray-600 mb-4",children:"You are about to create a new batch with the following details:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:450,columnNumber:17},globalThis),e.jsxDEV("div",{className:"bg-gray-50 p-3 rounded-md space-y-2",children:[e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Batch Name:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:454,columnNumber:22},globalThis)," ",b.name]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:454,columnNumber:19},globalThis),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Academic Year:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:455,columnNumber:22},globalThis)," ",b.academicYear]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:455,columnNumber:19},globalThis),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Course Type:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:456,columnNumber:22},globalThis)," ",b.courseType]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:456,columnNumber:19},globalThis),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Students:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:457,columnNumber:22},globalThis)," ",r.length]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:457,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:453,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-4",children:["This will create ",r.length," new student accounts. This action cannot be undone."]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:459,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:449,columnNumber:15},globalThis),e.jsxDEV("div",{className:"flex justify-end space-x-3",children:[e.jsxDEV("button",{onClick:()=>j(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",disabled:N,children:"Cancel"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:465,columnNumber:17},globalThis),e.jsxDEV("button",{onClick:z,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2",disabled:N,children:[N&&e.jsxDEV(Ve,{className:"h-4 w-4 animate-spin"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:477,columnNumber:31},globalThis),e.jsxDEV("span",{children:N?"Creating...":"Create Batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:478,columnNumber:19},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:472,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:464,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:444,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:443,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:317,columnNumber:7},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",lineNumber:316,columnNumber:5},globalThis)};export{Me as default};
