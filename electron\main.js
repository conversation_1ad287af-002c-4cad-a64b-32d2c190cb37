const { app, BrowserWindow, ipcMain, dialog, safeStorage } = require('electron');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const zlib = require('zlib');
const Database = require('./database/database');
const { createApplicationMenu, setUserContext, updateApplicationMenu } = require('./menu/menu');

// Production mode - always run in production for deployment
const isDev = false;
const isProd = true;

let mainWindow;
let splashWindow;
let database;





// Function to get the correct file path for the application
function getAppFilePath() {
  // Always use local files - no localhost dependencies
  return path.join(__dirname, '../dist/index.html');
}

// Function to check if built files exist
function checkBuiltFiles() {
  const indexPath = path.join(__dirname, '../dist/index.html');
  return fs.existsSync(indexPath);
}

// Function to ensure application is built before running
function ensureAppBuilt() {
  if (!checkBuiltFiles()) {
    const errorMessage = `
      Application not built. Please run the following commands:
      1. npm run build
      2. Then restart the application

      Built files should be located at: ${getAppFilePath()}
    `;
    throw new Error(errorMessage);
  }
}

// Function to create splash screen window
function createSplashWindow() {
  splashWindow = new BrowserWindow({
    width: 480,
    height: 360,
    frame: false,
    alwaysOnTop: true,
    transparent: false,
    resizable: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'splash-preload.js'),
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      devTools: false
    },
    show: false,
    center: true,
    skipTaskbar: true,
    backgroundColor: '#667eea'
  });

  // Load splash screen
  splashWindow.loadFile(path.join(__dirname, 'splash.html'));

  // Show splash window when ready
  splashWindow.once('ready-to-show', () => {
    splashWindow.show();
    if (isDev) {
      console.log('💫 Splash screen ready');
    }
  });

  // Handle splash window closed
  splashWindow.on('closed', () => {
    splashWindow = null;
  });

  return splashWindow;
}

// Function to close splash screen with fade effect
function closeSplashScreen() {
  if (splashWindow && !splashWindow.isDestroyed()) {
    // Fade out effect
    let opacity = 1.0;
    const fadeInterval = setInterval(() => {
      opacity -= 0.1;
      if (opacity <= 0) {
        clearInterval(fadeInterval);
        splashWindow.close();
        splashWindow = null;
      } else {
        splashWindow.setOpacity(opacity);
      }
    }, 50);
  }
}

// Function to update splash screen loading stage
function updateSplashStage(stage) {
  if (splashWindow && !splashWindow.isDestroyed()) {
    splashWindow.webContents.send('splash:stage', stage);
  }
}

// Function to show error on splash screen
function showSplashError(message) {
  if (splashWindow && !splashWindow.isDestroyed()) {
    splashWindow.webContents.send('splash:error', message);
  }
}

// Configure GPU and rendering settings before app ready
function configureGPUSettings() {
  // Completely disable GPU acceleration for stability
  // This is the most reliable approach for Windows desktop applications
  app.disableHardwareAcceleration();

  // Additional GPU-related flags for maximum compatibility
  app.commandLine.appendSwitch('--disable-gpu');
  app.commandLine.appendSwitch('--disable-gpu-sandbox');
  app.commandLine.appendSwitch('--disable-software-rasterizer');
  app.commandLine.appendSwitch('--disable-gpu-process-crash-limit');

  // Disable all GPU-related features
  app.commandLine.appendSwitch('--disable-gpu-rasterization');
  app.commandLine.appendSwitch('--disable-gpu-memory-buffer-video-frames');
  app.commandLine.appendSwitch('--disable-gpu-memory-buffer-compositor-resources');
  app.commandLine.appendSwitch('--disable-zero-copy');

  // Disable problematic rendering features
  app.commandLine.appendSwitch('--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer');
  app.commandLine.appendSwitch('--disable-accelerated-2d-canvas');
  app.commandLine.appendSwitch('--disable-accelerated-jpeg-decoding');
  app.commandLine.appendSwitch('--disable-accelerated-mjpeg-decode');
  app.commandLine.appendSwitch('--disable-accelerated-video-decode');

  // Memory and performance optimizations
  app.commandLine.appendSwitch('--max-old-space-size=512');
  app.commandLine.appendSwitch('--no-sandbox');

  // Disable unnecessary features for desktop app
  app.commandLine.appendSwitch('--disable-web-security');
  app.commandLine.appendSwitch('--disable-background-timer-throttling');
  app.commandLine.appendSwitch('--disable-renderer-backgrounding');
  app.commandLine.appendSwitch('--disable-backgrounding-occluded-windows');

  // Force software rendering
  app.commandLine.appendSwitch('--use-gl=swiftshader');
  app.commandLine.appendSwitch('--ignore-gpu-blacklist');
  app.commandLine.appendSwitch('--disable-gpu-driver-bug-workarounds');
}

async function createWindow() {
  // Update splash screen
  updateSplashStage('Preparing user interface...');

  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: true, // Always enable web security in production
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      // GPU and rendering optimizations - disabled for stability
      hardwareAcceleration: false,
      offscreen: false,
      // Memory optimizations
      backgroundThrottling: false,
      // Additional security for production
      sandbox: false,
      nodeIntegrationInWorker: false,
      nodeIntegrationInSubFrames: false,
      // Performance optimizations
      spellcheck: false,
      enableWebSQL: false,
      // Production mode - disable dev tools
      devTools: false
    },
    icon: path.join(__dirname, '../public/icon.png'),
    show: false, // Don't show until ready
    titleBarStyle: 'default',
    // Additional window optimizations
    transparent: false,
    frame: true,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true,
    // Performance settings
    skipTaskbar: false,
    kiosk: false,
    alwaysOnTop: false,
    fullscreen: false,
    simpleFullscreen: false,
    // Windows-specific optimizations
    thickFrame: true,
    vibrancy: null,
    // Disable unnecessary features
    enableLargerThanScreen: false,
    hasShadow: true,
    opacity: 1.0
  });

  // Load the application - Always use local files
  try {
    // Ensure the application is built before attempting to load
    ensureAppBuilt();

    const appFilePath = getAppFilePath();

    // Always load from local files - no network dependencies
    await mainWindow.loadFile(appFilePath);

    if (isDev) {
      console.log('✅ Application loaded successfully');
    }
  } catch (error) {
    // Show error on splash screen first
    showSplashError(`Failed to load application: ${error.message}`);

    // Also show error page in main window
    const errorHtml = `
      <html>
        <head><title>Application Error</title></head>
        <body style="font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5;">
          <div style="background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h1 style="color: #e74c3c;">Application Error</h1>
            <p><strong>Could not load the Peer Review System application</strong></p>
            <p>Error: ${error.message}</p>
            <h3>Troubleshooting:</h3>
            <ul>
              <li>Make sure the application has been built: <code>npm run build</code></li>
              <li>Check if the dist directory exists and contains index.html</li>
              <li>Try rebuilding the application</li>
              <li>Restart the application if the issue persists</li>
            </ul>
            <button onclick="location.reload()" style="background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Retry</button>
          </div>
        </body>
      </html>
    `;

    await mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(errorHtml)}`);
  }

  mainWindow.once('ready-to-show', () => {
    // Initialize application menu
    createApplicationMenu();

    // Update splash screen to final stage
    updateSplashStage('Ready to launch...');

    // Small delay to show final stage, then close splash and show main window
    setTimeout(() => {
      closeSplashScreen();
      mainWindow.show();

      // Production mode - no development logging or dev tools
    }, 500);
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle navigation errors
  mainWindow.webContents.on('did-fail-load', (_, errorCode, errorDescription, validatedURL) => {
    if (isDev) {
      console.error('Navigation failed:', { errorCode, errorDescription, validatedURL });
    }
    // In production, silently handle errors or show user-friendly message
  });

  // Security - disable developer tools access in production only
  if (isProd) {
    mainWindow.webContents.on('before-input-event', (event, input) => {
      // Block all developer tools shortcuts in production
      if (
        input.key === 'F12' ||                                    // Developer Tools
        (input.control && input.shift && input.key === 'I') ||   // Developer Tools (Ctrl+Shift+I)
        (input.control && input.shift && input.key === 'J') ||   // Console (Ctrl+Shift+J)
        (input.control && input.shift && input.key === 'C') ||   // Element Inspector (Ctrl+Shift+C)
        (input.control && input.key === 'U') ||                  // View Source (Ctrl+U)
        (input.control && input.shift && input.key === 'Delete') || // Clear Storage
        (input.alt && input.key === 'F4') ||                     // Alt+F4 (handled by OS but extra safety)
        (input.control && input.shift && input.key === 'R') ||   // Hard Reload
        (input.control && input.key === 'F5')                    // Reload with cache clear
      ) {
        event.preventDefault();
      }
    });
  }

  // Disable right-click context menu in production only
  if (isProd) {
    mainWindow.webContents.on('context-menu', (event) => {
      event.preventDefault();
    });
  }

  // Disable new window creation (prevents popup developer tools)
  mainWindow.webContents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });

  // Additional security: disable navigation to external URLs
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    // Only allow file protocol for local app files
    if (parsedUrl.protocol !== 'file:') {
      event.preventDefault();
    }
  });
}

// Configure GPU settings before app is ready
configureGPUSettings();

// Handle GPU process crashes gracefully
app.on('gpu-process-crashed', (_, _killed) => {
  // Silently handle GPU crashes in production - no logging
});

// Handle renderer process crashes
app.on('renderer-process-crashed', (_, webContents, _killed) => {
  // Production mode - no logging, just reload if possible
  if (webContents && !webContents.isDestroyed()) {
    webContents.reload();
  }
});

// Handle child process gone
app.on('child-process-gone', (_, _details) => {
  // Production mode - silently handle child process issues
});

app.whenReady().then(async () => {
  try {
    // Create splash screen first for immediate feedback
    createSplashWindow();

    // Small delay to ensure splash screen is visible
    await new Promise(resolve => setTimeout(resolve, 100));

    // Update splash screen and initialize database
    updateSplashStage('Loading configuration...');
    await new Promise(resolve => setTimeout(resolve, 300));

    updateSplashStage('Connecting to database...');
    database = new Database();

    try {
      const initResult = await database.initialize();

      // Log initialization result for debugging (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.log('Database initialization result:', initResult);
      }

      // Verify database is properly initialized
      if (!initResult || !initResult.success) {
        throw new Error('Database initialization returned unsuccessful result');
      }

      // Update splash with database status
      updateSplashStage(`Database ready (${initResult.tablesCreated} tables, setup ${initResult.setupRequired ? 'required' : 'complete'})`);
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error) {
      // Enhanced error reporting for database initialization
      const errorMessage = error.message || 'Unknown database error';
      const detailedError = `Database initialization failed: ${errorMessage}`;

      // Show detailed error on splash screen
      showSplashError(detailedError);

      // Log full error details for debugging (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.error('Database initialization error:', {
          message: error.message,
          stack: error.stack,
          code: error.code,
          originalError: error.originalError
        });
      }

      throw new Error(detailedError);
    }

    // Update splash screen and create main window
    updateSplashStage('Finalizing setup...');
    await new Promise(resolve => setTimeout(resolve, 300));

    // Create the main window
    await createWindow();

    app.on('activate', async () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        // Create splash screen first if reactivating
        createSplashWindow();
        await createWindow();
      }
    });
  } catch (error) {
    // Show error on splash screen if it exists, otherwise show dialog
    if (splashWindow && !splashWindow.isDestroyed()) {
      showSplashError(`Failed to initialize the application: ${error.message}`);
    } else {
      const { dialog } = require('electron');
      dialog.showErrorBox('Initialization Error',
        `Failed to initialize the application:\n\n${error.message}\n\nThe application will now close.`);
      app.quit();
    }
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Splash screen IPC handlers
ipcMain.on('splash:retry', () => {
  // Retry application initialization
  if (splashWindow && !splashWindow.isDestroyed()) {
    // Reset splash screen to loading state
    splashWindow.webContents.send('splash:stage', 'Retrying initialization...');

    // Restart the initialization process
    setTimeout(async () => {
      try {
        updateSplashStage('Loading configuration...');
        await new Promise(resolve => setTimeout(resolve, 300));

        updateSplashStage('Connecting to database...');
        if (!database) {
          database = new Database();
        }

        try {
          const retryResult = await database.initialize();

          // Verify retry was successful
          if (!retryResult || !retryResult.success) {
            throw new Error('Database retry initialization returned unsuccessful result');
          }

          // Update splash with retry success
          updateSplashStage(`Database retry successful (${retryResult.tablesCreated} tables)`);
          await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error) {
          const retryError = `Database retry initialization failed: ${error.message || 'Unknown error'}`;

          // Log retry error details (only in development)
          if (process.env.NODE_ENV === 'development') {
            console.error('Database retry error:', {
              message: error.message,
              stack: error.stack,
              code: error.code,
              originalError: error.originalError
            });
          }

          throw new Error(retryError);
        }

        updateSplashStage('Finalizing setup...');
        await new Promise(resolve => setTimeout(resolve, 300));

        if (!mainWindow || mainWindow.isDestroyed()) {
          await createWindow();
        }
      } catch (error) {
        showSplashError(`Retry failed: ${error.message}`);
      }
    }, 1000);
  }
});

// IPC Handlers for database operations
ipcMain.handle('db:query', async (_, query, params = []) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    return await database.query(query, params);
  } catch (error) {
    throw error;
  }
});

ipcMain.handle('db:run', async (_, query, params = []) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    return await database.run(query, params);
  } catch (error) {
    throw error;
  }
});

// User management IPC handlers
ipcMain.handle('users:create', async (_, userData) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    return await database.createUser(userData);
  } catch (error) {
    throw error;
  }
});

ipcMain.handle('users:authenticate', async (_, credentials) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    const result = await database.authenticateUser(credentials);

    // Update menu context when user authenticates
    if (result.success && result.user) {
      setUserContext(result.user);
    }

    return result;
  } catch (error) {
    throw error;
  }
});

// Menu-related IPC handlers
ipcMain.handle('menu:setUserContext', async (_, user) => {
  try {
    setUserContext(user);
    return { success: true };
  } catch (error) {
    throw error;
  }
});

ipcMain.handle('menu:updateMenu', async (_) => {
  try {
    updateApplicationMenu();
    return { success: true };
  } catch (error) {
    throw error;
  }
});

// Offline application - no external shell operations

ipcMain.handle('users:getAll', async (_, filters = {}) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    return await database.getAllUsers(filters);
  } catch (error) {
    throw error;
  }
});

// Enhanced admin management IPC handlers
ipcMain.handle('admin:updateUser', async (_, userId, userData, updatedBy) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    return await database.updateUser(userId, userData, updatedBy);
  } catch (error) {
    throw error;
  }
});

ipcMain.handle('admin:resetPassword', async (_, userId, newPassword, resetBy) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    return await database.resetUserPassword(userId, newPassword, resetBy);
  } catch (error) {
    throw error;
  }
});

ipcMain.handle('admin:deactivateUser', async (_, userId, deactivatedBy) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    return await database.deactivateUser(userId, deactivatedBy);
  } catch (error) {
    throw error;
  }
});

ipcMain.handle('admin:activateUser', async (_, userId, activatedBy) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    return await database.activateUser(userId, activatedBy);
  } catch (error) {
    throw error;
  }
});

ipcMain.handle('admin:generatePassword', async (_) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    return await database.generateSecurePassword();
  } catch (error) {
    throw error;
  }
});

ipcMain.handle('admin:getAuditLog', async (_, filters = {}) => {
  try {
    if (!database) {
      throw new Error('Database not initialized');
    }
    return await database.getAuditLog(filters);
  } catch (error) {
    throw error;
  }
});

// Batch management IPC handlers
ipcMain.handle('batches:create', async (_, batchData) => {
  try {
    return await database.createBatch(batchData);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:getAll', async (_, filters = {}) => {
  try {
    return await database.getAllBatches(filters);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:createWithStudents', async (_, batchData, studentsData, createdBy) => {
  try {
    return await database.createBatchWithStudents(batchData, studentsData, createdBy);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:checkNameUnique', async (_, name, teacherId, excludeBatchId) => {
  try {
    return await database.checkBatchNameUnique(name, teacherId, excludeBatchId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:checkStudentIdsUnique', async (_, studentIds, institutionId) => {
  try {
    return await database.checkStudentIdsUnique(studentIds, institutionId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:getDetails', async (_, batchId, teacherId) => {
  try {
    return await database.getBatchDetails(batchId, teacherId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:update', async (_, batchId, batchData, updatedBy, teacherId) => {
  try {
    return await database.updateBatch(batchId, batchData, updatedBy, teacherId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:addStudents', async (_, batchId, studentsData, addedBy, teacherId) => {
  try {
    return await database.addStudentsToBatch(batchId, studentsData, addedBy, teacherId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:removeStudent', async (_, batchId, studentId, removedBy, teacherId) => {
  try {
    return await database.removeStudentFromBatch(batchId, studentId, removedBy, teacherId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:archive', async (_, batchId, archivedBy, teacherId) => {
  try {
    return await database.archiveBatch(batchId, archivedBy, teacherId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:activate', async (_, batchId, activatedBy, teacherId) => {
  try {
    return await database.activateBatch(batchId, activatedBy, teacherId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('batches:getStatistics', async (_, teacherId) => {
  try {
    return await database.getBatchStatistics(teacherId);
  } catch (error) {throw error;
  }
});

// Enhanced Assessment management IPC handlers
ipcMain.handle('assessments:createWithQuestions', async (_, assessmentData, questionsData, createdBy) => {
  try {
    return await database.createAssessmentWithQuestions(assessmentData, questionsData, createdBy);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:checkNameUnique', async (_, name, institutionId, excludeAssessmentId) => {
  try {
    return await database.checkAssessmentNameUnique(name, institutionId, excludeAssessmentId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:getAll', async (_, filters = {}) => {
  try {
    return await database.getAllAssessments(filters);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:getDetails', async (_, assessmentId, institutionId) => {
  try {
    return await database.getAssessmentDetails(assessmentId, institutionId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:publish', async (_, assessmentId, publishedBy, institutionId) => {
  try {
    return await database.publishAssessment(assessmentId, publishedBy, institutionId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:assignToBatches', async (_, assessmentId, batchIds, assignedBy, institutionId) => {
  try {
    return await database.assignAssessmentToBatches(assessmentId, batchIds, assignedBy, institutionId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:delete', async (_, assessmentId, deletedBy, institutionId) => {
  try {
    return await database.deleteAssessment(assessmentId, deletedBy, institutionId);
  } catch (error) {throw error;
  }
});

// Assessment access IPC handlers
ipcMain.handle('assessments:getUserAccessibleBatches', async (_, userId, userRole, institutionId) => {
  try {
    return await database.getUserAccessibleBatches(userId, userRole, institutionId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:getAssessmentsForBatch', async (_, batchId, userId, userRole) => {
  try {
    return await database.getAssessmentsForBatch(batchId, userId, userRole);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:getBatchStudentsWithProgress', async (_, batchId, assessmentId, userId, userRole) => {
  try {
    return await database.getBatchStudentsWithProgress(batchId, assessmentId, userId, userRole);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:getStudentAssessmentHistory', async (_, studentId, assessmentId, batchId) => {
  try {
    return await database.getStudentAssessmentHistory(studentId, assessmentId, batchId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:canStudentAccessAssessment', async (_, studentId, assessmentId) => {
  try {
    return await database.canStudentAccessAssessment(studentId, assessmentId);
  } catch (error) {throw error;
  }
});

// Student management IPC handlers
ipcMain.handle('assessments:generateAssessmentForm', async (_event, assessmentId, teacherId, institutionId) => {
  try {
    return await database.generateAssessmentForm(assessmentId, teacherId, institutionId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:uploadStudentResponse', async (_event, assessmentId, studentId, responses, uploadedBy, institutionId) => {
  try {
    return await database.uploadStudentResponse(assessmentId, studentId, responses, uploadedBy, institutionId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:getStudentStatistics', async (_event, studentId, assessmentId, teacherId, institutionId) => {
  try {
    return await database.getStudentAssessmentStatistics(studentId, assessmentId, teacherId, institutionId);
  } catch (error) {throw error;
  }
});

// Assessment processing IPC handlers
ipcMain.handle('assessments:getBatchCompletionStatus', async (_event, batchId, assessmentId, teacherId, institutionId) => {
  try {
    return await database.getBatchCompletionStatus(batchId, assessmentId, teacherId, institutionId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:processForPeerReview', async (_event, assessmentId, batchId, processedBy, institutionId) => {
  try {
    return await database.processAssessmentForPeerReview(assessmentId, batchId, processedBy, institutionId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:canProcess', async (_event, assessmentId, batchId, teacherId) => {
  try {
    return await database.canProcessAssessment(assessmentId, batchId, teacherId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('assessments:getBatchSubmissionStatus', async (_event, batchId, assessmentId, teacherId) => {
  try {
    return await database.getBatchSubmissionStatus(batchId, assessmentId, teacherId);
  } catch (error) {throw error;
  }
});

// Assessment statistics IPC handlers
ipcMain.handle('assessments:getStatistics', async (_event, assessmentId, batchId, teacherId) => {
  try {
    return await database.getAssessmentStatistics(assessmentId, batchId, teacherId);
  } catch (error) {throw error;
  }
});

// Settings IPC handlers
ipcMain.handle('settings:getUserSettings', async (_event, userId) => {
  try {
    return await database.getUserSettings(userId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('settings:updateUserSetting', async (_event, userId, settingKey, settingValue) => {
  try {
    return await database.updateUserSetting(userId, settingKey, settingValue);
  } catch (error) {throw error;
  }
});

ipcMain.handle('settings:getApplicationSettings', async () => {
  try {
    return await database.getApplicationSettings();
  } catch (error) {throw error;
  }
});

ipcMain.handle('settings:updateApplicationSetting', async (_event, settingKey, settingValue, description) => {
  try {
    return await database.updateApplicationSetting(settingKey, settingValue, description);
  } catch (error) {throw error;
  }
});

// Institution IPC handlers
ipcMain.handle('institution:getDetails', async (_event, institutionId) => {
  try {
    return await database.getInstitutionDetails(institutionId);
  } catch (error) {throw error;
  }
});

ipcMain.handle('institution:updateDetails', async (_event, institutionId, institutionData, detailsData, updatedBy) => {
  try {
    return await database.updateInstitutionDetails(institutionId, institutionData, detailsData, updatedBy);
  } catch (error) {throw error;
  }
});

// Dashboard IPC handlers
ipcMain.handle('dashboard:getStatistics', async (_event, userId, userRole, institutionId) => {
  try {
    return await database.getDashboardStatistics(userId, userRole, institutionId);
  } catch (error) {throw error;
  }
});

// Peer review IPC handlers
ipcMain.handle('reviews:submit', async (_event, reviewData) => {
  try {
    return await database.submitReview(reviewData);
  } catch (error) {throw error;
  }
});

ipcMain.handle('reviews:getStats', async (_event, filters = {}) => {
  try {
    return await database.getReviewStats(filters);
  } catch (error) {throw error;
  }
});

// File operations
ipcMain.handle('file:showSaveDialog', async (_event, options) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
  } catch (error) {throw error;
  }
});

// Settings IPC handlers
ipcMain.handle('settings:get', async (_event, key) => {
  try {
    return await database.getSetting(key);
  } catch (error) {throw error;
  }
});

ipcMain.handle('settings:set', async (_event, key, value) => {
  try {
    return await database.setSetting(key, value);
  } catch (error) {throw error;
  }
});

// Institution management
ipcMain.handle('institution:get', async () => {
  try {
    return await database.getInstitution();
  } catch (error) {throw error;
  }
});

ipcMain.handle('institution:update', async (_event, institutionData, updatedBy) => {
  try {
    return await database.updateInstitution(institutionData, updatedBy);
  } catch (error) {throw error;
  }
});

ipcMain.handle('institution:uploadLogo', async (_event, logoData, updatedBy) => {
  try {
    return await database.saveInstitutionLogo(logoData, updatedBy);
  } catch (error) {throw error;
  }
});

ipcMain.handle('institution:removeLogo', async (_event, updatedBy) => {
  try {
    return await database.removeInstitutionLogo(updatedBy);
  } catch (error) {throw error;
  }
});

ipcMain.handle('institution:getLogoPath', async (_event, logoFileName) => {
  try {
    if (!logoFileName) {return null;}

    const logoPath = path.join(__dirname, 'assets/logos', logoFileName);
    if (fs.existsSync(logoPath)) {
      return logoPath;
    }
    return null;
  } catch {
    // Silently handle logo path errors
    return null;
  }
});

// PDF generation
ipcMain.handle('pdf:generateReport', async (_event, reportData) => {
  try {
    return await database.generatePDFReport(reportData);
  } catch (error) {throw error;
  }
});

// Offline application - no auto-updater needed

// Offline application - no auto-updater IPC handlers needed

ipcMain.handle('updater:getVersion', async () => {
  try {
    return app.getVersion();
  } catch (error) {throw error;
  }
});

// Logging IPC handlers
ipcMain.handle('logger:writeLogs', async (_, logs) => {
  try {
    const logDir = path.join(__dirname, '../logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    const today = new Date().toISOString().split('T')[0];
    const logFile = path.join(logDir, `app-${today}.log`);

    const logEntries = logs.map(log =>
      `${log.timestamp} [${log.level.toUpperCase()}] ${log.message} ${JSON.stringify(log.context || {})}`
    ).join('\n') + '\n';

    fs.appendFileSync(logFile, logEntries);
    return { success: true };
  } catch (error) {throw error;
  }
});

// Configuration IPC handlers
const configStore = new Map();

ipcMain.handle('config:get', async (_, key) => {
  try {
    return configStore.get(key);
  } catch (error) {throw error;
  }
});

ipcMain.handle('config:set', async (_, key, value) => {
  try {
    configStore.set(key, value);
    return { success: true };
  } catch (error) {throw error;
  }
});

ipcMain.handle('config:getAll', async () => {
  try {
    return Object.fromEntries(configStore);
  } catch (error) {throw error;
  }
});

ipcMain.handle('config:setAll', async (_, config) => {
  try {
    configStore.clear();
    Object.entries(config).forEach(([key, value]) => {
      configStore.set(key, value);
    });
    return { success: true };
  } catch (error) {throw error;
  }
});

ipcMain.handle('config:clear', async () => {
  try {
    configStore.clear();
    return { success: true };
  } catch (error) {throw error;
  }
});

// Safe storage IPC handlers
ipcMain.handle('safeStorage:isEncryptionAvailable', () => {
  return safeStorage.isEncryptionAvailable();
});

ipcMain.handle('safeStorage:encryptString', (_, plainText) => {
  try {
    return safeStorage.encryptString(plainText);
  } catch (error) {throw error;
  }
});

ipcMain.handle('safeStorage:decryptString', (_, encryptedData) => {
  try {
    return safeStorage.decryptString(encryptedData);
  } catch (error) {throw error;
  }
});

// First-time setup IPC handlers
ipcMain.handle('setup:checkStatus', async () => {
  try {
    const setupCompleted = await database.get(
      'SELECT setting_value FROM application_settings WHERE setting_key = ?',
      ['setup_completed']
    );

    const hasUsers = await database.query('SELECT COUNT(*) as count FROM users');

    return {
      success: true,
      data: {
        setupCompleted: setupCompleted?.setting_value === 'true',
        hasUsers: hasUsers[0]?.count > 0
      }
    };
  } catch (error) {return { success: false, error: error.message };
  }
});

ipcMain.handle('setup:createSuperAdmin', async (_, setupData) => {
  try {
    const { adminData, institutionData } = setupData;

    // Validate input data
    if (!adminData || !institutionData) {
      throw new Error('Missing required setup data');
    }

    // Validate required admin fields
    const requiredAdminFields = ['firstName', 'lastName', 'email', 'username', 'password'];
    for (const field of requiredAdminFields) {
      if (!adminData[field] || (typeof adminData[field] === 'string' && adminData[field].trim() === '')) {
        throw new Error(`Missing required admin field: ${field}`);
      }
    }

    // Validate required institution fields (offline-only, no contact info)
    const requiredInstitutionFields = ['name', 'address'];
    for (const field of requiredInstitutionFields) {
      if (!institutionData[field] || (typeof institutionData[field] === 'string' && institutionData[field].trim() === '')) {
        throw new Error(`Missing required institution field: ${field}`);
      }
    }

    // Create institution first
    const institutionResult = await database.createInstitution(institutionData);
    if (!institutionResult.success) {
      throw new Error(`Institution creation failed: ${institutionResult.error}`);
    }

    // Create super admin user
    const userResult = await database.createUser({
      ...adminData,
      role: 'super_admin',
      institutionId: institutionResult.data.id
    });

    if (!userResult.success) {
      // If user creation fails, we should clean up the institution
      // However, since createInstitution doesn't provide rollback, we'll log the error
      throw new Error(`User creation failed: ${userResult.error}`);
    }

    // Mark setup as completed
    await database.run(
      `INSERT OR REPLACE INTO application_settings (setting_key, setting_value, description)
       VALUES (?, ?, ?)`,
      ['setup_completed', 'true', 'Indicates whether the initial application setup has been completed']
    );

    // Log the setup completion
    await database.logAuditAction(
      userResult.data.id,
      'INITIAL_SETUP',
      'application_settings',
      'setup_completed',
      null,
      { setup_completed: true, admin_created: true }
    );

    return {
      success: true,
      data: {
        user: userResult.data,
        institution: institutionResult.data
      }
    };
  } catch (error) {
    console.error('Setup failed:', error);
    return {
      success: false,
      error: error.message || 'A database error occurred. Please try again.'
    };
  }
});

// Backup system IPC handlers
ipcMain.handle('backup:create', async (_, backupOptions) => {
  try {
    const { name, description, password, location } = backupOptions;

    // Get default backup location if not specified
    const backupLocation = location || path.join(app.getPath('documents'), 'PeerReviewBackups');

    // Ensure backup directory exists
    if (!fs.existsSync(backupLocation)) {
      fs.mkdirSync(backupLocation, { recursive: true });
    }

    // Generate backup filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFileName = `${name || 'backup'}_${timestamp}.prb`;
    const backupPath = path.join(backupLocation, backupFileName);

    // Get database path
    const dbPath = database.dbPath;

    // Read database file
    const dbData = fs.readFileSync(dbPath);

    // Create backup metadata
    const metadata = {
      name: name || 'Unnamed Backup',
      description: description || '',
      timestamp: new Date().toISOString(),
      version: app.getVersion(),
      size: dbData.length,
      checksum: crypto.createHash('sha256').update(dbData).digest('hex')
    };

    // Compress database data
    const compressedData = zlib.gzipSync(dbData);

    // Encrypt if password provided
    let finalData;
    if (password) {
      const key = crypto.scryptSync(password, 'salt', 32);
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);

      const encryptedData = Buffer.concat([
        cipher.update(compressedData),
        cipher.final()
      ]);

      finalData = JSON.stringify({
        encrypted: true,
        iv: iv.toString('hex'),
        data: encryptedData.toString('base64'),
        metadata
      });
    } else {
      finalData = JSON.stringify({
        encrypted: false,
        data: compressedData.toString('base64'),
        metadata
      });
    }

    // Write backup file
    fs.writeFileSync(backupPath, finalData);

    // Log backup creation
    await database.logAuditAction(
      null, // System action
      'BACKUP_CREATED',
      'system',
      backupFileName,
      null,
      { path: backupPath, size: finalData.length }
    );

    return {
      success: true,
      data: {
        path: backupPath,
        name: backupFileName,
        size: finalData.length,
        metadata
      }
    };
  } catch (error) {return { success: false, error: error.message };
  }
});

ipcMain.handle('backup:restore', async (_, restoreOptions) => {
  try {
    const { backupPath, password, mergeMode = false } = restoreOptions;

    // Read backup file
    const backupData = fs.readFileSync(backupPath, 'utf8');
    const backup = JSON.parse(backupData);

    // Decrypt if needed
    let compressedData;
    if (backup.encrypted) {
      if (!password) {
        throw new Error('Password required for encrypted backup');
      }

      const key = crypto.scryptSync(password, 'salt', 32);
      const iv = Buffer.from(backup.iv, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);

      const encryptedBuffer = Buffer.from(backup.data, 'base64');
      compressedData = Buffer.concat([
        decipher.update(encryptedBuffer),
        decipher.final()
      ]);
    } else {
      compressedData = Buffer.from(backup.data, 'base64');
    }

    // Decompress data
    const dbData = zlib.gunzipSync(compressedData);

    // Verify checksum
    const checksum = crypto.createHash('sha256').update(dbData).digest('hex');
    if (backup.metadata.checksum !== checksum) {
      throw new Error('Backup file is corrupted (checksum mismatch)');
    }

    // Close current database connection
    await database.close();

    if (mergeMode) {
      // TODO: Implement merge functionality
      throw new Error('Merge mode not yet implemented');
    } else {
      // Complete replacement
      const dbPath = database.dbPath;

      // Create backup of current database
      const currentBackupPath = `${dbPath}.backup.${Date.now()}`;
      if (fs.existsSync(dbPath)) {
        fs.copyFileSync(dbPath, currentBackupPath);
      }

      try {
        // Write restored database
        fs.writeFileSync(dbPath, dbData);

        // Reinitialize database connection
        await database.initialize();

        // Log restoration
        await database.logAuditAction(
          null, // System action
          'BACKUP_RESTORED',
          'system',
          path.basename(backupPath),
          null,
          {
            restored_from: backupPath,
            backup_timestamp: backup.metadata.timestamp,
            current_backup: currentBackupPath
          }
        );

        return {
          success: true,
          data: {
            restored: true,
            metadata: backup.metadata,
            currentBackup: currentBackupPath
          }
        };
      } catch (restoreError) {
        // Restore original database on failure
        if (fs.existsSync(currentBackupPath)) {
          fs.copyFileSync(currentBackupPath, dbPath);
          await database.initialize();
        }
        throw restoreError;
      }
    }
  } catch (error) {return { success: false, error: error.message };
  }
});

ipcMain.handle('backup:list', async (_, location) => {
  try {
    const backupLocation = location || path.join(app.getPath('documents'), 'PeerReviewBackups');

    if (!fs.existsSync(backupLocation)) {
      return { success: true, data: [] };
    }

    const files = fs.readdirSync(backupLocation);
    const backups = [];

    for (const file of files) {
      if (file.endsWith('.prb')) {
        try {
          const filePath = path.join(backupLocation, file);
          const stats = fs.statSync(filePath);
          const backupData = fs.readFileSync(filePath, 'utf8');
          const backup = JSON.parse(backupData);

          backups.push({
            name: file,
            path: filePath,
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime,
            encrypted: backup.encrypted,
            metadata: backup.metadata
          });
        } catch {
          // Failed to read backup file - skip it
        }
      }
    }

    // Sort by creation date (newest first)
    backups.sort((a, b) => new Date(b.created) - new Date(a.created));

    return { success: true, data: backups };
  } catch (error) {return { success: false, error: error.message };
  }
});

ipcMain.handle('backup:delete', async (_, backupPath) => {
  try {
    if (fs.existsSync(backupPath)) {
      fs.unlinkSync(backupPath);

      // Log backup deletion
      await database.logAuditAction(
        null, // System action
        'BACKUP_DELETED',
        'system',
        path.basename(backupPath),
        null,
        { deleted_path: backupPath }
      );
    }

    return { success: true };
  } catch (error) {return { success: false, error: error.message };
  }
});

ipcMain.handle('backup:verify', async (_, backupPath) => {
  try {
    const backupData = fs.readFileSync(backupPath, 'utf8');
    const backup = JSON.parse(backupData);

    // Basic structure validation
    if (!backup.metadata || !backup.data) {
      throw new Error('Invalid backup file structure');
    }

    // Try to decompress and verify checksum
    let compressedData;
    if (backup.encrypted) {
      // Can't verify encrypted backup without password
      return {
        success: true,
        data: {
          valid: true,
          encrypted: true,
          metadata: backup.metadata,
          requiresPassword: true
        }
      };
    } else {
      compressedData = Buffer.from(backup.data, 'base64');
    }

    const dbData = zlib.gunzipSync(compressedData);
    const checksum = crypto.createHash('sha256').update(dbData).digest('hex');

    return {
      success: true,
      data: {
        valid: checksum === backup.metadata.checksum,
        encrypted: false,
        metadata: backup.metadata,
        checksum: checksum,
        expectedChecksum: backup.metadata.checksum
      }
    };
  } catch (error) {return { success: false, error: error.message };
  }
});

// Handle app closing
app.on('before-quit', async () => {
  if (database) {
    await database.close();
  }
});
