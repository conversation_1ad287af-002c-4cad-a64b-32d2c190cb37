import { j as jsxDevRuntimeExports, r as reactExports, u as useNavigate } from './chunk-2ef8e52b.js';
import { b as useDatabase, u as useAuth } from './main-ab4f3d46.js';
import { F as FileText, A as AlertCircle, w as Calendar, I as Info, n as HelpCircle, y as Plus, a2 as GripVertical, $ as Copy, p as EyeOff, l as Eye, T as Trash2, a3 as Move, C as CheckCircle, c as Shield, q as User, a4 as Lock, r as Clock, Z as ArrowLeft, x as Save, b as ArrowRight, U as Users } from './chunk-028772a4.js';
import './chunk-03d61bd9.js';

// Assessment validation utilities for the Create Assessment module

// Validate assessment basic information
const validateAssessmentInfo = (assessmentData) => {
  const errors = {};

  // Assessment Name validation
  if (!assessmentData.name || assessmentData.name.trim().length === 0) {
    errors.name = ['Assessment name is required'];
  } else if (assessmentData.name.trim().length < 3) {
    errors.name = ['Assessment name must be at least 3 characters long'];
  } else if (assessmentData.name.trim().length > 100) {
    errors.name = ['Assessment name must not exceed 100 characters'];
  } else if (!/^[a-zA-Z0-9\s\-_.,()]+$/.test(assessmentData.name.trim())) {
    errors.name = ['Assessment name contains invalid characters'];
  }

  // Short Description validation
  if (!assessmentData.shortDescription || assessmentData.shortDescription.trim().length === 0) {
    errors.shortDescription = ['Short description is required'];
  } else if (assessmentData.shortDescription.trim().length < 10) {
    errors.shortDescription = ['Short description must be at least 10 characters long'];
  } else if (assessmentData.shortDescription.trim().length > 500) {
    errors.shortDescription = ['Short description must not exceed 500 characters'];
  }

  // Academic Year validation
  if (!assessmentData.academicYear) {
    errors.academicYear = ['Academic year is required'];
  } else if (!/^\d{4}-\d{4}$/.test(assessmentData.academicYear)) {
    errors.academicYear = ['Academic year must be in YYYY-YYYY format'];
  } else {
    const yearParts = assessmentData.academicYear.split('-');
    if (yearParts.length !== 2) {
      errors.academicYear = ['Academic year must be in YYYY-YYYY format'];
    } else {
      const startYearStr = yearParts[0].trim();
      const endYearStr = yearParts[1].trim();

      const startYear = parseInt(startYearStr, 10);
      const endYear = parseInt(endYearStr, 10);

      if (isNaN(startYear) || !Number.isInteger(startYear)) {
        errors.academicYear = ['Start year must be a valid integer'];
      } else if (isNaN(endYear) || !Number.isInteger(endYear)) {
        errors.academicYear = ['End year must be a valid integer'];
      } else if (endYear !== startYear + 1) {
        errors.academicYear = ['Academic year must be consecutive years (e.g., 2024-2025)'];
      } else {
        const currentYear = new Date().getFullYear();
        if (startYear < currentYear - 10 || startYear > currentYear + 5) {
          errors.academicYear = ['Academic year must be within reasonable range'];
        }
      }
    }
  }

  // Course Type validation
  if (!assessmentData.courseType) {
    errors.courseType = ['Course type is required'];
  } else if (!['Course 01', 'Course 02', 'Course 03', 'Course 04'].includes(assessmentData.courseType)) {
    errors.courseType = ['Invalid course type selected'];
  }

  // Assessment Type validation
  if (!assessmentData.assessmentType) {
    errors.assessmentType = ['Assessment type is required'];
  } else if (!['Formative', 'Summative', 'Peer Review'].includes(assessmentData.assessmentType)) {
    errors.assessmentType = ['Invalid assessment type selected'];
  }

  // Due Date validation
  if (!assessmentData.dueDate) {
    errors.dueDate = ['Due date is required'];
  } else {
    const dueDate = new Date(assessmentData.dueDate);
    const now = new Date();
    now.setHours(0, 0, 0, 0); // Reset time to start of day for comparison

    if (isNaN(dueDate.getTime())) {
      errors.dueDate = ['Invalid due date format'];
    } else if (dueDate <= now) {
      errors.dueDate = ['Due date must be in the future'];
    } else {
      // Check if due date is too far in the future (more than 2 years)
      const maxDate = new Date();
      maxDate.setFullYear(maxDate.getFullYear() + 2);
      if (dueDate > maxDate) {
        errors.dueDate = ['Due date cannot be more than 2 years in the future'];
      }
    }
  }

  // Instructions validation (optional field)
  if (assessmentData.instructions && assessmentData.instructions.trim().length > 2000) {
    errors.instructions = ['Instructions must not exceed 2000 characters'];
  }

  return errors;
};

// Validate individual question
const validateQuestion = (questionData, _questionIndex) => {
  const errors = {};

  // Question Text validation
  if (!questionData.questionText || questionData.questionText.trim().length === 0) {
    errors.questionText = ['Question text is required'];
  } else if (questionData.questionText.trim().length < 10) {
    errors.questionText = ['Question text must be at least 10 characters long'];
  } else if (questionData.questionText.trim().length > 1000) {
    errors.questionText = ['Question text must not exceed 1000 characters'];
  }

  // Question Type validation
  if (!questionData.questionType) {
    errors.questionType = ['Question type is required'];
  } else if (questionData.questionType !== 'MCQ') {
    errors.questionType = ['Only Multiple Choice Questions (MCQ) are currently supported'];
  }

  // MCQ Options validation
  if (questionData.questionType === 'MCQ') {
    if (!questionData.options || !Array.isArray(questionData.options)) {
      errors.options = ['MCQ questions must have options'];
    } else if (questionData.options.length < 2) {
      errors.options = ['MCQ questions must have at least 2 options'];
    } else if (questionData.options.length > 8) {
      errors.options = ['MCQ questions cannot have more than 8 options'];
    } else {
      // Validate each option
      const optionErrors = [];
      let hasValidOption = false;

      questionData.options.forEach((option, index) => {
        const optionError = {};

        // Option text validation
        if (!option.text || option.text.trim().length === 0) {
          optionError.text = 'Option text is required';
        } else if (option.text.trim().length > 200) {
          optionError.text = 'Option text must not exceed 200 characters';
        }

        // Option marks validation
        if (option.marks === undefined || option.marks === null || option.marks === '') {
          optionError.marks = 'Option marks are required';
        } else {
          // Handle edge cases for numeric input
          const marksStr = String(option.marks).trim();
          if (marksStr === '' || marksStr === 'null' || marksStr === 'undefined') {
            optionError.marks = 'Option marks are required';
          } else {
            const marks = parseInt(marksStr, 10);
            if (isNaN(marks) || !Number.isInteger(marks)) {
              optionError.marks = 'Option marks must be a whole number';
            } else if (marks < 0) {
              optionError.marks = 'Option marks cannot be negative';
            } else if (marks > 100) {
              optionError.marks = 'Option marks cannot exceed 100';
            } else {
              hasValidOption = true;
            }
          }
        }

        if (Object.keys(optionError).length > 0) {
          optionErrors[index] = optionError;
        }
      });

      if (optionErrors.length > 0) {
        errors.optionErrors = optionErrors;
      }

      if (!hasValidOption) {
        errors.options = ['At least one option must have valid marks'];
      }

      // Check for duplicate option texts
      const optionTexts = questionData.options
        .map(opt => opt.text?.trim().toLowerCase())
        .filter(text => text);

      const duplicateTexts = optionTexts.filter((text, index) =>
        optionTexts.indexOf(text) !== index
      );

      if (duplicateTexts.length > 0) {
        errors.options = ['Option texts must be unique within the question'];
      }
    }
  }

  return errors;
};

// Validate all questions in an assessment
const validateQuestions = (questionsData) => {
  const errors = {};

  if (!questionsData || !Array.isArray(questionsData)) {
    return { general: ['Questions data must be an array'] };
  }

  if (questionsData.length === 0) {
    return { general: ['At least one question is required'] };
  }

  if (questionsData.length > 50) {
    return { general: ['Assessment cannot have more than 50 questions'] };
  }

  // Validate each question
  questionsData.forEach((question, index) => {
    const questionErrors = validateQuestion(question);
    if (Object.keys(questionErrors).length > 0) {
      errors[index] = questionErrors;
    }
  });

  // Check for duplicate question texts
  const questionTexts = questionsData
    .map(q => q.questionText?.trim().toLowerCase())
    .filter(text => text);

  const duplicateTexts = questionTexts.filter((text, index) =>
    questionTexts.indexOf(text) !== index
  );

  if (duplicateTexts.length > 0) {
    errors.general = ['Question texts must be unique within the assessment'];
  }

  return errors;
};

// Validate complete assessment data
const validateCompleteAssessment = (assessmentData, questionsData) => {
  const assessmentErrors = validateAssessmentInfo(assessmentData);
  const questionErrors = validateQuestions(questionsData);

  return {
    assessment: assessmentErrors,
    questions: questionErrors,
    isValid: Object.keys(assessmentErrors).length === 0 && Object.keys(questionErrors).length === 0
  };
};

// Generate academic year options
const generateAcademicYearOptions = () => {
  const currentYear = new Date().getFullYear();
  const options = [];

  // Generate options from 2 years ago to 5 years in the future
  for (let year = currentYear - 2; year <= currentYear + 5; year++) {
    options.push({
      value: `${year}-${year + 1}`,
      label: `${year}-${year + 1}`
    });
  }

  return options;
};

// Course type options
const courseTypeOptions = [
  { value: 'Course 01', label: 'Course 01' },
  { value: 'Course 02', label: 'Course 02' },
  { value: 'Course 03', label: 'Course 03' },
  { value: 'Course 04', label: 'Course 04' }
];

// Assessment type options
const assessmentTypeOptions = [
  { value: 'Formative', label: 'Formative Assessment' },
  { value: 'Summative', label: 'Summative Assessment' },
  { value: 'Peer Review', label: 'Peer Review Assessment' }
];

// Sanitize assessment data for database storage
const sanitizeAssessmentData = (assessmentData) => {
  return {
    name: assessmentData.name?.trim(),
    shortDescription: assessmentData.shortDescription?.trim(),
    academicYear: assessmentData.academicYear,
    courseType: assessmentData.courseType,
    assessmentType: assessmentData.assessmentType,
    dueDate: assessmentData.dueDate,
    instructions: assessmentData.instructions?.trim() || null
  };
};

// Sanitize questions data for database storage
const sanitizeQuestionsData = (questionsData) => {
  return questionsData.map(question => ({
    questionText: question.questionText?.trim(),
    questionType: question.questionType || 'MCQ',
    options: question.options?.map(option => ({
      text: option.text?.trim(),
      marks: (() => {
        const marksStr = String(option.marks || 0).trim();
        const marks = parseInt(marksStr, 10);
        return isNaN(marks) ? 0 : marks;
      })()
    })) || []
  }));
};

// Format validation errors for display
const formatValidationErrors = (errors) => {
  const formatted = [];

  if (errors.assessment) {
    Object.entries(errors.assessment).forEach(([field, fieldErrors]) => {
      fieldErrors.forEach(error => {
        formatted.push(`Assessment ${field}: ${error}`);
      });
    });
  }

  if (errors.questions) {
    if (errors.questions.general) {
      errors.questions.general.forEach(error => {
        formatted.push(`Questions: ${error}`);
      });
    }

    Object.entries(errors.questions).forEach(([questionIndex, questionErrors]) => {
      if (questionIndex !== 'general') {
        const qIndex = parseInt(questionIndex, 10) + 1;
        Object.entries(questionErrors).forEach(([field, fieldErrors]) => {
          if (Array.isArray(fieldErrors)) {
            fieldErrors.forEach(error => {
              formatted.push(`Question ${qIndex} ${field}: ${error}`);
            });
          } else if (field === 'optionErrors') {
            Object.entries(fieldErrors).forEach(([optionIndex, optionErrors]) => {
              const oIndex = parseInt(optionIndex, 10) + 1;
              Object.entries(optionErrors).forEach(([optionField, optionError]) => {
                formatted.push(`Question ${qIndex} Option ${oIndex} ${optionField}: ${optionError}`);
              });
            });
          }
        });
      }
    });
  }

  return formatted;
};

const AssessmentInformationForm = ({
  assessmentData,
  onChange,
  validationErrors,
  academicYearOptions,
  courseTypeOptions,
  assessmentTypeOptions
}) => {
  const handleInputChange = (field, value) => {
    onChange({ [field]: value });
  };
  const formatDate = (dateString) => {
    if (!dateString) {
      return "";
    }
    const date = new Date(dateString);
    return date.toISOString().split("T")[0];
  };
  const handleDateChange = (value) => {
    handleInputChange("dueDate", value);
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 36,
          columnNumber: 11
        }, globalThis),
        "Assessment Information"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
        lineNumber: 35,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-1", children: "Provide basic information about your assessment. All fields marked with * are required." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
        lineNumber: 39,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
      lineNumber: 34,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "assessmentName", className: "block text-sm font-medium text-gray-700 mb-1", children: "Assessment Name *" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 47,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            type: "text",
            id: "assessmentName",
            value: assessmentData.name,
            onChange: (e) => handleInputChange("name", e.target.value),
            className: `input ${validationErrors.name ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
            placeholder: "Enter assessment name (3-100 characters)",
            maxLength: 100
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 50,
            columnNumber: 11
          },
          globalThis
        ),
        validationErrors.name && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 61,
            columnNumber: 15
          }, globalThis),
          validationErrors.name[0]
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 60,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: [
          assessmentData.name.length,
          "/100 characters"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 65,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
        lineNumber: 46,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "shortDescription", className: "block text-sm font-medium text-gray-700 mb-1", children: "Short Description *" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 72,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "textarea",
          {
            id: "shortDescription",
            rows: 3,
            value: assessmentData.shortDescription,
            onChange: (e) => handleInputChange("shortDescription", e.target.value),
            className: `input ${validationErrors.shortDescription ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
            placeholder: "Provide a brief description of the assessment (10-500 characters)",
            maxLength: 500
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 75,
            columnNumber: 11
          },
          globalThis
        ),
        validationErrors.shortDescription && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 86,
            columnNumber: 15
          }, globalThis),
          validationErrors.shortDescription[0]
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 85,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: [
          assessmentData.shortDescription.length,
          "/500 characters"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 90,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
        lineNumber: 71,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "academicYear", className: "block text-sm font-medium text-gray-700 mb-1", children: "Academic Year *" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 99,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              id: "academicYear",
              value: assessmentData.academicYear,
              onChange: (e) => handleInputChange("academicYear", e.target.value),
              className: `input ${validationErrors.academicYear ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "", children: "Select academic year" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
                  lineNumber: 108,
                  columnNumber: 15
                }, globalThis),
                academicYearOptions.map((option) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: option.value, children: option.label }, option.value, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
                  lineNumber: 110,
                  columnNumber: 17
                }, globalThis))
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 102,
              columnNumber: 13
            },
            globalThis
          ),
          validationErrors.academicYear && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 117,
              columnNumber: 17
            }, globalThis),
            validationErrors.academicYear[0]
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 116,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 98,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "courseType", className: "block text-sm font-medium text-gray-700 mb-1", children: "Course Type *" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 125,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              id: "courseType",
              value: assessmentData.courseType,
              onChange: (e) => handleInputChange("courseType", e.target.value),
              className: `input ${validationErrors.courseType ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "", children: "Select course type" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
                  lineNumber: 134,
                  columnNumber: 15
                }, globalThis),
                courseTypeOptions.map((option) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: option.value, children: option.label }, option.value, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
                  lineNumber: 136,
                  columnNumber: 17
                }, globalThis))
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 128,
              columnNumber: 13
            },
            globalThis
          ),
          validationErrors.courseType && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 143,
              columnNumber: 17
            }, globalThis),
            validationErrors.courseType[0]
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 142,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 124,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
        lineNumber: 96,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "assessmentType", className: "block text-sm font-medium text-gray-700 mb-1", children: "Assessment Type *" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 154,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              id: "assessmentType",
              value: assessmentData.assessmentType,
              onChange: (e) => handleInputChange("assessmentType", e.target.value),
              className: `input ${validationErrors.assessmentType ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "", children: "Select assessment type" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
                  lineNumber: 163,
                  columnNumber: 15
                }, globalThis),
                assessmentTypeOptions.map((option) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: option.value, children: option.label }, option.value, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
                  lineNumber: 165,
                  columnNumber: 17
                }, globalThis))
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 157,
              columnNumber: 13
            },
            globalThis
          ),
          validationErrors.assessmentType && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 172,
              columnNumber: 17
            }, globalThis),
            validationErrors.assessmentType[0]
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 171,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 153,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "dueDate", className: "block text-sm font-medium text-gray-700 mb-1", children: "Due Date *" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 180,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Calendar, { className: "h-4 w-4 text-gray-400" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 185,
              columnNumber: 17
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 184,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "date",
                id: "dueDate",
                value: formatDate(assessmentData.dueDate),
                onChange: (e) => handleDateChange(e.target.value),
                className: `input pl-10 ${validationErrors.dueDate ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
                min: (/* @__PURE__ */ new Date()).toISOString().split("T")[0]
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
                lineNumber: 187,
                columnNumber: 15
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 183,
            columnNumber: 13
          }, globalThis),
          validationErrors.dueDate && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 198,
              columnNumber: 17
            }, globalThis),
            validationErrors.dueDate[0]
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 197,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 179,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
        lineNumber: 151,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "instructions", className: "block text-sm font-medium text-gray-700 mb-1", children: "Instructions (Optional)" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 207,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "textarea",
          {
            id: "instructions",
            rows: 6,
            value: assessmentData.instructions,
            onChange: (e) => handleInputChange("instructions", e.target.value),
            className: `input ${validationErrors.instructions ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
            placeholder: "Provide detailed instructions for students taking this assessment...",
            maxLength: 2e3
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 210,
            columnNumber: 11
          },
          globalThis
        ),
        validationErrors.instructions && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 221,
            columnNumber: 15
          }, globalThis),
          validationErrors.instructions[0]
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 220,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: [
          assessmentData.instructions.length,
          "/2000 characters"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 225,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
        lineNumber: 206,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-blue-50 border border-blue-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Info, { className: "h-5 w-5 text-blue-400 mt-0.5 mr-3" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 233,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-blue-900", children: "Assessment Information" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 235,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-blue-800 mt-1 space-y-1", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Assessment names must be unique within your institution" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 237,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Due dates must be set in the future for student access" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 238,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Assessment type determines how peer reviews are conducted" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 239,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Instructions will be displayed to students before they begin" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
              lineNumber: 240,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
            lineNumber: 236,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
          lineNumber: 234,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
        lineNumber: 232,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
        lineNumber: 231,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
      lineNumber: 44,
      columnNumber: 7
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentInformationForm.jsx",
    lineNumber: 33,
    columnNumber: 5
  }, globalThis);
};

const QuestionManagement = ({ questionsData, onChange, validationErrors }) => {
  const [expandedQuestions, setExpandedQuestions] = reactExports.useState(/* @__PURE__ */ new Set([0]));
  const [draggedQuestion, setDraggedQuestion] = reactExports.useState(null);
  const addQuestion = () => {
    const newQuestion = {
      id: Date.now(),
      questionText: "",
      questionType: "MCQ",
      options: [
        { id: Date.now() + 1, text: "", marks: 0 },
        { id: Date.now() + 2, text: "", marks: 0 }
      ]
    };
    const newQuestions = [...questionsData, newQuestion];
    onChange(newQuestions);
    setExpandedQuestions((prev) => /* @__PURE__ */ new Set([...prev, newQuestions.length - 1]));
  };
  const removeQuestion = (index) => {
    if (questionsData.length <= 1) {
      return;
    }
    const newQuestions = questionsData.filter((_, i) => i !== index);
    onChange(newQuestions);
    setExpandedQuestions((prev) => {
      const newSet = /* @__PURE__ */ new Set();
      prev.forEach((i) => {
        if (i < index) {
          newSet.add(i);
        } else if (i > index) {
          newSet.add(i - 1);
        }
      });
      return newSet;
    });
  };
  const duplicateQuestion = (index) => {
    const questionToDuplicate = questionsData[index];
    const newQuestion = {
      ...questionToDuplicate,
      id: Date.now(),
      questionText: questionToDuplicate.questionText + " (Copy)",
      options: questionToDuplicate.options.map((option) => ({
        ...option,
        id: Date.now() + Math.random()
      }))
    };
    const newQuestions = [...questionsData];
    newQuestions.splice(index + 1, 0, newQuestion);
    onChange(newQuestions);
    setExpandedQuestions((prev) => /* @__PURE__ */ new Set([...prev, index + 1]));
  };
  const updateQuestion = (index, field, value) => {
    const newQuestions = [...questionsData];
    newQuestions[index] = { ...newQuestions[index], [field]: value };
    onChange(newQuestions);
  };
  const addOption = (questionIndex) => {
    const newQuestions = [...questionsData];
    const question = newQuestions[questionIndex];
    if (question.options.length >= 8) {
      return;
    }
    question.options.push({
      id: Date.now(),
      text: "",
      marks: 0
    });
    onChange(newQuestions);
  };
  const removeOption = (questionIndex, optionIndex) => {
    const newQuestions = [...questionsData];
    const question = newQuestions[questionIndex];
    if (question.options.length <= 2) {
      return;
    }
    question.options.splice(optionIndex, 1);
    onChange(newQuestions);
  };
  const updateOption = (questionIndex, optionIndex, field, value) => {
    const newQuestions = [...questionsData];
    const option = newQuestions[questionIndex].options[optionIndex];
    option[field] = field === "marks" ? value === "" ? 0 : Number(value) : value;
    onChange(newQuestions);
  };
  const moveOption = (questionIndex, optionIndex, direction) => {
    const newQuestions = [...questionsData];
    const options = newQuestions[questionIndex].options;
    const newIndex = direction === "up" ? optionIndex - 1 : optionIndex + 1;
    if (newIndex >= 0 && newIndex < options.length) {
      [options[optionIndex], options[newIndex]] = [options[newIndex], options[optionIndex]];
      onChange(newQuestions);
    }
  };
  const toggleQuestionExpansion = (index) => {
    setExpandedQuestions((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };
  const handleDragStart = (e, index) => {
    setDraggedQuestion(index);
    e.dataTransfer.effectAllowed = "move";
  };
  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };
  const handleDrop = (e, dropIndex) => {
    e.preventDefault();
    if (draggedQuestion === null || draggedQuestion === dropIndex) {
      setDraggedQuestion(null);
      return;
    }
    const newQuestions = [...questionsData];
    const draggedItem = newQuestions[draggedQuestion];
    newQuestions.splice(draggedQuestion, 1);
    const insertIndex = draggedQuestion < dropIndex ? dropIndex - 1 : dropIndex;
    newQuestions.splice(insertIndex, 0, draggedItem);
    onChange(newQuestions);
    setDraggedQuestion(null);
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(HelpCircle, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
              lineNumber: 179,
              columnNumber: 15
            }, globalThis),
            "Assessment Questions"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
            lineNumber: 178,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-1", children: "Create Multiple Choice Questions (MCQ) for your assessment. Each question can have 2-8 options with different mark allocations." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
            lineNumber: 182,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
          lineNumber: 177,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: addQuestion,
            disabled: questionsData.length >= 50,
            className: "btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Plus, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                lineNumber: 191,
                columnNumber: 13
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Add Question" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                lineNumber: 192,
                columnNumber: 13
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
            lineNumber: 186,
            columnNumber: 11
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
        lineNumber: 176,
        columnNumber: 9
      }, globalThis),
      questionsData.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-2 text-sm text-gray-500", children: [
        questionsData.length,
        " question",
        questionsData.length !== 1 ? "s" : "",
        " created (max 50)"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
        lineNumber: 197,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
      lineNumber: 175,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-4", children: questionsData.map((question, questionIndex) => {
      const questionErrors = validationErrors[questionIndex] || {};
      const isExpanded = expandedQuestions.has(questionIndex);
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "div",
        {
          className: `border rounded-lg ${questionErrors && Object.keys(questionErrors).length > 0 ? "border-red-300" : "border-gray-200"}`,
          draggable: true,
          onDragStart: (e) => handleDragStart(e, questionIndex),
          onDragOver: handleDragOver,
          onDrop: (e) => handleDrop(e, questionIndex),
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-4 bg-gray-50 border-b border-gray-200 flex items-center justify-between", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(GripVertical, { className: "h-5 w-5 text-gray-400 cursor-move" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 221,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-gray-900", children: [
                  "Question ",
                  questionIndex + 1
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 222,
                  columnNumber: 19
                }, globalThis),
                questionErrors && Object.keys(questionErrors).length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 text-red-500" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 226,
                  columnNumber: 21
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                lineNumber: 220,
                columnNumber: 17
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "button",
                  {
                    onClick: () => duplicateQuestion(questionIndex),
                    className: "text-gray-400 hover:text-gray-600",
                    title: "Duplicate Question",
                    children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Copy, { className: "h-4 w-4" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                      lineNumber: 236,
                      columnNumber: 21
                    }, globalThis)
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                    lineNumber: 231,
                    columnNumber: 19
                  },
                  globalThis
                ),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "button",
                  {
                    onClick: () => toggleQuestionExpansion(questionIndex),
                    className: "text-gray-400 hover:text-gray-600",
                    title: isExpanded ? "Collapse" : "Expand",
                    children: isExpanded ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(EyeOff, { className: "h-4 w-4" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                      lineNumber: 243,
                      columnNumber: 35
                    }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Eye, { className: "h-4 w-4" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                      lineNumber: 243,
                      columnNumber: 68
                    }, globalThis)
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                    lineNumber: 238,
                    columnNumber: 19
                  },
                  globalThis
                ),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "button",
                  {
                    onClick: () => removeQuestion(questionIndex),
                    disabled: questionsData.length <= 1,
                    className: "text-red-400 hover:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed",
                    title: "Remove Question",
                    children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Trash2, { className: "h-4 w-4" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                      lineNumber: 251,
                      columnNumber: 21
                    }, globalThis)
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                    lineNumber: 245,
                    columnNumber: 19
                  },
                  globalThis
                )
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                lineNumber: 230,
                columnNumber: 17
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
              lineNumber: 219,
              columnNumber: 15
            }, globalThis),
            isExpanded && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-4 space-y-4", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Question Text *" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 261,
                  columnNumber: 21
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "textarea",
                  {
                    rows: 3,
                    value: question.questionText,
                    onChange: (e) => updateQuestion(questionIndex, "questionText", e.target.value),
                    className: `input ${questionErrors.questionText ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
                    placeholder: "Enter your question here...",
                    maxLength: 1e3
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                    lineNumber: 264,
                    columnNumber: 21
                  },
                  globalThis
                ),
                questionErrors.questionText && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600 flex items-center", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 mr-1" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                    lineNumber: 274,
                    columnNumber: 25
                  }, globalThis),
                  questionErrors.questionText[0]
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 273,
                  columnNumber: 23
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: [
                  question.questionText.length,
                  "/1000 characters"
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 278,
                  columnNumber: 21
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                lineNumber: 260,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Question Type" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 285,
                  columnNumber: 21
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "select",
                  {
                    value: question.questionType,
                    onChange: (e) => updateQuestion(questionIndex, "questionType", e.target.value),
                    className: "input",
                    disabled: true,
                    children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "MCQ", children: "Multiple Choice Question" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                      lineNumber: 294,
                      columnNumber: 23
                    }, globalThis)
                  },
                  void 0,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                    lineNumber: 288,
                    columnNumber: 21
                  },
                  globalThis
                ),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "Currently only MCQ questions are supported" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 296,
                  columnNumber: 21
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                lineNumber: 284,
                columnNumber: 19
              }, globalThis),
              question.questionType === "MCQ" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mb-3", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700", children: "Answer Options *" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                    lineNumber: 305,
                    columnNumber: 25
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                    "button",
                    {
                      onClick: () => addOption(questionIndex),
                      disabled: question.options.length >= 8,
                      className: "btn-outline text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed",
                      children: [
                        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Plus, { className: "h-3 w-3" }, void 0, false, {
                          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                          lineNumber: 313,
                          columnNumber: 27
                        }, globalThis),
                        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Add Option" }, void 0, false, {
                          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                          lineNumber: 314,
                          columnNumber: 27
                        }, globalThis)
                      ]
                    },
                    void 0,
                    true,
                    {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                      lineNumber: 308,
                      columnNumber: 25
                    },
                    globalThis
                  )
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 304,
                  columnNumber: 23
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-3", children: question.options.map((option, optionIndex) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "div",
                  {
                    className: `border rounded-md p-3 ${questionErrors.optionErrors && questionErrors.optionErrors[optionIndex] ? "border-red-300 bg-red-50" : "border-gray-200"}`,
                    children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-start space-x-3", children: [
                      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600 mt-1", children: String.fromCharCode(65 + optionIndex) }, void 0, false, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                        lineNumber: 330,
                        columnNumber: 31
                      }, globalThis),
                      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1 space-y-3", children: [
                        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
                          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                            "input",
                            {
                              type: "text",
                              value: option.text,
                              onChange: (e) => updateOption(questionIndex, optionIndex, "text", e.target.value),
                              className: `input ${questionErrors.optionErrors && questionErrors.optionErrors[optionIndex]?.text ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
                              placeholder: `Option ${String.fromCharCode(65 + optionIndex)} text`,
                              maxLength: 200
                            },
                            void 0,
                            false,
                            {
                              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                              lineNumber: 338,
                              columnNumber: 35
                            },
                            globalThis
                          ),
                          questionErrors.optionErrors && questionErrors.optionErrors[optionIndex]?.text && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600", children: questionErrors.optionErrors[optionIndex].text }, void 0, false, {
                            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                            lineNumber: 351,
                            columnNumber: 37
                          }, globalThis)
                        ] }, void 0, true, {
                          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                          lineNumber: 337,
                          columnNumber: 33
                        }, globalThis),
                        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: [
                          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-xs font-medium text-gray-700 mb-1", children: "Marks" }, void 0, false, {
                            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                            lineNumber: 360,
                            columnNumber: 37
                          }, globalThis),
                          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                            "input",
                            {
                              type: "number",
                              min: "0",
                              max: "100",
                              value: option.marks,
                              onChange: (e) => updateOption(questionIndex, optionIndex, "marks", e.target.value),
                              className: `input ${questionErrors.optionErrors && questionErrors.optionErrors[optionIndex]?.marks ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
                              placeholder: "0"
                            },
                            void 0,
                            false,
                            {
                              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                              lineNumber: 363,
                              columnNumber: 37
                            },
                            globalThis
                          ),
                          questionErrors.optionErrors && questionErrors.optionErrors[optionIndex]?.marks && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600", children: questionErrors.optionErrors[optionIndex].marks }, void 0, false, {
                            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                            lineNumber: 377,
                            columnNumber: 39
                          }, globalThis)
                        ] }, void 0, true, {
                          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                          lineNumber: 359,
                          columnNumber: 35
                        }, globalThis) }, void 0, false, {
                          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                          lineNumber: 358,
                          columnNumber: 33
                        }, globalThis)
                      ] }, void 0, true, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                        lineNumber: 335,
                        columnNumber: 31
                      }, globalThis),
                      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0 flex flex-col space-y-1", children: [
                        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                          "button",
                          {
                            onClick: () => moveOption(questionIndex, optionIndex, "up"),
                            disabled: optionIndex === 0,
                            className: "text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",
                            title: "Move Up",
                            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Move, { className: "h-4 w-4 transform rotate-180" }, void 0, false, {
                              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                              lineNumber: 393,
                              columnNumber: 35
                            }, globalThis)
                          },
                          void 0,
                          false,
                          {
                            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                            lineNumber: 387,
                            columnNumber: 33
                          },
                          globalThis
                        ),
                        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                          "button",
                          {
                            onClick: () => moveOption(questionIndex, optionIndex, "down"),
                            disabled: optionIndex === question.options.length - 1,
                            className: "text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",
                            title: "Move Down",
                            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Move, { className: "h-4 w-4" }, void 0, false, {
                              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                              lineNumber: 401,
                              columnNumber: 35
                            }, globalThis)
                          },
                          void 0,
                          false,
                          {
                            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                            lineNumber: 395,
                            columnNumber: 33
                          },
                          globalThis
                        ),
                        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                          "button",
                          {
                            onClick: () => removeOption(questionIndex, optionIndex),
                            disabled: question.options.length <= 2,
                            className: "text-red-400 hover:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed",
                            title: "Remove Option",
                            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Trash2, { className: "h-4 w-4" }, void 0, false, {
                              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                              lineNumber: 409,
                              columnNumber: 35
                            }, globalThis)
                          },
                          void 0,
                          false,
                          {
                            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                            lineNumber: 403,
                            columnNumber: 33
                          },
                          globalThis
                        )
                      ] }, void 0, true, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                        lineNumber: 386,
                        columnNumber: 31
                      }, globalThis)
                    ] }, void 0, true, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                      lineNumber: 328,
                      columnNumber: 29
                    }, globalThis)
                  },
                  option.id || optionIndex,
                  false,
                  {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                    lineNumber: 320,
                    columnNumber: 27
                  },
                  globalThis
                )) }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 318,
                  columnNumber: 23
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-3 text-sm text-gray-500", children: [
                  question.options.length,
                  "/8 options • Minimum 2, Maximum 8 options per question"
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 418,
                  columnNumber: 23
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 bg-blue-50 border border-blue-200 rounded-md p-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-4 w-4 text-blue-400 mt-0.5 mr-2" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                    lineNumber: 425,
                    columnNumber: 27
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-blue-800", children: [
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "font-medium", children: "Peer Review MCQ Guidelines:" }, void 0, false, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                      lineNumber: 427,
                      columnNumber: 29
                    }, globalThis),
                    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "mt-1 space-y-1", children: [
                      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• All options are considered valid answers in peer review context" }, void 0, false, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                        lineNumber: 429,
                        columnNumber: 31
                      }, globalThis),
                      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Assign marks based on the quality or depth of the response" }, void 0, false, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                        lineNumber: 430,
                        columnNumber: 31
                      }, globalThis),
                      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Higher marks can indicate more comprehensive or insightful answers" }, void 0, false, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                        lineNumber: 431,
                        columnNumber: 31
                      }, globalThis),
                      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Students will see all options and their associated marks" }, void 0, false, {
                        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                        lineNumber: 432,
                        columnNumber: 31
                      }, globalThis)
                    ] }, void 0, true, {
                      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                      lineNumber: 428,
                      columnNumber: 29
                    }, globalThis)
                  ] }, void 0, true, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                    lineNumber: 426,
                    columnNumber: 27
                  }, globalThis)
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 424,
                  columnNumber: 25
                }, globalThis) }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                  lineNumber: 423,
                  columnNumber: 23
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
                lineNumber: 303,
                columnNumber: 21
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
              lineNumber: 258,
              columnNumber: 17
            }, globalThis)
          ]
        },
        question.id || questionIndex,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
          lineNumber: 210,
          columnNumber: 13
        },
        globalThis
      );
    }) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
      lineNumber: 204,
      columnNumber: 7
    }, globalThis),
    questionsData.length === 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-12 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(HelpCircle, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
        lineNumber: 449,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No questions yet" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
        lineNumber: 450,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "Get started by adding your first question to the assessment." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
        lineNumber: 451,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: addQuestion,
          className: "btn-primary flex items-center space-x-2 mx-auto",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Plus, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
              lineNumber: 459,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Add First Question" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
              lineNumber: 460,
              columnNumber: 15
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
          lineNumber: 455,
          columnNumber: 13
        },
        globalThis
      ) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
        lineNumber: 454,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
      lineNumber: 448,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/QuestionManagement.jsx",
    lineNumber: 174,
    columnNumber: 5
  }, globalThis);
};

const AssessmentReview = ({
  assessmentData,
  questionsData,
  validationErrors,
  onCreateAssessment,
  creating
}) => {
  const [showPreview, setShowPreview] = reactExports.useState(false);
  const hasErrors = Object.keys(validationErrors).length > 0;
  const totalMarks = questionsData.reduce((total, question) => {
    return total + question.options.reduce((qTotal, option) => Math.max(qTotal, option.marks), 0);
  }, 0);
  const formatDate = (dateString) => {
    if (!dateString) {
      return "Not set";
    }
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  };
  const getAssessmentTypeDescription = (type) => {
    switch (type) {
      case "Formative":
        return "Ongoing assessment for learning and feedback";
      case "Summative":
        return "Assessment of learning at the end of instruction";
      case "Peer Review":
        return "Students assess and provide feedback on peer work";
      default:
        return "Assessment type not specified";
    }
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Eye, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 54,
          columnNumber: 11
        }, globalThis),
        "Review & Publish Assessment"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 53,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-1", children: "Review your assessment details and questions before publishing. Once published, the assessment will be available for assignment to student batches." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 57,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
      lineNumber: 52,
      columnNumber: 7
    }, globalThis),
    hasErrors ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-6 bg-red-50 border border-red-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5 text-red-400 mt-0.5 mr-3" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 66,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-red-900 mb-2", children: "Please fix the following errors before publishing:" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 68,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-red-800 space-y-1", children: formatValidationErrors({
          assessment: validationErrors,
          questions: validationErrors
        }).map((error, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
          "• ",
          error
        ] }, index, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 76,
          columnNumber: 19
        }, globalThis)) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 71,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 67,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
      lineNumber: 65,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
      lineNumber: 64,
      columnNumber: 9
    }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-6 bg-green-50 border border-green-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-green-400 mt-0.5 mr-3" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 85,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-green-900", children: "Assessment is ready for publishing" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 87,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-green-800 mt-1", children: "All required fields are completed and validation checks have passed." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 90,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 86,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
      lineNumber: 84,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
      lineNumber: 83,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border border-gray-200 rounded-lg mb-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-6 py-4 border-b border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-md font-medium text-gray-900 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "h-4 w-4 mr-2" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 102,
          columnNumber: 13
        }, globalThis),
        "Assessment Summary"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 101,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 100,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6 space-y-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-700 mb-2", children: "Assessment Name" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 111,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-900", children: assessmentData.name || "Not specified" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 112,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 110,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-700 mb-2", children: "Assessment Type" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 115,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-900", children: assessmentData.assessmentType || "Not specified" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 116,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-500 mt-1", children: getAssessmentTypeDescription(assessmentData.assessmentType) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 119,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 114,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-700 mb-2", children: "Academic Year" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 124,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-900", children: assessmentData.academicYear || "Not specified" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 125,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 123,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-700 mb-2", children: "Course Type" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 128,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-900", children: assessmentData.courseType || "Not specified" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 129,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 127,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-700 mb-2", children: "Due Date" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 132,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-900 flex items-center", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Calendar, { className: "h-4 w-4 mr-1 text-gray-400" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
                lineNumber: 134,
                columnNumber: 17
              }, globalThis),
              formatDate(assessmentData.dueDate)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 133,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 131,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-700 mb-2", children: "Total Questions" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 139,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-900 flex items-center", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(HelpCircle, { className: "h-4 w-4 mr-1 text-gray-400" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
                lineNumber: 141,
                columnNumber: 17
              }, globalThis),
              questionsData.length,
              " question",
              questionsData.length !== 1 ? "s" : ""
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 140,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 138,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 109,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-700 mb-2", children: "Description" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 149,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-900 bg-gray-50 p-3 rounded-md", children: assessmentData.shortDescription || "No description provided" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 150,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 148,
          columnNumber: 11
        }, globalThis),
        assessmentData.instructions && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-700 mb-2", children: "Instructions" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 158,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-900 bg-gray-50 p-3 rounded-md whitespace-pre-wrap", children: assessmentData.instructions }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 159,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 157,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 107,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
      lineNumber: 99,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border border-gray-200 rounded-lg mb-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-6 py-4 border-b border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-md font-medium text-gray-900 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(HelpCircle, { className: "h-4 w-4 mr-2" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 172,
            columnNumber: 15
          }, globalThis),
          "Questions Overview"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 171,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setShowPreview(!showPreview),
            className: "btn-outline text-sm flex items-center space-x-1",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Eye, { className: "h-3 w-3" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
                lineNumber: 179,
                columnNumber: 15
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: [
                showPreview ? "Hide" : "Show",
                " Preview"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
                lineNumber: 180,
                columnNumber: 15
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 175,
            columnNumber: 13
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 170,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 169,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6 mb-6", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-blue-600", children: questionsData.length }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 189,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Total Questions" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 190,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 188,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-green-600", children: totalMarks }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 193,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Maximum Marks" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 194,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 192,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-2xl font-bold text-purple-600", children: questionsData.reduce((total, q) => total + q.options.length, 0) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 197,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: "Total Options" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 200,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 196,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 187,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-3", children: questionsData.map((question, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "border border-gray-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-start", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-1", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h6", { className: "text-sm font-medium text-gray-900", children: [
              "Question ",
              index + 1
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 210,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-1 line-clamp-2", children: question.questionText || "No question text" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 213,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 209,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-right text-sm text-gray-500", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              question.options.length,
              " options"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 218,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
              "Max: ",
              Math.max(...question.options.map((o) => o.marks)),
              " marks"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 219,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 217,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 208,
          columnNumber: 17
        }, globalThis) }, index, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 207,
          columnNumber: 15
        }, globalThis)) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 205,
          columnNumber: 11
        }, globalThis),
        showPreview && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-6 border-t border-gray-200 pt-6", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900 mb-4", children: "Question Preview" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 229,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: questionsData.map((question, questionIndex) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-gray-50 border border-gray-200 rounded-md p-4", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-3", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h6", { className: "text-sm font-medium text-gray-900", children: [
                "Question ",
                questionIndex + 1
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
                lineNumber: 234,
                columnNumber: 23
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-700 mt-1", children: question.questionText }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
                lineNumber: 237,
                columnNumber: 23
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 233,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-2", children: question.options.map((option, optionIndex) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between bg-white border border-gray-200 rounded p-2", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600", children: String.fromCharCode(65 + optionIndex) }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
                  lineNumber: 246,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm text-gray-900", children: option.text }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
                  lineNumber: 249,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
                lineNumber: 245,
                columnNumber: 27
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm font-medium text-gray-600", children: [
                option.marks,
                " marks"
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
                lineNumber: 251,
                columnNumber: 27
              }, globalThis)
            ] }, optionIndex, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 244,
              columnNumber: 25
            }, globalThis)) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
              lineNumber: 242,
              columnNumber: 21
            }, globalThis)
          ] }, questionIndex, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 232,
            columnNumber: 19
          }, globalThis)) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 230,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 228,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 185,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
      lineNumber: 168,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-blue-50 border border-blue-200 rounded-md p-4 mb-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Info, { className: "h-5 w-5 text-blue-400 mt-0.5 mr-3" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 268,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-blue-900", children: "Publishing Information" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 270,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-blue-800 mt-1 space-y-1", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Once published, the assessment will be available for assignment to student batches" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 272,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• You can assign the assessment to multiple batches after publishing" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 273,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Students will be able to access the assessment once it's assigned to their batch" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 274,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "• Assessment questions and options cannot be modified after publishing" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 275,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 271,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 269,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
      lineNumber: 267,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
      lineNumber: 266,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-center", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      "button",
      {
        onClick: onCreateAssessment,
        disabled: hasErrors || creating,
        className: "btn-primary flex items-center space-x-2 px-8 py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed",
        children: creating ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-5 w-5 border-b-2 border-white" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 290,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Publishing Assessment..." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 291,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 289,
          columnNumber: 13
        }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 295,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Publish Assessment" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
            lineNumber: 296,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
          lineNumber: 294,
          columnNumber: 13
        }, globalThis)
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
        lineNumber: 283,
        columnNumber: 9
      },
      globalThis
    ) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
      lineNumber: 282,
      columnNumber: 7
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/AssessmentReview.jsx",
    lineNumber: 51,
    columnNumber: 5
  }, globalThis);
};

const SuperAdminAuth = ({ onAuthenticate, loading, user }) => {
  const [credentials, setCredentials] = reactExports.useState({
    adminId: user?.id || "",
    password: ""
  });
  const [showPassword, setShowPassword] = reactExports.useState(false);
  const [errors, setErrors] = reactExports.useState({});
  const validateForm = () => {
    const newErrors = {};
    if (!credentials.adminId) {
      newErrors.adminId = "Super Admin ID is required";
    } else if (credentials.adminId !== user?.id) {
      newErrors.adminId = "Admin ID does not match current user";
    }
    if (!credentials.password) {
      newErrors.password = "Password is required";
    } else if (credentials.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onAuthenticate(credentials);
    }
  };
  const handleInputChange = (field, value) => {
    setCredentials((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-md w-full space-y-8", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Shield, { className: "h-8 w-8 text-red-600" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 60,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 59,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "mt-6 text-center text-3xl font-extrabold text-gray-900", children: "Super Admin Authentication" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 62,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-2 text-center text-sm text-gray-600", children: "Additional verification required to create assessments" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 65,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5 text-yellow-400 mt-0.5 mr-3" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
          lineNumber: 70,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-yellow-900", children: "Security Notice" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 72,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-yellow-800 mt-1", children: "Assessment creation requires Super Admin privileges. Please verify your identity to proceed." }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 73,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
          lineNumber: 71,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 69,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 68,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
      lineNumber: 58,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("form", { className: "mt-8 space-y-6", onSubmit: handleSubmit, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "adminId", className: "block text-sm font-medium text-gray-700 mb-1", children: "Super Admin ID" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 85,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(User, { className: "h-5 w-5 text-gray-400" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
              lineNumber: 90,
              columnNumber: 19
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
              lineNumber: 89,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                id: "adminId",
                name: "adminId",
                type: "text",
                value: credentials.adminId,
                onChange: (e) => handleInputChange("adminId", e.target.value),
                className: `input pl-10 ${errors.adminId ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
                placeholder: "Enter your Super Admin ID",
                disabled: loading
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
                lineNumber: 92,
                columnNumber: 17
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 88,
            columnNumber: 15
          }, globalThis),
          errors.adminId && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600", children: errors.adminId }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 104,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
          lineNumber: 84,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "password", className: "block text-sm font-medium text-gray-700 mb-1", children: "Password" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 110,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Lock, { className: "h-5 w-5 text-gray-400" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
              lineNumber: 115,
              columnNumber: 19
            }, globalThis) }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
              lineNumber: 114,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                id: "password",
                name: "password",
                type: showPassword ? "text" : "password",
                value: credentials.password,
                onChange: (e) => handleInputChange("password", e.target.value),
                className: `input pl-10 pr-10 ${errors.password ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""}`,
                placeholder: "Enter your password",
                disabled: loading
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
                lineNumber: 117,
                columnNumber: 17
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                type: "button",
                className: "absolute inset-y-0 right-0 pr-3 flex items-center",
                onClick: () => setShowPassword(!showPassword),
                disabled: loading,
                children: showPassword ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(EyeOff, { className: "h-5 w-5 text-gray-400 hover:text-gray-600" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
                  lineNumber: 134,
                  columnNumber: 21
                }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Eye, { className: "h-5 w-5 text-gray-400 hover:text-gray-600" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
                  lineNumber: 136,
                  columnNumber: 21
                }, globalThis)
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
                lineNumber: 127,
                columnNumber: 17
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 113,
            columnNumber: 15
          }, globalThis),
          errors.password && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-red-600", children: errors.password }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 141,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
          lineNumber: 109,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 82,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-blue-50 border border-blue-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-blue-400 mt-0.5 mr-3" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
          lineNumber: 149,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-blue-900", children: "Current User" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 151,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-blue-800 mt-1", children: [
            "Logged in as: ",
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: [
              user?.first_name,
              " ",
              user?.last_name
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
              lineNumber: 153,
              columnNumber: 33
            }, globalThis),
            " (",
            user?.email,
            ")"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 152,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-blue-800", children: [
            "Role: ",
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: user?.role }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
              lineNumber: 156,
              columnNumber: 25
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 155,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
          lineNumber: 150,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 148,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 147,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          type: "submit",
          disabled: loading,
          className: "group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed",
          children: loading ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
              lineNumber: 171,
              columnNumber: 19
            }, globalThis),
            "Authenticating..."
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 170,
            columnNumber: 17
          }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Shield, { className: "h-4 w-4 mr-2" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
              lineNumber: 176,
              columnNumber: 19
            }, globalThis),
            "Authenticate & Continue"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 175,
            columnNumber: 17
          }, globalThis)
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
          lineNumber: 164,
          columnNumber: 13
        },
        globalThis
      ) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 163,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-gray-50 border border-gray-200 rounded-md p-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-gray-900 mb-2", children: "Security Information" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
          lineNumber: 186,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-gray-600 space-y-1", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Two-factor authentication ensures only authorized Super Admins can create assessments" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 188,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Your credentials are verified against the current session" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 189,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• This additional step protects sensitive assessment creation functionality" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 190,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• All authentication attempts are logged for security auditing" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
            lineNumber: 191,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
          lineNumber: 187,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 185,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
        lineNumber: 184,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
      lineNumber: 81,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
    lineNumber: 57,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/assessment/SuperAdminAuth.jsx",
    lineNumber: 56,
    columnNumber: 5
  }, globalThis);
};

const CreateAssessment = () => {
  const navigate = useNavigate();
  const {
    createAssessmentWithQuestions,
    checkAssessmentNameUnique,
    getInstitution
  } = useDatabase();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = reactExports.useState(0);
  const [isAuthenticated, setIsAuthenticated] = reactExports.useState(false);
  const [authLoading, setAuthLoading] = reactExports.useState(false);
  const [assessmentData, setAssessmentData] = reactExports.useState({
    name: "",
    shortDescription: "",
    academicYear: "",
    courseType: "",
    assessmentType: "",
    dueDate: "",
    instructions: ""
  });
  const [questionsData, setQuestionsData] = reactExports.useState([]);
  const [saving, setSaving] = reactExports.useState(false);
  const [message, setMessage] = reactExports.useState({ type: "", text: "" });
  const [validationErrors, setValidationErrors] = reactExports.useState({});
  const [institution, setInstitution] = reactExports.useState(null);
  const [lastSaved, setLastSaved] = reactExports.useState(null);
  const steps = [
    {
      id: "info",
      title: "Assessment Details",
      description: "Basic assessment information and configuration",
      icon: FileText
    },
    {
      id: "questions",
      title: "Questions",
      description: "Create and manage assessment questions",
      icon: Users
    },
    {
      id: "review",
      title: "Review & Publish",
      description: "Review assessment and publish for use",
      icon: Eye
    }
  ];
  reactExports.useEffect(() => {
    loadInstitution();
    loadDraftData();
    const timer = setInterval(autoSave, 3e4);
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, []);
  reactExports.useEffect(() => {
    setValidationErrors({});
  }, [assessmentData, questionsData]);
  const loadInstitution = async () => {
    try {
      const institutionData = await getInstitution();
      setInstitution(institutionData);
    } catch (error) {
      console.error("Failed to load institution:", error);
    }
  };
  const loadDraftData = () => {
    try {
      const savedAssessment = localStorage.getItem("createAssessment_draft_assessment");
      const savedQuestions = localStorage.getItem("createAssessment_draft_questions");
      const savedStep = localStorage.getItem("createAssessment_draft_step");
      if (savedAssessment) {
        setAssessmentData(JSON.parse(savedAssessment));
      }
      if (savedQuestions) {
        setQuestionsData(JSON.parse(savedQuestions));
      }
      if (savedStep) {
        setCurrentStep(parseInt(savedStep));
      }
      const lastSavedTime = localStorage.getItem("createAssessment_draft_timestamp");
      if (lastSavedTime) {
        setLastSaved(new Date(lastSavedTime));
      }
    } catch (error) {
      console.error("Failed to load draft data:", error);
    }
  };
  const autoSave = () => {
    if (assessmentData.name || questionsData.length > 0) {
      saveDraft();
    }
  };
  const saveDraft = () => {
    try {
      localStorage.setItem("createAssessment_draft_assessment", JSON.stringify(assessmentData));
      localStorage.setItem("createAssessment_draft_questions", JSON.stringify(questionsData));
      localStorage.setItem("createAssessment_draft_step", currentStep.toString());
      localStorage.setItem("createAssessment_draft_timestamp", (/* @__PURE__ */ new Date()).toISOString());
      setLastSaved(/* @__PURE__ */ new Date());
    } catch (error) {
      console.error("Failed to save draft:", error);
    }
  };
  const clearDraft = () => {
    try {
      localStorage.removeItem("createAssessment_draft_assessment");
      localStorage.removeItem("createAssessment_draft_questions");
      localStorage.removeItem("createAssessment_draft_step");
      localStorage.removeItem("createAssessment_draft_timestamp");
      setLastSaved(null);
    } catch (error) {
      console.error("Failed to clear draft:", error);
    }
  };
  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: "", text: "" }), 5e3);
  };
  const handleSuperAdminAuth = async (credentials) => {
    setAuthLoading(true);
    try {
      if (credentials.adminId !== user.id || !credentials.password) {
        throw new Error("Invalid Super Admin credentials");
      }
      if (user.role !== "super_admin") {
        throw new Error("Only Super Admins can create assessments");
      }
      setIsAuthenticated(true);
      showMessage("success", "Authentication successful");
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setAuthLoading(false);
    }
  };
  const validateCurrentStep = () => {
    let errors = {};
    switch (currentStep) {
      case 0:
        errors = validateAssessmentInfo(assessmentData);
        break;
      case 1:
        errors = validateQuestions(questionsData);
        break;
      case 2:
        const completeValidation = validateCompleteAssessment(assessmentData, questionsData);
        errors = { ...completeValidation.assessment, ...completeValidation.questions };
        break;
    }
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };
  const handleNext = () => {
    if (validateCurrentStep()) {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
      saveDraft();
    }
  };
  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };
  const handleAssessmentDataChange = (newData) => {
    setAssessmentData((prev) => ({ ...prev, ...newData }));
  };
  const handleQuestionsDataChange = (newQuestions) => {
    setQuestionsData(newQuestions);
  };
  const handleCreateAssessment = async () => {
    try {
      setSaving(true);
      const validation = validateCompleteAssessment(assessmentData, questionsData);
      if (!validation.isValid) {
        setValidationErrors({ ...validation.assessment, ...validation.questions });
        showMessage("error", "Please fix validation errors before creating assessment");
        return;
      }
      const uniqueCheck = await checkAssessmentNameUnique(
        assessmentData.name,
        institution?.id
      );
      if (!uniqueCheck.unique) {
        showMessage("error", "An assessment with this name already exists");
        return;
      }
      const sanitizedAssessment = sanitizeAssessmentData(assessmentData);
      const sanitizedQuestions = sanitizeQuestionsData(questionsData);
      await createAssessmentWithQuestions(
        {
          ...sanitizedAssessment,
          institutionId: institution?.id
        },
        sanitizedQuestions,
        user.id
      );
      showMessage("success", "Assessment created successfully!");
      clearDraft();
      setTimeout(() => {
        navigate("/assessment-management");
      }, 2e3);
    } catch (error) {
      showMessage("error", error.message);
    } finally {
      setSaving(false);
    }
  };
  if (!user || user.role !== "super_admin") {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-center min-h-screen", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Shield, { className: "mx-auto h-12 w-12 text-red-500" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 298,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "mt-2 text-lg font-medium text-gray-900", children: "Access Denied" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 299,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "Only Super Admins can create assessments." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 300,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 297,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 296,
      columnNumber: 7
    }, globalThis);
  }
  if (!isAuthenticated) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      SuperAdminAuth,
      {
        onAuthenticate: handleSuperAdminAuth,
        loading: authLoading,
        user
      },
      void 0,
      false,
      {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 311,
        columnNumber: 7
      },
      globalThis
    );
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-2xl font-bold text-gray-900", children: "Create Assessment" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 324,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600", children: "Create a comprehensive assessment with questions for peer review" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 325,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 323,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
        lastSaved && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center text-sm text-gray-500", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Clock, { className: "h-4 w-4 mr-1" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
            lineNumber: 330,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: [
            "Last saved: ",
            lastSaved.toLocaleTimeString()
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
            lineNumber: 331,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 329,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => navigate("/assessment-management"),
            className: "btn-outline flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowLeft, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
                lineNumber: 338,
                columnNumber: 13
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Back to Assessments" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
                lineNumber: 339,
                columnNumber: 13
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
            lineNumber: 334,
            columnNumber: 11
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 327,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 322,
      columnNumber: 7
    }, globalThis),
    message.text && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `rounded-md p-4 ${message.type === "error" ? "bg-red-50" : "bg-green-50"}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex-shrink-0", children: message.type === "error" ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5 text-red-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 350,
        columnNumber: 17
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-green-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 352,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 348,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: `text-sm ${message.type === "error" ? "text-red-800" : "text-green-800"}`, children: message.text }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 356,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 355,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 347,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 346,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg p-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("nav", { "aria-label": "Progress", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ol", { className: "flex items-center", children: steps.map((step, index) => {
      const Icon = step.icon;
      const isActive = index === currentStep;
      const isCompleted = index < currentStep;
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: `relative ${index !== steps.length - 1 ? "pr-8 sm:pr-20" : ""}`, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `relative flex h-8 w-8 items-center justify-center rounded-full ${isCompleted ? "bg-green-600" : isActive ? "bg-blue-600" : "bg-gray-300"}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Icon, { className: "h-4 w-4 text-white" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
            lineNumber: 383,
            columnNumber: 23
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
            lineNumber: 376,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-4 min-w-0", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: `text-sm font-medium ${isActive ? "text-blue-600" : isCompleted ? "text-green-600" : "text-gray-500"}`, children: step.title }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
              lineNumber: 386,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-500", children: step.description }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
              lineNumber: 391,
              columnNumber: 23
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
            lineNumber: 385,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 375,
          columnNumber: 19
        }, globalThis),
        index !== steps.length - 1 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 395,
          columnNumber: 21
        }, globalThis)
      ] }, step.id, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 374,
        columnNumber: 17
      }, globalThis);
    }) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 367,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 366,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 365,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg", children: [
      currentStep === 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        AssessmentInformationForm,
        {
          assessmentData,
          onChange: handleAssessmentDataChange,
          validationErrors,
          academicYearOptions: generateAcademicYearOptions(),
          courseTypeOptions,
          assessmentTypeOptions
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 407,
          columnNumber: 11
        },
        globalThis
      ),
      currentStep === 1 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        QuestionManagement,
        {
          questionsData,
          onChange: handleQuestionsDataChange,
          validationErrors
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 418,
          columnNumber: 11
        },
        globalThis
      ),
      currentStep === 2 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        AssessmentReview,
        {
          assessmentData,
          questionsData,
          validationErrors,
          onCreateAssessment: handleCreateAssessment,
          creating: saving
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 426,
          columnNumber: 11
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 405,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: currentStep > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: handlePrevious,
          className: "btn-outline flex items-center space-x-2",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowLeft, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
              lineNumber: 444,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Previous" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
              lineNumber: 445,
              columnNumber: 15
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 440,
          columnNumber: 13
        },
        globalThis
      ) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 438,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: saveDraft,
            className: "btn-outline flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Save, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
                lineNumber: 455,
                columnNumber: 13
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Save Draft" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
                lineNumber: 456,
                columnNumber: 13
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
            lineNumber: 451,
            columnNumber: 11
          },
          globalThis
        ),
        currentStep < steps.length - 1 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: handleNext,
            className: "btn-primary flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Next" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
                lineNumber: 464,
                columnNumber: 15
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowRight, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
                lineNumber: 465,
                columnNumber: 15
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
            lineNumber: 460,
            columnNumber: 13
          },
          globalThis
        ) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: handleCreateAssessment,
            disabled: saving,
            className: "btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",
            children: saving ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
                lineNumber: 475,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Creating..." }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
                lineNumber: 476,
                columnNumber: 19
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
              lineNumber: 474,
              columnNumber: 17
            }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
                lineNumber: 480,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Create Assessment" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
                lineNumber: 481,
                columnNumber: 19
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
              lineNumber: 479,
              columnNumber: 17
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
            lineNumber: 468,
            columnNumber: 13
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 450,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 437,
      columnNumber: 7
    }, globalThis),
    Object.keys(validationErrors).length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-red-50 border border-red-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5 text-red-400 mt-0.5 mr-3" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 493,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-red-900 mb-2", children: "Please fix the following errors:" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 495,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-red-800 space-y-1", children: formatValidationErrors({
          assessment: currentStep === 0 ? validationErrors : {},
          questions: currentStep === 1 ? validationErrors : {}
        }).map((error, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
          "• ",
          error
        ] }, index, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 501,
          columnNumber: 19
        }, globalThis)) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
          lineNumber: 496,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
        lineNumber: 494,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 492,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
      lineNumber: 491,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateAssessment.jsx",
    lineNumber: 320,
    columnNumber: 5
  }, globalThis);
};

export { CreateAssessment as default };
