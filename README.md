# Peer Review System

A comprehensive offline Windows desktop application for peer review assessments built with React, Vite, and Electron. Designed for educational institutions to conduct peer evaluations with complete offline functionality and robust data management.

## 🌟 Key Features

### Core Functionality
- **🔒 Complete Offline Operation**: No internet dependency - works entirely offline
- **🖥️ Native Windows Desktop App**: Optimized for Windows 10+ with native OS integration
- **👥 Role-Based Access Control**: Super Admin, Teacher, and Student roles with granular permissions
- **📊 Comprehensive Assessment System**: Create custom peer review forms with flexible scoring
- **📈 Advanced Analytics**: Real-time statistics with Chart.js visualizations
- **📄 PDF Report Generation**: Professional individual and batch reports
- **🏢 Institution Management**: Customizable branding and settings
- **💾 Local Data Storage**: Secure SQLite database with backup/restore capabilities

### User Experience
- **🎨 Modern UI**: Clean, responsive interface built with Tailwind CSS
- **⚡ Fast Performance**: Vite-powered development with optimized production builds
- **🔐 Security Hardened**: bcrypt password hashing, XSS prevention, audit logging
- **📱 Mobile Responsive**: Full functionality across all device sizes
- **🌐 Internationalization Ready**: Extensible for multiple languages

### Advanced Features
- **📋 Batch Management**: Organize students efficiently with bulk operations
- **🎯 Smart Peer Assignment**: Fair distribution algorithms for peer reviews
- **📊 Real-Time Progress Tracking**: Live status updates and completion indicators
- **🔍 Advanced Search & Filtering**: Powerful data discovery tools
- **📤 Data Export/Import**: Comprehensive backup and migration capabilities
- **💾 Complete Offline Operation**: Standalone desktop application with no external dependencies

## 🛠️ Technical Stack

- **Frontend**: React 18 + Vite + TypeScript
- **Desktop Framework**: Electron 27+ with secure IPC
- **Database**: SQLite with optimized queries
- **Styling**: Tailwind CSS + Lucide React icons
- **Charts**: Chart.js with react-chartjs-2
- **PDF Generation**: PDFKit for professional reports
- **Security**: bcrypt, DOMPurify, JWT sessions
- **Testing**: Jest + Playwright for comprehensive coverage

## 🚀 Quick Start

### Prerequisites

- **Node.js**: v16 or higher ([Download](https://nodejs.org/))
- **npm**: Included with Node.js
- **Operating System**: Windows 10 or Windows 11 (64-bit or 32-bit)
- **Disk Space**: 200MB for installation, 50MB for data storage
- **Memory**: 4GB RAM minimum, 8GB recommended

### Installation Options

#### Option 1: Standalone Application (Recommended)
```bash
# Clone the repository
git clone https://github.com/ACPL/peer-review-system.git
cd peer-review-system

# Install dependencies
npm install

# Start the application
npm start
```

#### Option 2: Windows Batch Script
```cmd
# Double-click or run in Command Prompt
start-peer-review.bat
```

#### Option 3: PowerShell Script
```powershell
# Run in PowerShell
.\start-peer-review.ps1
```

#### Option 4: Direct Launch (After Building)
```cmd
npm run start:quick
```

### First-Time Setup

1. **Super Admin Registration**: Create your first Super Admin account
2. **Institution Setup**: Configure your institution details and branding
3. **User Management**: Add teachers and students to the system
4. **Assessment Creation**: Design your first peer review assessment

### Initial Setup

The application will guide you through the initial setup process on first launch, including creating your Super Admin account.

## 🔧 Production Build

### Building the Application

```cmd
# Test production build by starting the built application
npm start
```

### Building for Distribution

```cmd
# Build complete Windows application (installer + portable)
npm run build:electron
```

This single command will:
- Build the React application
- Create Windows installer (NSIS)
- Create portable executable
- Output files to `dist-electron/` directory

## 📁 Project Structure

```
peer-review-system/
├── 📁 electron/                    # Electron main process
│   ├── 📄 main.js                 # Main process entry point
│   ├── 📄 preload.js              # Secure IPC bridge
│   ├── 📁 config/                 # Configuration files
│   └── 📁 database/               # Database layer
│       ├── 📄 database.js         # SQLite database class
│       └── 📁 models/             # Database models
├── 📁 src/                        # React frontend application
│   ├── 📁 components/             # Reusable UI components
│   │   ├── 📁 admin/              # Admin-specific components
│   │   ├── 📁 assessment/         # Assessment components
│   │   ├── 📁 batch/              # Batch management components
│   │   └── 📁 __tests__/          # Component tests
│   ├── 📁 pages/                  # Main application pages
│   ├── 📁 contexts/               # React context providers
│   ├── 📁 hooks/                  # Custom React hooks
│   ├── 📁 utils/                  # Utility functions
│   └── 📁 types/                  # TypeScript type definitions
├── 📁 public/                     # Static assets and icons
├── 📁 scripts/                    # Build and development scripts
├── 📁 docs/                       # Technical documentation
├── 📁 dist/                       # Production build output
└── 📁 dist-electron/              # Electron distribution files
```

## 🗄️ Database Schema

The application uses SQLite with optimized schema design:

### Core Tables
- **`users`**: User accounts with role-based access and security features
- **`institution`**: Institution settings, branding, and configuration
- **`batches`**: Student groups for organized assessment management
- **`batch_students`**: Many-to-many relationship for batch enrollment
- **`assessments`**: Peer review assessment configurations and metadata
- **`peer_reviews`**: Individual peer review submissions and responses
- **`settings`**: Application configuration and user preferences
- **`audit_log`**: Comprehensive system activity logging for compliance

### Security Features
- **Password Hashing**: bcrypt with configurable cost factor
- **Session Management**: JWT-based authentication with 4-hour expiry
- **Audit Trail**: Complete action logging with user attribution
- **Data Validation**: Input sanitization and XSS prevention

## 👥 User Roles & Permissions

### Super Admin
- **Full System Access**: Complete control over all features
- **Assessment Creation**: Design and configure peer review assessments
- **User Management**: Create, edit, and manage all user accounts
- **Institution Settings**: Configure branding and system settings
- **Audit Access**: View comprehensive system audit logs

### Teacher
- **Batch Management**: Create and manage student batches
- **Assessment Assignment**: Assign assessments to batches
- **Progress Monitoring**: Track assessment completion and progress
- **Report Generation**: Generate and export student reports
- **Student Management**: Manage students within their batches

### Student
- **Assessment Participation**: Take assigned peer review assessments
- **Progress Tracking**: View personal assessment progress
- **Result Access**: View individual performance reports
- **Profile Management**: Update personal information and preferences

## 🎯 Core Workflows

### Assessment Creation & Management
- **Multi-Step Wizard**: Intuitive assessment creation process
- **Flexible Scoring**: Points, percentages, or custom scoring systems
- **Criteria Configuration**: Weighted scoring with custom criteria
- **Batch Assignment**: Target specific student groups
- **Scheduling**: Date-based assessment availability

### Peer Review Process
- **Smart Assignment**: Fair peer distribution algorithms
- **Interactive Interface**: Star ratings with comment collection
- **Progress Tracking**: Real-time completion monitoring
- **Quality Assurance**: Built-in validation and consistency checks

### Analytics & Reporting
- **Individual Reports**: Comprehensive PDF reports for each student
- **Batch Analytics**: Statistical analysis with visual charts
- **Performance Metrics**: Detailed scoring and ranking information
- **Export Capabilities**: Multiple format support for data export

## 🛠️ Development Guide

### Script Reference

For detailed development instructions, see [DEVELOPMENT_SCRIPTS.md](DEVELOPMENT_SCRIPTS.md).

#### Quick Reference
```cmd
# Development
npm run dev           # Hot reload development
npm start            # Start built application

# Building
npm run build:electron # Build complete Windows application

# Utilities
npm run clean         # Clean build artifacts
```

### Adding New Features

1. **Database Schema**: Update `electron/database/database.js`
2. **IPC Handlers**: Add secure communication in `electron/main.js`
3. **React Components**: Create in `src/components/` with proper TypeScript
4. **Context Providers**: Update state management in `src/contexts/`
5. **Testing**: Add unit tests and update E2E test coverage

### Code Quality Standards

- **TypeScript**: Strict type checking enabled
- **ESLint**: Enforced code style and best practices
- **Testing**: Minimum 80% code coverage required
- **Security**: All inputs sanitized, secure IPC communication
- **Performance**: Optimized builds with code splitting

## 🔧 Troubleshooting

### Common Issues

| Issue | Solution |
|-------|----------|
| **Port conflicts** | Check ports 5173, 5174, 4173 are available |
| **Database errors** | Ensure `data/` directory has write permissions |
| **Build failures** | Run `npm run clean && npm install` |
| **PDF generation** | Verify file system permissions |
| **Hot reload not working** | Restart dev server, check Electron console |

### Application Logs

```cmd
# Check application logs
# Windows: %APPDATA%\PeerReviewSystem\logs\
```

### Performance Optimization

- **Database**: Indexes on frequently queried columns
- **Frontend**: Lazy loading for large components
- **Memory**: Efficient state management with React contexts
- **Build**: Tree shaking and code splitting enabled

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

### Development Workflow
1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** changes: `git commit -m 'Add amazing feature'`
4. **Test** thoroughly: `npm run test:all`
5. **Push** to branch: `git push origin feature/amazing-feature`
6. **Submit** a Pull Request

### Code Standards
- Follow existing code style and patterns
- Add tests for new functionality
- Update documentation as needed
- Ensure all tests pass before submitting

### Reporting Issues
- Use the GitHub issue tracker
- Provide detailed reproduction steps
- Include system information and logs
- Check existing issues before creating new ones

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

### Key Points
- ✅ Free for commercial and personal use
- ✅ Modify and distribute freely
- ✅ Include original license in distributions
- ❌ No warranty or liability

## 🆘 Support & Documentation

### Getting Help
- **📖 User Guide**: See [USER_GUIDE.md](USER_GUIDE.md) for detailed usage instructions
- **🔧 Development**: Check [DEVELOPMENT_SCRIPTS.md](DEVELOPMENT_SCRIPTS.md) for technical details
- **🐛 Issues**: Contact <NAME_EMAIL>
- **💬 Support**: Visit https://www.ajinkyacreatiion.com for assistance

### Additional Resources
- **Security**: Review security best practices in the codebase
- **Performance**: Optimization guidelines in technical documentation
- **Deployment**: Production deployment guides in `deploy/` directory
- **API**: IPC communication patterns in `electron/` directory

## 👥 Attribution & Credits

### Author
**Maj. Sachin Kumar Singh**
- Role: Project Author & System Architect

### Developer
**Hrishikesh Mohite**
- Role: Lead Developer & Implementation Specialist
- Email: <EMAIL>
- Website: [https://www.hrishikeshmohite.com](https://www.hrishikeshmohite.com)

### Company
**Ajinkyacreatiion PVT. LTD.**
- Website: [https://www.ajinkyacreatiion.com](https://www.ajinkyacreatiion.com)
- Email: <EMAIL>

### Acknowledgments
- Special thanks to all educational institutions providing feedback
- Community contributors and beta testers
- Open source libraries and frameworks that made this project possible

---

**Made with ❤️ by Ajinkyacreatiion PVT. LTD.** | **Version 1.0.0** | **Last Updated: 2024**
