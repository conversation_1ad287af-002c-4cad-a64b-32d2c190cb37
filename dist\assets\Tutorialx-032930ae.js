import{u as c,r as u,j as e}from"./chunk-81a058b1.js";import{Z as p,b,d as N,H as d,U as g,F as x,C as h,a5 as v,g as f,a9 as A}from"./chunk-0b87a8e8.js";import"./chunk-81a949b4.js";const w=()=>{const t=c(),[s,l]=u.useState(0),i=[{title:"Welcome to Peer Review System",icon:N,content:e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("p",{className:"text-lg text-gray-700",children:"Welcome to the comprehensive Peer Review System! This tutorial will guide you through all the key features and help you get started quickly."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:36,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-blue-900 mb-2",children:"What you'll learn:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:41,columnNumber:13},globalThis),e.jsxDEV("ul",{className:"text-blue-800 space-y-1",children:[e.jsxDEV("li",{children:"• How to navigate the dashboard"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:43,columnNumber:15},globalThis),e.jsxDEV("li",{children:"• Managing students and batches"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:44,columnNumber:15},globalThis),e.jsxDEV("li",{children:"• Creating and managing assessments"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:45,columnNumber:15},globalThis),e.jsxDEV("li",{children:"• Conducting peer reviews"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:46,columnNumber:15},globalThis),e.jsxDEV("li",{children:"• Generating reports and statistics"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:47,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:42,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:40,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:35,columnNumber:9},globalThis)},{title:"Dashboard Overview",icon:d,content:e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("p",{className:"text-gray-700",children:"The Dashboard is your central hub for all activities. Here you can see:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:58,columnNumber:11},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxDEV("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900 mb-2",children:"Quick Statistics"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:63,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"View total students, active assessments, and completion rates at a glance."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:64,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:62,columnNumber:13},globalThis),e.jsxDEV("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900 mb-2",children:"Recent Activity"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:69,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"See the latest submissions, reviews, and system activities."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:70,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:68,columnNumber:13},globalThis),e.jsxDEV("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900 mb-2",children:"Quick Actions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:75,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Access frequently used features like creating assessments or managing batches."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:76,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:74,columnNumber:13},globalThis),e.jsxDEV("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900 mb-2",children:"Notifications"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:81,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Stay updated with important alerts and system messages."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:82,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:80,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:61,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:57,columnNumber:9},globalThis)},{title:"Managing Students and Batches",icon:g,content:e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("p",{className:"text-gray-700",children:"Organize your students into batches for better management:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:95,columnNumber:11},globalThis),e.jsxDEV("div",{className:"space-y-3",children:[e.jsxDEV("div",{className:"flex items-start space-x-3",children:[e.jsxDEV("div",{className:"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold",children:"1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:100,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"Create a Batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:102,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Go to Batches → Create New Batch. Enter batch details and add students."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:103,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:101,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:99,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex items-start space-x-3",children:[e.jsxDEV("div",{className:"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold",children:"2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:107,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"Add Students"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:109,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Import from CSV or add manually. Each student gets unique credentials."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:110,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:108,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:106,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex items-start space-x-3",children:[e.jsxDEV("div",{className:"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold",children:"3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:114,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"Manage Batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:116,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Edit batch details, add/remove students, or archive completed batches."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:117,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:115,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:113,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:98,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:94,columnNumber:9},globalThis)},{title:"Creating Assessments",icon:x,content:e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("p",{className:"text-gray-700",children:"Create comprehensive assessments for peer review:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:129,columnNumber:11},globalThis),e.jsxDEV("div",{className:"space-y-3",children:[e.jsxDEV("div",{className:"flex items-start space-x-3",children:[e.jsxDEV("div",{className:"w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold",children:"1"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:134,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"Assessment Details"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:136,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Set title, description, time limits, and instructions."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:137,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:135,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:133,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex items-start space-x-3",children:[e.jsxDEV("div",{className:"w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold",children:"2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:141,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"Add Questions"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:143,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Create multiple-choice questions with multiple valid answers for peer review context."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:144,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:142,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:140,columnNumber:13},globalThis),e.jsxDEV("div",{className:"flex items-start space-x-3",children:[e.jsxDEV("div",{className:"w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold",children:"3"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:148,columnNumber:15},globalThis),e.jsxDEV("div",{children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"Assign to Batches"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:150,columnNumber:17},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Select which batches can access the assessment."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:151,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:149,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:147,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:132,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:128,columnNumber:9},globalThis)},{title:"Conducting Peer Reviews",icon:h,content:e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("p",{className:"text-gray-700",children:"The peer review process ensures comprehensive evaluation:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:163,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-yellow-50 p-4 rounded-lg mb-4",children:[e.jsxDEV("h4",{className:"font-semibold text-yellow-900 mb-2",children:"Process Overview:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:167,columnNumber:13},globalThis),e.jsxDEV("ol",{className:"text-yellow-800 space-y-1 list-decimal list-inside",children:[e.jsxDEV("li",{children:"Students complete the assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:169,columnNumber:15},globalThis),e.jsxDEV("li",{children:"System processes responses for peer review"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:170,columnNumber:15},globalThis),e.jsxDEV("li",{children:"Students review peers' answers anonymously"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:171,columnNumber:15},globalThis),e.jsxDEV("li",{children:"Results are compiled and analyzed"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:172,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:168,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:166,columnNumber:11},globalThis),e.jsxDEV("div",{className:"space-y-2",children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"Key Features:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:176,columnNumber:13},globalThis),e.jsxDEV("ul",{className:"text-gray-600 space-y-1",children:[e.jsxDEV("li",{children:"• Anonymous peer evaluation"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:178,columnNumber:15},globalThis),e.jsxDEV("li",{children:"• Multiple reviewers per response"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:179,columnNumber:15},globalThis),e.jsxDEV("li",{children:"• Automated result compilation"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:180,columnNumber:15},globalThis),e.jsxDEV("li",{children:"• Detailed analytics and insights"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:181,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:177,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:175,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:162,columnNumber:9},globalThis)},{title:"Reports and Analytics",icon:v,content:e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("p",{className:"text-gray-700",children:"Generate comprehensive reports and analyze performance:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:192,columnNumber:11},globalThis),e.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxDEV("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-purple-900 mb-2",children:"Student Reports"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:197,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-purple-800 text-sm",children:"Individual performance, peer review scores, and improvement suggestions."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:198,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:196,columnNumber:13},globalThis),e.jsxDEV("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-purple-900 mb-2",children:"Batch Analytics"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:203,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-purple-800 text-sm",children:"Overall batch performance, completion rates, and comparative analysis."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:204,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:202,columnNumber:13},globalThis),e.jsxDEV("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-purple-900 mb-2",children:"Assessment Insights"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:209,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-purple-800 text-sm",children:"Question-wise analysis, difficulty levels, and response patterns."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:210,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:208,columnNumber:13},globalThis),e.jsxDEV("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-purple-900 mb-2",children:"Export Options"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:215,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-purple-800 text-sm",children:"PDF reports, CSV data exports, and printable summaries."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:216,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:214,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:195,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:191,columnNumber:9},globalThis)},{title:"System Settings",icon:f,content:e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("p",{className:"text-gray-700",children:"Customize the system to match your institution's needs:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:229,columnNumber:11},globalThis),e.jsxDEV("div",{className:"space-y-3",children:[e.jsxDEV("div",{className:"bg-gray-50 p-3 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"Institution Settings"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:234,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Update institution details, logo, and contact information."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:235,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:233,columnNumber:13},globalThis),e.jsxDEV("div",{className:"bg-gray-50 p-3 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"User Management"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:238,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Manage teachers, admins, and their permissions."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:239,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:237,columnNumber:13},globalThis),e.jsxDEV("div",{className:"bg-gray-50 p-3 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"System Preferences"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:242,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Configure default settings, notifications, and security options."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:243,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:241,columnNumber:13},globalThis),e.jsxDEV("div",{className:"bg-gray-50 p-3 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-gray-900",children:"Backup & Security"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:246,columnNumber:15},globalThis),e.jsxDEV("p",{className:"text-gray-600 text-sm",children:"Set up automated backups and security policies."},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:247,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:245,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:232,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:228,columnNumber:9},globalThis)},{title:"Getting Started",icon:A,content:e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("p",{className:"text-lg text-gray-700",children:"You're ready to start using the Peer Review System!"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:258,columnNumber:11},globalThis),e.jsxDEV("div",{className:"bg-green-50 p-4 rounded-lg",children:[e.jsxDEV("h4",{className:"font-semibold text-green-900 mb-3",children:"Quick Start Checklist:"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:262,columnNumber:13},globalThis),e.jsxDEV("div",{className:"space-y-2",children:[e.jsxDEV("label",{className:"flex items-center space-x-2",children:[e.jsxDEV("input",{type:"checkbox",className:"rounded"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:265,columnNumber:17},globalThis),e.jsxDEV("span",{className:"text-green-800",children:"Set up institution details"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:266,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:264,columnNumber:15},globalThis),e.jsxDEV("label",{className:"flex items-center space-x-2",children:[e.jsxDEV("input",{type:"checkbox",className:"rounded"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:269,columnNumber:17},globalThis),e.jsxDEV("span",{className:"text-green-800",children:"Create your first batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:270,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:268,columnNumber:15},globalThis),e.jsxDEV("label",{className:"flex items-center space-x-2",children:[e.jsxDEV("input",{type:"checkbox",className:"rounded"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:273,columnNumber:17},globalThis),e.jsxDEV("span",{className:"text-green-800",children:"Add students to the batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:274,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:272,columnNumber:15},globalThis),e.jsxDEV("label",{className:"flex items-center space-x-2",children:[e.jsxDEV("input",{type:"checkbox",className:"rounded"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:277,columnNumber:17},globalThis),e.jsxDEV("span",{className:"text-green-800",children:"Create your first assessment"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:278,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:276,columnNumber:15},globalThis),e.jsxDEV("label",{className:"flex items-center space-x-2",children:[e.jsxDEV("input",{type:"checkbox",className:"rounded"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:281,columnNumber:17},globalThis),e.jsxDEV("span",{className:"text-green-800",children:"Assign assessment to batch"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:282,columnNumber:17},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:280,columnNumber:15},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:263,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:261,columnNumber:11},globalThis),e.jsxDEV("div",{className:"text-center",children:e.jsxDEV("button",{onClick:()=>t("/dashboard"),className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Go to Dashboard"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:287,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:286,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:257,columnNumber:9},globalThis)}],a=i[s],o=a.icon,m=()=>{s<i.length-1&&l(s+1)},n=()=>{s>0&&l(s-1)};return e.jsxDEV("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxDEV("div",{className:"max-w-4xl mx-auto px-4",children:[e.jsxDEV("div",{className:"text-center mb-8",children:[e.jsxDEV("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Tutorial"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:319,columnNumber:11},globalThis),e.jsxDEV("p",{className:"text-gray-600",children:"Learn how to use the Peer Review System effectively"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:320,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:318,columnNumber:9},globalThis),e.jsxDEV("div",{className:"mb-8",children:[e.jsxDEV("div",{className:"flex justify-between items-center mb-2",children:[e.jsxDEV("span",{className:"text-sm text-gray-600",children:"Progress"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:326,columnNumber:13},globalThis),e.jsxDEV("span",{className:"text-sm text-gray-600",children:[s+1," of ",i.length]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:327,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:325,columnNumber:11},globalThis),e.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsxDEV("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${(s+1)/i.length*100}%`}},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:330,columnNumber:13},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:329,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:324,columnNumber:9},globalThis),e.jsxDEV("div",{className:"bg-white rounded-lg shadow-lg p-8 mb-8",children:[e.jsxDEV("div",{className:"flex items-center mb-6",children:[e.jsxDEV("div",{className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4",children:e.jsxDEV(o,{className:"w-6 h-6 text-white"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:341,columnNumber:15},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:340,columnNumber:13},globalThis),e.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900",children:a.title},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:343,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:339,columnNumber:11},globalThis),e.jsxDEV("div",{className:"prose max-w-none",children:a.content},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:346,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:338,columnNumber:9},globalThis),e.jsxDEV("div",{className:"flex justify-between items-center",children:[e.jsxDEV("button",{onClick:n,disabled:s===0,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${s===0?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:[e.jsxDEV(p,{className:"w-4 h-4 mr-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:362,columnNumber:13},globalThis),"Previous"]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:353,columnNumber:11},globalThis),e.jsxDEV("div",{className:"flex space-x-2",children:i.map((j,r)=>e.jsxDEV("button",{onClick:()=>l(r),className:`w-3 h-3 rounded-full transition-colors ${r===s?"bg-blue-600":"bg-gray-300"}`},r,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:368,columnNumber:15},globalThis))},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:366,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:m,disabled:s===i.length-1,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${s===i.length-1?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:["Next",e.jsxDEV(b,{className:"w-4 h-4 ml-2"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:388,columnNumber:13},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:378,columnNumber:11},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:352,columnNumber:9},globalThis),e.jsxDEV("div",{className:"text-center mt-8",children:e.jsxDEV("button",{onClick:()=>t("/dashboard"),className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Dashboard"},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:394,columnNumber:11},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:393,columnNumber:9},globalThis)]},void 0,!0,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:316,columnNumber:7},globalThis)},void 0,!1,{fileName:"E:/ACPL/Applications/peer-review-system/src/pages/Tutorial.jsx",lineNumber:315,columnNumber:5},globalThis)};export{w as default};
