const h=s=>{const e=[];if(!s)return e.push("Password is required"),e;s.length<8&&e.push("Password must be at least 8 characters long"),s.length>128&&e.push("Password is too long (maximum 128 characters)"),/[a-z]/.test(s)||e.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(s)||e.push("Password must contain at least one uppercase letter"),/\d/.test(s)||e.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(s)||e.push("Password must contain at least one special character");const t=[/(.)\1{2,}/,/123456|654321|abcdef|qwerty|password/i];for(const r of t)if(r.test(s)){e.push("Password contains common patterns and is not secure");break}return e},l=s=>{const e=[];return s?(/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(s)||e.push("Please enter a valid email address"),s.length>254&&e.push("Email address is too long (maximum 254 characters)"),e):(e.push("Email is required"),e)},d=s=>{const e=[];if(s){const t=/^[\+]?[1-9][\d]{0,15}$/,r=s.replace(/[\s\-\(\)]/g,"");t.test(r)||e.push("Please enter a valid phone number")}return e},u=(s,e="Name")=>{const t=[];return s?(s.length<2&&t.push(`${e} must be at least 2 characters long`),s.length>50&&t.push(`${e} must be less than 50 characters long`),/^[a-zA-Z\s\-'\.]+$/.test(s)||t.push(`${e} can only contain letters, spaces, hyphens, apostrophes, and periods`),t):(t.push(`${e} is required`),t)},f=s=>{const e=[];return s?(s.length<3&&e.push("Teacher ID must be at least 3 characters long"),s.length>20&&e.push("Teacher ID must be less than 20 characters long"),/^[a-zA-Z0-9\-_]+$/.test(s)||e.push("Teacher ID can only contain letters, numbers, hyphens, and underscores"),e):(e.push("Teacher ID is required"),e)},T=(s,e=!1)=>{const t={},r=u(s.firstName,"First name");r.length>0&&(t.firstName=r);const n=u(s.lastName,"Last name");n.length>0&&(t.lastName=n);const a=l(s.email);a.length>0&&(t.email=a);const i=d(s.phone);if(i.length>0&&(t.phone=i),e&&s.teacherId){const o=f(s.teacherId);o.length>0&&(t.teacherId=o)}if(s.password){const o=h(s.password);o.length>0&&(t.password=o)}return t},q=s=>Object.keys(s).length>0,p=s=>typeof s!="string"?s:s.trim().replace(/[<>]/g,"").replace(/['"]/g,"").substring(0,255),F=s=>{const e={};return Object.keys(s).forEach(t=>{t==="password"?e[t]=s[t]?.trim():typeof s[t]=="string"?e[t]=p(s[t]):e[t]=s[t]}),e},m=s=>{const e=[];return s?(s.length<2&&e.push("Institution name must be at least 2 characters long"),s.length>100&&e.push("Institution name must be less than 100 characters long"),e):(e.push("Institution name is required"),e)},g=s=>{const e=[];return s&&s.length>500&&e.push("Description must be less than 500 characters long"),e},b=s=>{const e=[];if(s)try{new URL(s)}catch{e.push("Please enter a valid website URL (e.g., https://example.com)")}return e},y=s=>{const e=[];if(s){const t=String(s).trim();if(t===""||t==="null"||t==="undefined")return e;const r=parseInt(t,10),n=new Date().getFullYear();isNaN(r)||!Number.isInteger(r)?e.push("Please enter a valid year"):r<1800?e.push("Established year cannot be before 1800"):r>n&&e.push("Established year cannot be in the future")}return e},v=s=>{const e=[];return s&&!["University","College","School","Training Center","Other"].includes(s)&&e.push("Please select a valid institute type"),e},$=s=>{const e={},t=m(s.name);t.length>0&&(e.name=t);const r=g(s.description);r.length>0&&(e.description=r);const n=l(s.email);n.length>0&&(e.email=n);const a=d(s.phone);a.length>0&&(e.phone=a),s.address?s.address.length<10&&(e.address=["Address must be at least 10 characters long"]):e.address=["Address is required"];const i=b(s.websiteUrl);i.length>0&&(e.websiteUrl=i);const o=y(s.establishedYear);o.length>0&&(e.establishedYear=o);const c=v(s.instituteType);return c.length>0&&(e.instituteType=c),e},Z=s=>{const e=[];if(!s)return e.push("Please select a file"),e;["image/png","image/jpeg","image/jpg","image/svg+xml"].includes(s.type)||e.push("Only PNG, JPG, JPEG, and SVG files are allowed");const r=2*1024*1024;return s.size>r&&e.push("File size must be less than 2MB"),e},Y=s=>{const e=[];return s?(s.length<3&&e.push("Batch name must be at least 3 characters long"),s.length>50&&e.push("Batch name must be less than 50 characters long"),/^[a-zA-Z0-9\s]+$/.test(s)||e.push("Batch name can only contain letters, numbers, and spaces"),e):(e.push("Batch name is required"),e)},E=s=>{const e=[];if(!s)return e.push("Academic year is required"),e;if(!/^\d{4}-\d{4}$/.test(s))return e.push("Academic year must be in format YYYY-YYYY (e.g., 2024-2025)"),e;const r=s.split("-");if(r.length!==2)return e.push("Academic year must be in format YYYY-YYYY (e.g., 2024-2025)"),e;const n=r[0].trim(),a=r[1].trim(),i=parseInt(n,10),o=parseInt(a,10),c=new Date().getFullYear();return isNaN(i)||!Number.isInteger(i)?(e.push("Start year must be a valid integer"),e):isNaN(o)||!Number.isInteger(o)?(e.push("End year must be a valid integer"),e):((i<2e3||i>c+5)&&e.push("Start year must be between 2000 and "+(c+5)),o!==i+1&&e.push("End year must be exactly one year after start year"),e)},I=s=>{const e=[],t=["Course 01","Course 02","Course 03","Course 04"];return s?(t.includes(s)||e.push("Please select a valid course type"),e):(e.push("Course type is required"),e)},P=s=>{const e=[];return s&&s.length>200&&e.push("Description must be less than 200 characters long"),e},w=s=>{const e=[];return s?(s.length<2&&e.push("Full name must be at least 2 characters long"),s.length>50&&e.push("Full name must be less than 50 characters long"),/^[a-zA-Z\s]+$/.test(s)||e.push("Full name can only contain letters and spaces"),s.trim().split(" ").filter(r=>r.length>0).length<1&&e.push("Please provide at least a first name"),e):(e.push("Full name is required"),e)},N=s=>{const e=[];return s?(s.length<6&&e.push("Student ID must be at least 6 characters long"),s.length>20&&e.push("Student ID must be less than 20 characters long"),/^[a-zA-Z0-9]+$/.test(s)||e.push("Student ID can only contain letters and numbers"),e):(e.push("Student ID is required"),e)},S=s=>{const e=[];if(!s)return e.push("Year of birth is required"),e;const t=String(s).trim();if(t===""||t==="null"||t==="undefined")return e.push("Year of birth is required"),e;const r=parseInt(t,10),n=new Date().getFullYear();return isNaN(r)||!Number.isInteger(r)?e.push("Please enter a valid year"):r<n-100?e.push("Year of birth cannot be more than 100 years ago"):r>n-10?e.push("Year of birth cannot be less than 10 years ago"):r<1900?e.push("Year of birth must be after 1900"):r>n&&e.push("Year of birth cannot be in the future"),e},A=s=>{const e={},t=Y(s.name);t.length>0&&(e.name=t);const r=E(s.academicYear);r.length>0&&(e.academicYear=r);const n=I(s.courseType);n.length>0&&(e.courseType=n);const a=P(s.description);return a.length>0&&(e.description=a),e},z=s=>{const e={},t=w(s.fullName);t.length>0&&(e.fullName=t);const r=N(s.studentId);r.length>0&&(e.studentId=r);const n=h(s.password);n.length>0&&(e.password=n);const a=S(s.yearOfBirth);if(a.length>0&&(e.yearOfBirth=a),s.email){const i=l(s.email);i.length>0&&(e.email=i)}return e},x=(s,e)=>{const t={batch:{},students:[]};if(t.batch=A(s),!e||e.length===0)t.batch.students=["At least one student is required"];else if(e.length>50)t.batch.students=["Maximum 50 students allowed per batch"];else{e.forEach((a,i)=>{const o=z(a);Object.keys(o).length>0&&(t.students[i]=o)});const r=e.map(a=>a.studentId).filter(a=>a),n=r.filter((a,i)=>r.indexOf(a)!==i);n.length>0&&(t.batch.duplicateStudentIds=[`Duplicate student IDs found: ${[...new Set(n)].join(", ")}`])}return t};export{h as a,Z as b,$ as c,x as d,z as e,q as h,F as s,T as v};
