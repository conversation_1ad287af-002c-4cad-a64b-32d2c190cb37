import { j as jsxDevRuntimeExports, r as reactExports, u as useNavigate } from './chunk-2ef8e52b.js';
import { b as useDatabase, u as useAuth } from './main-ab4f3d46.js';
import { s as sanitizeUserData, d as validateBatchWithStudents, h as hasValidationErrors } from './chunk-8b627984.js';
import { F as FileText, w as Calendar, d as BookOpen, Y as AlignLeft, I as Info, b as ArrowRight, e as UserPlus, a as AlertTriangle, C as CheckCircle, u as Upload, W as Download, U as Users, R as RefreshCw, T as Trash2, p as EyeOff, l as Eye, Z as ArrowLeft, X, _ as FileX, $ as Copy, a0 as RotateCcw, H as Home, A as AlertCircle, a1 as Loader } from './chunk-028772a4.js';
import { d as downloadCSVTemplate, a as downloadCorrectedCSV, v as validateCSVFile, r as readCSVFile, p as parseCSVContent, b as validateCSVStudents, m as mergeStudentsData } from './chunk-4660e7bb.js';
import './chunk-03d61bd9.js';

const BatchInformationForm = ({
  batchData,
  errors,
  onChange,
  onNext,
  canProceed
}) => {
  const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
  const generateAcademicYears = () => {
    const years = [];
    for (let i = -2; i <= 5; i++) {
      const startYear = currentYear + i;
      const endYear = startYear + 1;
      years.push(`${startYear}-${endYear}`);
    }
    return years;
  };
  const courseTypes = [
    "Course 01",
    "Course 02",
    "Course 03",
    "Course 04"
  ];
  const academicYears = generateAcademicYears();
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-xl font-semibold text-gray-900 flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 44,
          columnNumber: 11
        }, globalThis),
        "Batch Information"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
        lineNumber: 43,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-1", children: "Enter the basic information for your new batch" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
        lineNumber: 47,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
      lineNumber: 42,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("form", { className: "space-y-6", "data-testid": "create-batch-form", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Batch Name *" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 55,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            type: "text",
            value: batchData.name,
            onChange: (e) => onChange("name", e.target.value),
            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.name ? "border-red-500" : "border-gray-300"}`,
            placeholder: "Enter batch name (e.g., CS-2024-A, Engineering Batch 1)",
            maxLength: 50,
            "data-testid": "batch-name-input"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 58,
            columnNumber: 11
          },
          globalThis
        ),
        errors.name && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.name[0] }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 70,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-500 mt-1", children: "3-50 characters, letters, numbers, and spaces only" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 72,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
        lineNumber: 54,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-2 flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Calendar, { className: "h-4 w-4 mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
              lineNumber: 82,
              columnNumber: 15
            }, globalThis),
            "Academic Year *"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 81,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              value: batchData.academicYear,
              onChange: (e) => onChange("academicYear", e.target.value),
              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.academicYear ? "border-red-500" : "border-gray-300"}`,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "", children: "Select academic year" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
                  lineNumber: 92,
                  columnNumber: 15
                }, globalThis),
                academicYears.map((year) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: year, children: year }, year, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
                  lineNumber: 94,
                  columnNumber: 17
                }, globalThis))
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
              lineNumber: 85,
              columnNumber: 13
            },
            globalThis
          ),
          errors.academicYear && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.academicYear[0] }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 98,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 80,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-2 flex items-center", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(BookOpen, { className: "h-4 w-4 mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
              lineNumber: 105,
              columnNumber: 15
            }, globalThis),
            "Course Type *"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 104,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              value: batchData.courseType,
              onChange: (e) => onChange("courseType", e.target.value),
              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.courseType ? "border-red-500" : "border-gray-300"}`,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "", children: "Select course type" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
                  lineNumber: 115,
                  columnNumber: 15
                }, globalThis),
                courseTypes.map((type) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: type, children: type }, type, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
                  lineNumber: 117,
                  columnNumber: 17
                }, globalThis))
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
              lineNumber: 108,
              columnNumber: 13
            },
            globalThis
          ),
          errors.courseType && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.courseType[0] }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 121,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 103,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
        lineNumber: 78,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: "block text-sm font-medium text-gray-700 mb-2 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlignLeft, { className: "h-4 w-4 mr-1" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 129,
            columnNumber: 13
          }, globalThis),
          "Description (Optional)"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 128,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "textarea",
          {
            value: batchData.description,
            onChange: (e) => onChange("description", e.target.value),
            rows: 3,
            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.description ? "border-red-500" : "border-gray-300"}`,
            placeholder: "Brief description of the batch (optional)",
            maxLength: 200
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 132,
            columnNumber: 11
          },
          globalThis
        ),
        errors.description && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-sm mt-1", children: errors.description[0] }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 143,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mt-1", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-500", children: "Provide additional context about this batch" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 146,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-500", children: [
            batchData.description.length,
            "/200 characters"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 149,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 145,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
        lineNumber: 127,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-blue-50 border border-blue-200 rounded-md p-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Info, { className: "h-5 w-5 text-blue-600 mt-0.5 mr-3" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 158,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-blue-900 mb-1", children: "Batch Creation Guidelines" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 160,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-blue-800 space-y-1", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Batch names should be unique within your teaching scope" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
              lineNumber: 164,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Academic year format: YYYY-YYYY (e.g., 2024-2025)" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
              lineNumber: 165,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• You can add 1-50 students per batch in the next step" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
              lineNumber: 166,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "• Student accounts will be automatically created" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
              lineNumber: 167,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 163,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 159,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
        lineNumber: 157,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
        lineNumber: 156,
        columnNumber: 9
      }, globalThis),
      Object.keys(errors).length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-red-50 border border-red-200 rounded-md p-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-red-900 mb-2", children: "Please fix the following errors:" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 176,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-red-800 space-y-1", children: Object.entries(errors).map(
          ([field, fieldErrors]) => fieldErrors && fieldErrors.map((error, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
            "• ",
            error
          ] }, `${field}-${index}`, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
            lineNumber: 182,
            columnNumber: 19
          }, globalThis))
        ) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 179,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
        lineNumber: 175,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end pt-6 border-t border-gray-200", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          type: "button",
          onClick: onNext,
          disabled: !canProceed,
          className: `flex items-center space-x-2 px-6 py-2 rounded-md font-medium ${canProceed ? "bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" : "bg-gray-300 text-gray-500 cursor-not-allowed"}`,
          "data-testid": "create-batch-button",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Next: Add Students" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
              lineNumber: 202,
              columnNumber: 13
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowRight, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
              lineNumber: 203,
              columnNumber: 13
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
          lineNumber: 191,
          columnNumber: 11
        },
        globalThis
      ) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
        lineNumber: 190,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
      lineNumber: 52,
      columnNumber: 7
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchInformationForm.jsx",
    lineNumber: 41,
    columnNumber: 5
  }, globalThis);
};

const StudentEntryTable = ({
  studentsData,
  errors,
  onChange,
  onGeneratePassword,
  onPrevious,
  onNext,
  canProceed,
  loading
}) => {
  const [showPasswords, setShowPasswords] = reactExports.useState({});
  const [showBulkActions, setShowBulkActions] = reactExports.useState(false);
  const [showCSVUpload, setShowCSVUpload] = reactExports.useState(false);
  const [csvFile, setCSVFile] = reactExports.useState(null);
  const [csvPreview, setCSVPreview] = reactExports.useState(null);
  const [csvProcessing, setCSVProcessing] = reactExports.useState(false);
  const [csvErrors, setCSVErrors] = reactExports.useState([]);
  const [importSummary, setImportSummary] = reactExports.useState(null);
  const fileInputRef = reactExports.useRef(null);
  const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
  const generateYearOptions = () => {
    const years = [];
    for (let i = currentYear - 100; i <= currentYear - 10; i++) {
      years.push(i);
    }
    return years.reverse();
  };
  const yearOptions = generateYearOptions();
  const addStudent = () => {
    const newStudent = {
      id: Date.now(),
      // Temporary ID for React keys
      fullName: "",
      studentId: "",
      password: "",
      yearOfBirth: "",
      email: ""
    };
    onChange([...studentsData, newStudent]);
  };
  const removeStudent = (index) => {
    const newStudents = studentsData.filter((_, i) => i !== index);
    onChange(newStudents);
  };
  const updateStudent = (index, field, value) => {
    const newStudents = [...studentsData];
    newStudents[index] = {
      ...newStudents[index],
      [field]: value
    };
    onChange(newStudents);
  };
  const generatePasswordForStudent = async (index) => {
    try {
      const password = await onGeneratePassword();
      if (password) {
        updateStudent(index, "password", password);
      }
    } catch (error) {
      console.error("Failed to generate password:", error);
    }
  };
  const togglePasswordVisibility = (index) => {
    setShowPasswords((prev) => ({
      ...prev,
      [index]: !prev[index]
    }));
  };
  const clearAllStudents = () => {
    onChange([]);
  };
  const generateAllPasswords = async () => {
    for (let i = 0; i < studentsData.length; i++) {
      await generatePasswordForStudent(i);
    }
  };
  const handleCSVFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) {
      return;
    }
    const fileErrors = validateCSVFile(file);
    if (fileErrors.length > 0) {
      setCSVErrors(fileErrors);
      return;
    }
    setCSVFile(file);
    setCSVErrors([]);
    processCSVFile(file);
  };
  const handleCSVDrop = (event) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (!file) {
      return;
    }
    const fileErrors = validateCSVFile(file);
    if (fileErrors.length > 0) {
      setCSVErrors(fileErrors);
      return;
    }
    setCSVFile(file);
    setCSVErrors([]);
    processCSVFile(file);
  };
  const handleCSVDragOver = (event) => {
    event.preventDefault();
  };
  const processCSVFile = async (file) => {
    try {
      setCSVProcessing(true);
      setCSVErrors([]);
      const content = await readCSVFile(file);
      const parseResult = parseCSVContent(content);
      if (parseResult.errors.length > 0) {
        setCSVErrors(parseResult.errors.map((err) => `Row ${err.row}: ${err.error}`));
      }
      const validationResult = validateCSVStudents(parseResult.students);
      const studentsWithPasswords = await Promise.all(
        validationResult.validStudents.map(async (student) => {
          const password = await onGeneratePassword();
          return { ...student, password: password || "TempPass123!" };
        })
      );
      setCSVPreview({
        validStudents: studentsWithPasswords,
        invalidStudents: validationResult.invalidStudents,
        duplicateIds: validationResult.duplicateIds,
        totalRows: parseResult.totalRows
      });
    } catch (error) {
      setCSVErrors([error.message]);
    } finally {
      setCSVProcessing(false);
    }
  };
  const handleImportCSV = () => {
    if (!csvPreview || csvPreview.validStudents.length === 0) {
      return;
    }
    const mergeResult = mergeStudentsData(studentsData, csvPreview.validStudents);
    onChange(mergeResult.mergedStudents);
    setImportSummary({
      totalProcessed: csvPreview.totalRows,
      imported: mergeResult.totalAdded,
      duplicates: mergeResult.duplicates.length,
      errors: csvPreview.invalidStudents.length
    });
    setCSVFile(null);
    setCSVPreview(null);
    setShowCSVUpload(false);
  };
  const resetCSVUpload = () => {
    setCSVFile(null);
    setCSVPreview(null);
    setCSVErrors([]);
    setCSVProcessing(false);
    setImportSummary(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };
  const closeCSVUpload = () => {
    resetCSVUpload();
    setShowCSVUpload(false);
  };
  const getStudentError = (index, field) => {
    return errors.students[index] && errors.students[index][field] ? errors.students[index][field][0] : null;
  };
  const hasStudentErrors = (index) => {
    return errors.students[index] && Object.keys(errors.students[index]).length > 0;
  };
  const totalErrors = errors.students.filter((s) => s && Object.keys(s).length > 0).length;
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-xl font-semibold text-gray-900 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(UserPlus, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 251,
            columnNumber: 15
          }, globalThis),
          "Add Students"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 250,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-1", children: "Add students to your batch (1-50 students)" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 254,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 249,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `text-sm font-medium ${studentsData.length > 50 ? "text-red-600" : studentsData.length === 0 ? "text-gray-500" : "text-green-600"}`, children: [
        studentsData.length,
        "/50 students"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 260,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 259,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 248,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 247,
      columnNumber: 7
    }, globalThis),
    (errors.batch.students || errors.batch.duplicateStudentIds || errors.batch.duplicateInstitutionIds) && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4 p-4 bg-red-50 border border-red-200 rounded-md", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertTriangle, { className: "h-5 w-5 text-red-600 mt-0.5 mr-3" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 274,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-red-900 mb-1", children: "Batch Validation Errors" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 276,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-red-800 space-y-1", children: [
          errors.batch.students && errors.batch.students.map((error, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
            "• ",
            error
          ] }, index, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 279,
            columnNumber: 19
          }, globalThis)),
          errors.batch.duplicateStudentIds && errors.batch.duplicateStudentIds.map((error, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
            "• ",
            error
          ] }, index, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 282,
            columnNumber: 19
          }, globalThis)),
          errors.batch.duplicateInstitutionIds && errors.batch.duplicateInstitutionIds.map((error, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
            "• ",
            error
          ] }, index, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 285,
            columnNumber: 19
          }, globalThis))
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 277,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 275,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 273,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 272,
      columnNumber: 9
    }, globalThis),
    importSummary && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4 p-4 bg-green-50 border border-green-200 rounded-md", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5 text-green-600 mt-0.5 mr-3" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 297,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-green-900 mb-1", children: "CSV Import Completed" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 299,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-green-800", children: [
          "Successfully imported ",
          importSummary.imported,
          " students from ",
          importSummary.totalProcessed,
          " rows.",
          importSummary.duplicates > 0 && ` ${importSummary.duplicates} duplicates skipped.`,
          importSummary.errors > 0 && ` ${importSummary.errors} rows had errors.`
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 300,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 298,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 296,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 295,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4 flex flex-wrap items-center gap-3", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: addStudent,
          disabled: studentsData.length >= 50,
          className: "btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(UserPlus, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 317,
              columnNumber: 11
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Add Student" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 318,
              columnNumber: 11
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 312,
          columnNumber: 9
        },
        globalThis
      ),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: () => setShowCSVUpload(true),
          disabled: studentsData.length >= 50,
          className: "btn-outline flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Upload, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 326,
              columnNumber: 11
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Import CSV" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 327,
              columnNumber: 11
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 321,
          columnNumber: 9
        },
        globalThis
      ),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: downloadCSVTemplate,
          className: "btn-outline flex items-center space-x-2 text-sm",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Download, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 334,
              columnNumber: 11
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Download Template" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 335,
              columnNumber: 11
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 330,
          columnNumber: 9
        },
        globalThis
      ),
      studentsData.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(jsxDevRuntimeExports.Fragment, { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setShowBulkActions(!showBulkActions),
            className: "btn-outline flex items-center space-x-2",
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-4 w-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 344,
                columnNumber: 15
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Bulk Actions" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 345,
                columnNumber: 15
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 340,
            columnNumber: 13
          },
          globalThis
        ),
        showBulkActions && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: generateAllPasswords,
              className: "btn-outline flex items-center space-x-2 text-sm",
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RefreshCw, { className: "h-3 w-3" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 354,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Generate All Passwords" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 355,
                  columnNumber: 19
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 350,
              columnNumber: 17
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: clearAllStudents,
              className: "btn-outline text-red-600 border-red-300 hover:bg-red-50 flex items-center space-x-2 text-sm",
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Trash2, { className: "h-3 w-3" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 362,
                  columnNumber: 19
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Clear All" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 363,
                  columnNumber: 19
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 358,
              columnNumber: 17
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 349,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 339,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 311,
      columnNumber: 7
    }, globalThis),
    studentsData.length > 0 ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("table", { className: "min-w-full divide-y divide-gray-200", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("thead", { className: "bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "#" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 377,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Full Name *" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 380,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Student ID *" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 383,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Password *" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 386,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Year of Birth *" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 389,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Email" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 392,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Actions" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 395,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 376,
        columnNumber: 15
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 375,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tbody", { className: "bg-white divide-y divide-gray-200", children: studentsData.map((student, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { className: hasStudentErrors(index) ? "bg-red-50" : "", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900", children: index + 1 }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 403,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-4 whitespace-nowrap", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              type: "text",
              value: student.fullName,
              onChange: (e) => updateStudent(index, "fullName", e.target.value),
              className: `w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${getStudentError(index, "fullName") ? "border-red-500" : "border-gray-300"}`,
              placeholder: "John Doe",
              maxLength: 50
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 409,
              columnNumber: 21
            },
            globalThis
          ),
          getStudentError(index, "fullName") && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-xs mt-1", children: getStudentError(index, "fullName") }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 420,
            columnNumber: 23
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 408,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-4 whitespace-nowrap", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              type: "text",
              value: student.studentId,
              onChange: (e) => updateStudent(index, "studentId", e.target.value.toUpperCase()),
              className: `w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${getStudentError(index, "studentId") ? "border-red-500" : "border-gray-300"}`,
              placeholder: "STU001",
              maxLength: 20
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 426,
              columnNumber: 21
            },
            globalThis
          ),
          getStudentError(index, "studentId") && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-xs mt-1", children: getStudentError(index, "studentId") }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 437,
            columnNumber: 23
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 425,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-4 whitespace-nowrap", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-1", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: showPasswords[index] ? "text" : "password",
                value: student.password,
                onChange: (e) => updateStudent(index, "password", e.target.value),
                className: `flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${getStudentError(index, "password") ? "border-red-500" : "border-gray-300"}`,
                placeholder: "Password"
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 444,
                columnNumber: 23
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                type: "button",
                onClick: () => togglePasswordVisibility(index),
                className: "p-1 text-gray-400 hover:text-gray-600",
                children: showPasswords[index] ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(EyeOff, { className: "h-3 w-3" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 458,
                  columnNumber: 49
                }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Eye, { className: "h-3 w-3" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 458,
                  columnNumber: 82
                }, globalThis)
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 453,
                columnNumber: 23
              },
              globalThis
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                type: "button",
                onClick: () => generatePasswordForStudent(index),
                className: "p-1 text-blue-600 hover:text-blue-800",
                title: "Generate Password",
                children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RefreshCw, { className: "h-3 w-3" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 466,
                  columnNumber: 25
                }, globalThis)
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 460,
                columnNumber: 23
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 443,
            columnNumber: 21
          }, globalThis),
          getStudentError(index, "password") && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-xs mt-1", children: getStudentError(index, "password") }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 470,
            columnNumber: 23
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 442,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-4 whitespace-nowrap", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              value: student.yearOfBirth,
              onChange: (e) => updateStudent(index, "yearOfBirth", e.target.value),
              className: `w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${getStudentError(index, "yearOfBirth") ? "border-red-500" : "border-gray-300"}`,
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "", children: "Year" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 483,
                  columnNumber: 23
                }, globalThis),
                yearOptions.map((year) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: year, children: year }, year, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 485,
                  columnNumber: 25
                }, globalThis))
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 476,
              columnNumber: 21
            },
            globalThis
          ),
          getStudentError(index, "yearOfBirth") && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-xs mt-1", children: getStudentError(index, "yearOfBirth") }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 489,
            columnNumber: 23
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 475,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-4 whitespace-nowrap", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "input",
            {
              type: "email",
              value: student.email,
              onChange: (e) => updateStudent(index, "email", e.target.value),
              className: `w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${getStudentError(index, "email") ? "border-red-500" : "border-gray-300"}`,
              placeholder: "<EMAIL>"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 495,
              columnNumber: 21
            },
            globalThis
          ),
          getStudentError(index, "email") && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-red-500 text-xs mt-1", children: getStudentError(index, "email") }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 505,
            columnNumber: 23
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 494,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => removeStudent(index),
            className: "text-red-600 hover:text-red-900 p-1",
            title: "Remove Student",
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Trash2, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 516,
              columnNumber: 23
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 511,
            columnNumber: 21
          },
          globalThis
        ) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 510,
          columnNumber: 19
        }, globalThis)
      ] }, student.id || index, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 402,
        columnNumber: 17
      }, globalThis)) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 400,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 374,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 373,
      columnNumber: 9
    }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "mx-auto h-12 w-12 text-gray-400" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 526,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "mt-2 text-sm font-medium text-gray-900", children: "No students added" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 527,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "Get started by adding your first student to the batch." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 528,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-6", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: addStudent,
          className: "btn-primary flex items-center space-x-2 mx-auto",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(UserPlus, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 536,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Add First Student" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 537,
              columnNumber: 15
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 532,
          columnNumber: 13
        },
        globalThis
      ) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 531,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 525,
      columnNumber: 9
    }, globalThis),
    totalErrors > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 p-4 bg-red-50 border border-red-200 rounded-md", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertTriangle, { className: "h-5 w-5 text-red-600 mt-0.5 mr-3" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 547,
        columnNumber: 13
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-red-900 mb-1", children: [
          totalErrors,
          " student",
          totalErrors > 1 ? "s have" : " has",
          " validation errors"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 549,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-red-800", children: "Please fix the highlighted errors before proceeding." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 552,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 548,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 546,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 545,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between pt-6 border-t border-gray-200 mt-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: onPrevious,
          className: "flex items-center space-x-2 px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowLeft, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 566,
              columnNumber: 11
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Previous: Batch Info" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 567,
              columnNumber: 11
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 562,
          columnNumber: 9
        },
        globalThis
      ),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: onNext,
          disabled: !canProceed || loading,
          className: `flex items-center space-x-2 px-6 py-2 rounded-md font-medium ${canProceed && !loading ? "bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" : "bg-gray-300 text-gray-500 cursor-not-allowed"}`,
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: loading ? "Creating..." : "Create Batch" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 579,
              columnNumber: 11
            }, globalThis),
            !loading && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowRight, { className: "h-4 w-4" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 580,
              columnNumber: 24
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 570,
          columnNumber: 9
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 561,
      columnNumber: 7
    }, globalThis),
    showCSVUpload && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mb-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Upload, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 590,
            columnNumber: 17
          }, globalThis),
          "Import Students from CSV"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 589,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: closeCSVUpload,
            className: "text-gray-400 hover:text-gray-600",
            children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(X, { className: "h-5 w-5" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 597,
              columnNumber: 17
            }, globalThis)
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 593,
            columnNumber: 15
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 588,
        columnNumber: 13
      }, globalThis),
      !csvFile && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "div",
          {
            className: "border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors",
            onDrop: handleCSVDrop,
            onDragOver: handleCSVDragOver,
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "mx-auto h-12 w-12 text-gray-400 mb-4" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 609,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-lg font-medium text-gray-900 mb-2", children: "Upload CSV File" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 610,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mb-4", children: "Drag and drop your CSV file here, or click to browse" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 613,
                columnNumber: 19
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "input",
                {
                  ref: fileInputRef,
                  type: "file",
                  accept: ".csv",
                  onChange: handleCSVFileSelect,
                  className: "hidden"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 616,
                  columnNumber: 19
                },
                globalThis
              ),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                "button",
                {
                  onClick: () => fileInputRef.current?.click(),
                  className: "btn-primary",
                  children: "Choose CSV File"
                },
                void 0,
                false,
                {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 623,
                  columnNumber: 19
                },
                globalThis
              )
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 604,
            columnNumber: 17
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mt-4 text-sm text-gray-600", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "font-medium mb-2", children: "CSV Format Requirements:" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 632,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "list-disc list-inside space-y-1", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "Required columns: Full Name, Student ID, Year of Birth" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 634,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "Optional column: Email" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 635,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "Maximum 50 students per file" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 636,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: "File size limit: 5MB" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 637,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 633,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 631,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 603,
        columnNumber: 15
      }, globalThis),
      csvErrors.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4 p-4 bg-red-50 border border-red-200 rounded-md", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileX, { className: "h-5 w-5 text-red-600 mt-0.5 mr-3" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 647,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-red-900 mb-1", children: "File Errors" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 649,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-red-800 space-y-1", children: csvErrors.map((error, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { children: [
            "• ",
            error
          ] }, index, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 652,
            columnNumber: 25
          }, globalThis)) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 650,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 648,
          columnNumber: 19
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 646,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 645,
        columnNumber: 15
      }, globalThis),
      csvProcessing && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 664,
          columnNumber: 19
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm text-blue-800", children: "Processing CSV file..." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 665,
          columnNumber: 19
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 663,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 662,
        columnNumber: 15
      }, globalThis),
      csvPreview && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-md font-medium text-gray-900 mb-4", children: "Import Preview" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 673,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-4 mb-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-green-50 p-3 rounded-md", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-green-900", children: "Valid Students" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 678,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-lg font-bold text-green-600", children: csvPreview.validStudents.length }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 679,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 677,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-red-50 p-3 rounded-md", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-red-900", children: "Invalid Rows" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 682,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-lg font-bold text-red-600", children: csvPreview.invalidStudents.length }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 683,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 681,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-yellow-50 p-3 rounded-md", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-yellow-900", children: "Duplicate IDs" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 686,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-lg font-bold text-yellow-600", children: csvPreview.duplicateIds.length }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 687,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 685,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-blue-50 p-3 rounded-md", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-blue-900", children: "Total Rows" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 690,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-lg font-bold text-blue-600", children: csvPreview.totalRows }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 691,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 689,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 676,
          columnNumber: 17
        }, globalThis),
        csvPreview.validStudents.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-gray-900 mb-2", children: [
            "Valid Students (",
            csvPreview.validStudents.length,
            ")"
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 698,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-h-40 overflow-y-auto border border-gray-200 rounded-md", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("table", { className: "min-w-full divide-y divide-gray-200", children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("thead", { className: "bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase", children: "Name" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 705,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase", children: "Student ID" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 706,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase", children: "Year" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 707,
                  columnNumber: 29
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase", children: "Email" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 708,
                  columnNumber: 29
                }, globalThis)
              ] }, void 0, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 704,
                columnNumber: 27
              }, globalThis) }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 703,
                columnNumber: 25
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tbody", { className: "bg-white divide-y divide-gray-200", children: csvPreview.validStudents.slice(0, 10).map((student, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-2 text-sm text-gray-900", children: student.fullName }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 714,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-2 text-sm text-gray-900", children: student.studentId }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 715,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-2 text-sm text-gray-900", children: student.yearOfBirth }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 716,
                  columnNumber: 31
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-3 py-2 text-sm text-gray-900", children: student.email || "-" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                  lineNumber: 717,
                  columnNumber: 31
                }, globalThis)
              ] }, index, true, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 713,
                columnNumber: 29
              }, globalThis)) }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 711,
                columnNumber: 25
              }, globalThis)
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 702,
              columnNumber: 23
            }, globalThis),
            csvPreview.validStudents.length > 10 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "px-3 py-2 text-xs text-gray-500 bg-gray-50", children: [
              "... and ",
              csvPreview.validStudents.length - 10,
              " more students"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 723,
              columnNumber: 25
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 701,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 697,
          columnNumber: 19
        }, globalThis),
        csvPreview.invalidStudents.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between mb-2", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h5", { className: "text-sm font-medium text-red-900", children: [
              "Invalid Rows (",
              csvPreview.invalidStudents.length,
              ")"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 735,
              columnNumber: 23
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                onClick: () => downloadCorrectedCSV(csvPreview.invalidStudents),
                className: "text-xs text-red-600 hover:text-red-800 flex items-center space-x-1",
                children: [
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Download, { className: "h-3 w-3" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                    lineNumber: 742,
                    columnNumber: 25
                  }, globalThis),
                  /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Download Errors" }, void 0, false, {
                    fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                    lineNumber: 743,
                    columnNumber: 25
                  }, globalThis)
                ]
              },
              void 0,
              true,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
                lineNumber: 738,
                columnNumber: 23
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 734,
            columnNumber: 21
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-h-32 overflow-y-auto border border-red-200 rounded-md bg-red-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-3 text-sm text-red-800", children: csvPreview.invalidStudents.map((student, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-2", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: [
              "Row ",
              student.rowNumber,
              ":"
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
              lineNumber: 750,
              columnNumber: 29
            }, globalThis),
            " ",
            student.fullName || "Unknown",
            " -",
            Object.values(student.errors).flat().join(", ")
          ] }, index, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 749,
            columnNumber: 27
          }, globalThis)) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 747,
            columnNumber: 23
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 746,
            columnNumber: 21
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
          lineNumber: 733,
          columnNumber: 19
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 672,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: closeCSVUpload,
            className: "px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",
            children: "Cancel"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 763,
            columnNumber: 15
          },
          globalThis
        ),
        csvFile && !csvPreview && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: resetCSVUpload,
            className: "px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700",
            children: "Reset"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 771,
            columnNumber: 17
          },
          globalThis
        ),
        csvPreview && csvPreview.validStudents.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: handleImportCSV,
            className: "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",
            children: [
              "Import ",
              csvPreview.validStudents.length,
              " Students"
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
            lineNumber: 780,
            columnNumber: 17
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
        lineNumber: 762,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 587,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
      lineNumber: 586,
      columnNumber: 9
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/StudentEntryTable.jsx",
    lineNumber: 246,
    columnNumber: 5
  }, globalThis);
};

const BatchCreationSummary = ({
  batchData,
  studentsData,
  creationResult,
  onCreateAnother,
  onGoToDashboard
}) => {
  const [showPasswords, setShowPasswords] = reactExports.useState(false);
  const [copiedIndex, setCopiedIndex] = reactExports.useState(null);
  const copyToClipboard = async (text, index = null) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2e3);
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
    }
  };
  const generateCredentialsText = () => {
    let text = `Batch: ${batchData.name}
`;
    text += `Academic Year: ${batchData.academicYear}
`;
    text += `Course Type: ${batchData.courseType}

`;
    text += "Student Login Credentials:\n";
    text += `${"=".repeat(50)}

`;
    studentsData.forEach((student, index) => {
      text += `${index + 1}. ${student.fullName}
`;
      text += `   Student ID: ${student.studentId}
`;
      text += `   Username: ${student.studentId.toLowerCase()}
`;
      text += `   Password: ${student.password}
`;
      if (student.email) {
        text += `   Email: ${student.email}
`;
      }
      text += "\n";
    });
    text += "\nNote: Students must change their password on first login.\n";
    text += `Generated on: ${( new Date()).toLocaleString()}
`;
    return text;
  };
  const downloadCredentials = () => {
    const text = generateCredentialsText();
    const blob = new Blob([text], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${batchData.name.replace(/\s+/g, "_")}_credentials.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "p-6", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center mb-8", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-8 w-8 text-green-600" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 78,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 77,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "text-2xl font-bold text-gray-900", children: "Batch Created Successfully!" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 80,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600 mt-2", children: [
        'Your batch "',
        batchData.name,
        '" has been created with ',
        studentsData.length,
        " students"
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 81,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
      lineNumber: 76,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-gray-50 rounded-lg p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-semibold text-gray-900 mb-4 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(FileText, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 91,
            columnNumber: 13
          }, globalThis),
          "Batch Information"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 90,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-3", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-600", children: "Batch Name:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 96,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium", children: batchData.name }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 97,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 95,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-600", children: "Academic Year:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 100,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium", children: batchData.academicYear }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 101,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 99,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-600", children: "Course Type:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 104,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium", children: batchData.courseType }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 105,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 103,
            columnNumber: 13
          }, globalThis),
          batchData.description && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-600", children: "Description:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 109,
              columnNumber: 17
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-800 mt-1", children: batchData.description }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 110,
              columnNumber: 17
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 108,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-600", children: "Batch ID:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 114,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-mono text-sm", children: creationResult.batch.id }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 115,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 113,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-600", children: "Created:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 118,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-sm", children: (/* @__PURE__ */ new Date()).toLocaleString() }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 119,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 117,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 94,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 89,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-gray-50 rounded-lg p-6", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-semibold text-gray-900 mb-4 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-5 w-5 mr-2 text-green-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 127,
            columnNumber: 13
          }, globalThis),
          "Student Statistics"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 126,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "space-y-3", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-600", children: "Total Students:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 132,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-green-600", children: studentsData.length }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 133,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 131,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-600", children: "Accounts Created:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 136,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-green-600", children: creationResult.students.length }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 137,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 135,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-600", children: "With Email:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 140,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium", children: studentsData.filter((s) => s.email).length }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 141,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 139,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "text-gray-600", children: "Password Reset Required:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 144,
              columnNumber: 15
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "font-medium text-orange-600", children: "All Students" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 145,
              columnNumber: 15
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 143,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 130,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 125,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
      lineNumber: 87,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white border border-gray-200 rounded-lg p-6 mb-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between mb-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-semibold text-gray-900 flex items-center", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "h-5 w-5 mr-2 text-blue-600" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 155,
            columnNumber: 13
          }, globalThis),
          "Student Login Credentials"
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 154,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: () => setShowPasswords(!showPasswords),
              className: "btn-outline flex items-center space-x-2 text-sm",
              children: [
                showPasswords ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(EyeOff, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
                  lineNumber: 163,
                  columnNumber: 32
                }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Eye, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
                  lineNumber: 163,
                  columnNumber: 65
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: [
                  showPasswords ? "Hide" : "Show",
                  " Passwords"
                ] }, void 0, true, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
                  lineNumber: 164,
                  columnNumber: 15
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 159,
              columnNumber: 13
            },
            globalThis
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: downloadCredentials,
              className: "btn-primary flex items-center space-x-2 text-sm",
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Download, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
                  lineNumber: 170,
                  columnNumber: 15
                }, globalThis),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Download" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
                  lineNumber: 171,
                  columnNumber: 15
                }, globalThis)
              ]
            },
            void 0,
            true,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 166,
              columnNumber: 13
            },
            globalThis
          )
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 158,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 153,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "ml-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: "text-sm font-medium text-yellow-900", children: "Important Security Notice" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 179,
          columnNumber: 15
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-yellow-800 mt-1", children: "All students will be required to change their password on first login. Please share these credentials securely with your students." }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 180,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 178,
        columnNumber: 13
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 177,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 176,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("table", { className: "min-w-full divide-y divide-gray-200", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("thead", { className: "bg-gray-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Student" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 192,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Student ID" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 195,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Username" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 198,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Password" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 201,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Email" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 204,
            columnNumber: 17
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("th", { className: "px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Actions" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 207,
            columnNumber: 17
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 191,
          columnNumber: 15
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 190,
          columnNumber: 13
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tbody", { className: "bg-white divide-y divide-gray-200", children: studentsData.map((student, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("tr", { className: "hover:bg-gray-50", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-4 py-4 whitespace-nowrap", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-medium text-gray-900", children: student.fullName }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 216,
              columnNumber: 21
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: [
              "Born: ",
              student.yearOfBirth
            ] }, void 0, true, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 217,
              columnNumber: 21
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 215,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-4 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-mono text-gray-900", children: student.studentId }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 220,
            columnNumber: 21
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 219,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-4 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm font-mono text-gray-900", children: student.studentId.toLowerCase() }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 223,
            columnNumber: 21
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 222,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-4 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-2", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `text-sm font-mono ${showPasswords ? "text-gray-900" : "text-gray-400"}`, children: showPasswords ? student.password : "••••••••" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 227,
              columnNumber: 23
            }, globalThis),
            showPasswords && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                onClick: () => copyToClipboard(student.password, index),
                className: "text-gray-400 hover:text-gray-600",
                title: "Copy password",
                children: copiedIndex === index ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-4 w-4 text-green-600" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
                  lineNumber: 237,
                  columnNumber: 29
                }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Copy, { className: "h-4 w-4" }, void 0, false, {
                  fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
                  lineNumber: 239,
                  columnNumber: 29
                }, globalThis)
              },
              void 0,
              false,
              {
                fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
                lineNumber: 231,
                columnNumber: 25
              },
              globalThis
            )
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 226,
            columnNumber: 21
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 225,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-4 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-900", children: student.email || "-" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 246,
            columnNumber: 21
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 245,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("td", { className: "px-4 py-4 whitespace-nowrap", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              onClick: () => copyToClipboard(`Username: ${student.studentId.toLowerCase()}
Password: ${student.password}`, `creds-${index}`),
              className: "text-blue-600 hover:text-blue-900 text-sm",
              title: "Copy credentials",
              children: copiedIndex === `creds-${index}` ? "Copied!" : "Copy"
            },
            void 0,
            false,
            {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 249,
              columnNumber: 21
            },
            globalThis
          ) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 248,
            columnNumber: 19
          }, globalThis)
        ] }, index, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 214,
          columnNumber: 17
        }, globalThis)) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 212,
          columnNumber: 13
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 189,
        columnNumber: 11
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 188,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
      lineNumber: 152,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-semibold text-blue-900 mb-3", children: "Next Steps" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 266,
        columnNumber: 9
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: "text-sm text-blue-800 space-y-2", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: "flex items-start", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5", children: "1" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 269,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Download and securely share the login credentials with your students" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 270,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 268,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: "flex items-start", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5", children: "2" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 273,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Inform students that they must change their password on first login" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 274,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 272,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: "flex items-start", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5", children: "3" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 277,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Create assessments and assignments for your new batch" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 278,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 276,
          columnNumber: 11
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: "flex items-start", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: "flex-shrink-0 w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5", children: "4" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 281,
            columnNumber: 13
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Monitor student activity and engagement through the dashboard" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
            lineNumber: 282,
            columnNumber: 13
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 280,
          columnNumber: 11
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
        lineNumber: 267,
        columnNumber: 9
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
      lineNumber: 265,
      columnNumber: 7
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex flex-col sm:flex-row gap-4 justify-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: onCreateAnother,
          className: "btn-outline flex items-center justify-center space-x-2 px-6 py-3",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(RotateCcw, { className: "h-5 w-5" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 293,
              columnNumber: 11
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Create Another Batch" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 294,
              columnNumber: 11
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 289,
          columnNumber: 9
        },
        globalThis
      ),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: onGoToDashboard,
          className: "btn-primary flex items-center justify-center space-x-2 px-6 py-3",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Home, { className: "h-5 w-5" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 301,
              columnNumber: 11
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "Go to Dashboard" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
              lineNumber: 302,
              columnNumber: 11
            }, globalThis)
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
          lineNumber: 297,
          columnNumber: 9
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
      lineNumber: 288,
      columnNumber: 7
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/components/batch/BatchCreationSummary.jsx",
    lineNumber: 74,
    columnNumber: 5
  }, globalThis);
};

const CreateBatch = () => {
  const navigate = useNavigate();
  const {
    createBatchWithStudents,
    checkBatchNameUnique,
    checkStudentIdsUnique,
    generateSecurePassword,
    getInstitution
  } = useDatabase();
  const { user } = useAuth();
  const [batchData, setBatchData] = reactExports.useState({
    name: "",
    academicYear: "",
    courseType: "",
    description: ""
  });
  const [studentsData, setStudentsData] = reactExports.useState([]);
  const [institution, setInstitution] = reactExports.useState(null);
  const [currentStep, setCurrentStep] = reactExports.useState(1);
  const [loading, setLoading] = reactExports.useState(false);
  const [errors, setErrors] = reactExports.useState({ batch: {}, students: [] });
  const [message, setMessage] = reactExports.useState({ type: "", text: "" });
  const [showConfirmModal, setShowConfirmModal] = reactExports.useState(false);
  const [creationResult, setCreationResult] = reactExports.useState(null);
  const [autoSaveTimer, setAutoSaveTimer] = reactExports.useState(null);
  const [lastSaved, setLastSaved] = reactExports.useState(null);
  reactExports.useEffect(() => {
    if (!user || !["teacher", "admin", "super_admin"].includes(user.role)) {
      navigate("/dashboard");
      return;
    }
    loadInstitution();
    loadDraftData();
  }, [user, navigate]);
  const loadInstitution = async () => {
    try {
      const institutionData = await getInstitution();
      setInstitution(institutionData);
    } catch {
      showMessage("error", "Failed to load institution data");
    }
  };
  const loadDraftData = () => {
    try {
      const savedBatch = localStorage.getItem("createBatch_draft");
      const savedStudents = localStorage.getItem("createBatch_students");
      if (savedBatch) {
        setBatchData(JSON.parse(savedBatch));
      }
      if (savedStudents) {
        setStudentsData(JSON.parse(savedStudents));
      }
      const savedTime = localStorage.getItem("createBatch_lastSaved");
      if (savedTime) {
        setLastSaved(new Date(savedTime));
      }
    } catch (error) {
      console.error("Failed to load draft data:", error);
    }
  };
  const saveDraftData = () => {
    try {
      localStorage.setItem("createBatch_draft", JSON.stringify(batchData));
      localStorage.setItem("createBatch_students", JSON.stringify(studentsData));
      localStorage.setItem("createBatch_lastSaved", (/* @__PURE__ */ new Date()).toISOString());
      setLastSaved(/* @__PURE__ */ new Date());
    } catch (error) {
      console.error("Failed to save draft data:", error);
    }
  };
  const clearDraftData = () => {
    try {
      localStorage.removeItem("createBatch_draft");
      localStorage.removeItem("createBatch_students");
      localStorage.removeItem("createBatch_lastSaved");
      setLastSaved(null);
    } catch (error) {
      console.error("Failed to clear draft data:", error);
    }
  };
  reactExports.useEffect(() => {
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
    }
    const timer = setTimeout(() => {
      if (batchData.name || studentsData.length > 0) {
        saveDraftData();
      }
    }, 2e3);
    setAutoSaveTimer(timer);
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [batchData, studentsData]);
  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: "", text: "" }), 5e3);
  };
  const handleBatchDataChange = (field, value) => {
    setBatchData((prev) => ({
      ...prev,
      [field]: value
    }));
    if (errors.batch[field]) {
      setErrors((prev) => ({
        ...prev,
        batch: {
          ...prev.batch,
          [field]: void 0
        }
      }));
    }
  };
  const handleStudentsDataChange = (newStudentsData) => {
    setStudentsData(newStudentsData);
    setErrors((prev) => ({
      ...prev,
      students: []
    }));
  };
  const validateForm = async () => {
    const sanitizedBatch = sanitizeUserData(batchData);
    const sanitizedStudents = studentsData.map((student) => sanitizeUserData(student));
    const validationErrors = validateBatchWithStudents(sanitizedBatch, sanitizedStudents);
    if (sanitizedBatch.name && user) {
      try {
        const isUnique = await checkBatchNameUnique(sanitizedBatch.name, user.id);
        if (!isUnique) {
          validationErrors.batch.name = ["A batch with this name already exists"];
        }
      } catch (error) {
        console.error("Failed to check batch name uniqueness:", error);
      }
    }
    if (sanitizedStudents.length > 0 && institution) {
      try {
        const studentIds = sanitizedStudents.map((s) => s.studentId).filter((id) => id);
        if (studentIds.length > 0) {
          const uniqueCheck = await checkStudentIdsUnique(studentIds, institution.id);
          if (!uniqueCheck.unique) {
            validationErrors.batch.duplicateInstitutionIds = [
              `Student IDs already exist in institution: ${uniqueCheck.duplicates.join(", ")}`
            ];
          }
        }
      } catch (error) {
        console.error("Failed to check student ID uniqueness:", error);
      }
    }
    setErrors(validationErrors);
    return !hasValidationErrors(validationErrors.batch) && validationErrors.students.every((s) => !s || Object.keys(s).length === 0);
  };
  const handleSubmit = async () => {
    const isValid = await validateForm();
    if (!isValid) {
      showMessage("error", "Please fix the validation errors before creating the batch");
      return;
    }
    setShowConfirmModal(true);
  };
  const handleConfirmCreate = async () => {
    try {
      setLoading(true);
      setShowConfirmModal(false);
      const sanitizedBatch = sanitizeUserData(batchData);
      const sanitizedStudents = studentsData.map((student) => sanitizeUserData(student));
      const batchDataWithIds = {
        ...sanitizedBatch,
        teacherId: user.id,
        institutionId: institution.id
      };
      const result = await createBatchWithStudents(
        batchDataWithIds,
        sanitizedStudents,
        user.id
      );
      setCreationResult(result);
      clearDraftData();
      showMessage("success", `Batch "${sanitizedBatch.name}" created successfully with ${sanitizedStudents.length} students`);
      setCurrentStep(3);
    } catch {
      showMessage("error", "Failed to create batch");
    } finally {
      setLoading(false);
    }
  };
  const handleGeneratePassword = async () => {
    try {
      return await generateSecurePassword();
    } catch {
      showMessage("error", "Failed to generate password");
      return null;
    }
  };
  const steps = [
    {
      id: 1,
      title: "Batch Information",
      description: "Enter basic batch details",
      icon: FileText
    },
    {
      id: 2,
      title: "Add Students",
      description: "Add students to the batch",
      icon: UserPlus
    },
    {
      id: 3,
      title: "Summary",
      description: "Review and confirm",
      icon: CheckCircle
    }
  ];
  const canProceedToStep2 = () => {
    return batchData.name && batchData.academicYear && batchData.courseType;
  };
  const canProceedToStep3 = () => {
    return studentsData.length > 0 && studentsData.length <= 50;
  };
  if (!user || !["teacher", "admin", "super_admin"].includes(user.role)) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-center min-h-screen", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Users, { className: "mx-auto h-12 w-12 text-red-500" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 305,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: "mt-2 text-lg font-medium text-gray-900", children: "Access Denied" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 306,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "mt-1 text-sm text-gray-500", children: "You need teacher or admin privileges to create batches." }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 307,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
      lineNumber: 304,
      columnNumber: 9
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
      lineNumber: 303,
      columnNumber: 7
    }, globalThis);
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-h-screen bg-gray-50", "data-testid": "create-batch", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8", children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-8", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center space-x-4 mb-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          onClick: () => navigate("/dashboard"),
          className: "flex items-center text-gray-600 hover:text-gray-900",
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(ArrowLeft, { className: "h-5 w-5 mr-1" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
              lineNumber: 325,
              columnNumber: 15
            }, globalThis),
            "Back to Dashboard"
          ]
        },
        void 0,
        true,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 321,
          columnNumber: 13
        },
        globalThis
      ) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 320,
        columnNumber: 11
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: "text-3xl font-bold text-gray-900", children: "Create New Batch" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 332,
            columnNumber: 15
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-gray-600 mt-1", children: "Create a new student batch with integrated student management" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 333,
            columnNumber: 15
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 331,
          columnNumber: 13
        }, globalThis),
        lastSaved && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "text-sm text-gray-500", children: [
          "Last saved: ",
          lastSaved.toLocaleTimeString()
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 337,
          columnNumber: 15
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 330,
        columnNumber: 11
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
      lineNumber: 319,
      columnNumber: 9
    }, globalThis),
    message.text && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `mb-6 p-4 rounded-md flex items-center space-x-2 ${message.type === "success" ? "bg-green-50 text-green-800 border border-green-200" : "bg-red-50 text-red-800 border border-red-200"}`, children: [
      message.type === "success" ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(CheckCircle, { className: "h-5 w-5" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 352,
        columnNumber: 15
      }, globalThis) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(AlertCircle, { className: "h-5 w-5" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 354,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: message.text }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 356,
        columnNumber: 13
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
      lineNumber: 346,
      columnNumber: 11
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-8", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("nav", { "aria-label": "Progress", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ol", { className: "flex items-center", children: steps.map((step, stepIdx) => {
      const Icon = step.icon;
      const isActive = currentStep === step.id;
      const isCompleted = currentStep > step.id;
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: `${stepIdx !== steps.length - 1 ? "pr-8 sm:pr-20" : ""} relative`, children: [
        stepIdx !== steps.length - 1 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "absolute inset-0 flex items-center", "aria-hidden": "true", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `h-0.5 w-full ${isCompleted ? "bg-blue-600" : "bg-gray-200"}` }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 373,
          columnNumber: 25
        }, globalThis) }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 372,
          columnNumber: 23
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative flex items-center space-x-3", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `flex h-8 w-8 items-center justify-center rounded-full ${isCompleted ? "bg-blue-600 text-white" : isActive ? "bg-blue-100 text-blue-600 border-2 border-blue-600" : "bg-gray-100 text-gray-400"}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Icon, { className: "h-4 w-4" }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 384,
            columnNumber: 25
          }, globalThis) }, void 0, false, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 377,
            columnNumber: 23
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "min-w-0", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: `text-sm font-medium ${isActive ? "text-blue-600" : "text-gray-500"}`, children: step.title }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
              lineNumber: 387,
              columnNumber: 25
            }, globalThis),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-xs text-gray-500", children: step.description }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
              lineNumber: 390,
              columnNumber: 25
            }, globalThis)
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 386,
            columnNumber: 23
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 376,
          columnNumber: 21
        }, globalThis)
      ] }, step.id, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 370,
        columnNumber: 19
      }, globalThis);
    }) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
      lineNumber: 363,
      columnNumber: 13
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
      lineNumber: 362,
      columnNumber: 11
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
      lineNumber: 361,
      columnNumber: 9
    }, globalThis),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-white shadow rounded-lg", children: [
      currentStep === 1 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        BatchInformationForm,
        {
          batchData,
          errors: errors.batch,
          onChange: handleBatchDataChange,
          onNext: () => canProceedToStep2() && setCurrentStep(2),
          canProceed: canProceedToStep2()
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 403,
          columnNumber: 13
        },
        globalThis
      ),
      currentStep === 2 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        StudentEntryTable,
        {
          studentsData,
          errors,
          onChange: handleStudentsDataChange,
          onGeneratePassword: handleGeneratePassword,
          onPrevious: () => setCurrentStep(1),
          onNext: handleSubmit,
          canProceed: canProceedToStep3(),
          loading
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 413,
          columnNumber: 13
        },
        globalThis
      ),
      currentStep === 3 && creationResult && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        BatchCreationSummary,
        {
          batchData,
          studentsData,
          creationResult,
          onCreateAnother: () => {
            setBatchData({ name: "", academicYear: "", courseType: "", description: "" });
            setStudentsData([]);
            setCreationResult(null);
            setCurrentStep(1);
          },
          onGoToDashboard: () => navigate("/dashboard")
        },
        void 0,
        false,
        {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 426,
          columnNumber: 13
        },
        globalThis
      )
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
      lineNumber: 401,
      columnNumber: 9
    }, globalThis),
    showConfirmModal && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white", children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-between items-center mb-4", children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: "text-lg font-medium text-gray-900", children: "Confirm Batch Creation" }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 446,
        columnNumber: 17
      }, globalThis) }, void 0, false, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 445,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "mb-4", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mb-4", children: "You are about to create a new batch with the following details:" }, void 0, false, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 450,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "bg-gray-50 p-3 rounded-md space-y-2", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Batch Name:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
              lineNumber: 454,
              columnNumber: 22
            }, globalThis),
            " ",
            batchData.name
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 454,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Academic Year:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
              lineNumber: 455,
              columnNumber: 22
            }, globalThis),
            " ",
            batchData.academicYear
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 455,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Course Type:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
              lineNumber: 456,
              columnNumber: 22
            }, globalThis),
            " ",
            batchData.courseType
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 456,
            columnNumber: 19
          }, globalThis),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Students:" }, void 0, false, {
              fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
              lineNumber: 457,
              columnNumber: 22
            }, globalThis),
            " ",
            studentsData.length
          ] }, void 0, true, {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 457,
            columnNumber: 19
          }, globalThis)
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 453,
          columnNumber: 17
        }, globalThis),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: "text-sm text-gray-600 mt-4", children: [
          "This will create ",
          studentsData.length,
          " new student accounts. This action cannot be undone."
        ] }, void 0, true, {
          fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
          lineNumber: 459,
          columnNumber: 17
        }, globalThis)
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 449,
        columnNumber: 15
      }, globalThis),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: "flex justify-end space-x-3", children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: () => setShowConfirmModal(false),
            className: "px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",
            disabled: loading,
            children: "Cancel"
          },
          void 0,
          false,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 465,
            columnNumber: 17
          },
          globalThis
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            onClick: handleConfirmCreate,
            className: "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2",
            disabled: loading,
            children: [
              loading && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(Loader, { className: "h-4 w-4 animate-spin" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
                lineNumber: 477,
                columnNumber: 31
              }, globalThis),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: loading ? "Creating..." : "Create Batch" }, void 0, false, {
                fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
                lineNumber: 478,
                columnNumber: 19
              }, globalThis)
            ]
          },
          void 0,
          true,
          {
            fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
            lineNumber: 472,
            columnNumber: 17
          },
          globalThis
        )
      ] }, void 0, true, {
        fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
        lineNumber: 464,
        columnNumber: 15
      }, globalThis)
    ] }, void 0, true, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
      lineNumber: 444,
      columnNumber: 13
    }, globalThis) }, void 0, false, {
      fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
      lineNumber: 443,
      columnNumber: 11
    }, globalThis)
  ] }, void 0, true, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
    lineNumber: 317,
    columnNumber: 7
  }, globalThis) }, void 0, false, {
    fileName: "E:/ACPL/Applications/peer-review-system/src/pages/CreateBatch.jsx",
    lineNumber: 316,
    columnNumber: 5
  }, globalThis);
};

export { CreateBatch as default };
