import { useNavigate } from 'react-router-dom';
import {
  Building,
  Shield,
  GraduationCap,
  BookOpen,
  Sparkles,
  ArrowRight
} from 'lucide-react';

/**
 * Welcome Screen with role-based navigation cards
 */
const WelcomeScreen = () => {
  const navigate = useNavigate();

  // Role-based navigation cards (merged super_admin + admin into single Administrator role)
  const roleCards = [
    {
      id: 'super_admin', // Internal role remains super_admin for full permissions
      title: 'Administrator',
      description: 'Full system access with assessment creation, user management, and system administration',
      icon: Shield,
      color: 'purple',
      features: ['Create Assessments', 'Manage Users', 'System Settings', 'Institution Management', 'Full Access'],
      path: '/dashboard?role=super_admin'
    },
    {
      id: 'teacher',
      title: 'Teacher',
      description: 'Manage batches, conduct assessments, and view student progress',
      icon: GraduationCap,
      color: 'green',
      features: ['Batch Management', 'Take Assessments', 'Student Progress', 'Reports'],
      path: '/dashboard?role=teacher'
    },
    {
      id: 'student',
      title: 'Student',
      description: 'Participate in peer review assessments and view results',
      icon: BookOpen,
      color: 'blue',
      features: ['Take Assessments', 'View Results', 'Peer Reviews', 'Progress Tracking'],
      path: '/dashboard?role=student'
    }
  ];

  const handleRoleSelection = (role) => {
    // Set the selected role in localStorage for the dashboard to use
    localStorage.setItem('selectedRole', role.id);
    navigate(role.path);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl w-full">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="flex items-center space-x-3">
              <Building className="h-16 w-16 text-blue-600" />
              <span className="text-4xl font-bold text-gray-900">Peer Review System</span>
            </div>
          </div>
          <div className="flex justify-center mb-6">
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
              <Sparkles className="h-10 w-10 text-blue-600" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Welcome to Peer Review System
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            A comprehensive offline desktop application for peer review assessments in educational institutions.
            Choose your role to get started.
          </p>
        </div>

        {/* Role Selection Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {roleCards.map((role) => {
            const Icon = role.icon;
            const colorClasses = {
              purple: 'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
              blue: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
              green: 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
              orange: 'from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'
            };

            return (
              <button
                key={role.id}
                onClick={() => handleRoleSelection(role)}
                className={`
                  relative p-8 rounded-2xl shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl
                  bg-gradient-to-br ${colorClasses[role.color]} text-white text-left group
                `}
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-2xl font-bold text-white mb-2">
                      {role.title}
                    </h3>
                    <p className="text-white text-opacity-90 mb-4 leading-relaxed">
                      {role.description}
                    </p>
                    <div className="space-y-2">
                      {role.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                          <span className="text-sm text-white text-opacity-80">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <ArrowRight className="h-6 w-6 text-white text-opacity-60 group-hover:text-opacity-100 transition-opacity" />
                  </div>
                </div>
              </button>
            );
          })}
        </div>

        {/* Footer */}
        <div className="text-center">
          <p className="text-gray-500 text-sm">
            Select your role to access the appropriate dashboard and features
          </p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeScreen;
