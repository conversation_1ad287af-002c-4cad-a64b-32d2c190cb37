/* eslint-disable no-unused-vars */
const initSqlJs = require('sql.js');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const PDFDocument = require('pdfkit');

// Safely import Electron app (may not be available in testing environments)
let app = null;
try {
  const electron = require('electron');
  app = electron.app;
} catch (error) {
  // Electron not available (testing environment)
  app = null;
}
// const os = require('os'); // Removed unused import

class PeerReviewDatabase {
  constructor() {
    this.db = null;
    this.dbPath = null; // Will be set during initialization
    this.retryAttempts = 3;
    this.retryDelay = 1000;
  }

  // Initialize database path - called during initialization
  initializeDatabasePath() {
    try {
      // Check if app is available (Electron environment)
      if (app && typeof app.getPath === 'function') {
        const userDataPath = app.getPath('userData');
        this.dbPath = path.join(userDataPath, 'data', 'peer_review.db');
      } else {
        // Fallback for testing or non-Electron environments
        const fallbackPath = process.env.PEER_REVIEW_DB_PATH ||
                            path.join(process.cwd(), 'data', 'peer_review.db');
        this.dbPath = fallbackPath;
      }

      return this.dbPath;
    } catch (error) {
      throw new Error(`Failed to initialize database path: ${error.message}`);
    }
  }

  // Enhanced error handling with categorization
  handleDatabaseError(error, operation, context = {}) {
    const timestamp = new Date().toISOString();

    // Log error information for debugging (could be used for file logging in the future)
    const errorInfo = {
      timestamp,
      operation,
      context,
      error: {
        message: error.message,
        code: error.code,
        errno: error.errno,
        stack: error.stack
      }
    };

    // Log error to file system (currently disabled for production)
    if (process.env.NODE_ENV === 'development') {
      console.error('Database Error:', errorInfo);
    }

    // Categorize error type
    let errorType = 'DATABASE_ERROR';
    let userMessage = 'A database error occurred. Please try again.';
    let canRetry = true;

    // Ensure error.message exists for string operations
    const errorMsg = error.message || error.toString() || 'Unknown error';

    if (error.code === 'SQLITE_CONSTRAINT') {
      errorType = 'CONSTRAINT_VIOLATION';
      if (errorMsg.includes('UNIQUE')) {
        userMessage = 'This record already exists. Please check for duplicates.';
        canRetry = false;
      } else if (errorMsg.includes('FOREIGN KEY')) {
        userMessage = 'This action conflicts with existing data relationships.';
        canRetry = false;
      } else {
        userMessage = 'Data validation failed. Please check your input.';
        canRetry = false;
      }
    } else if (error.code === 'SQLITE_BUSY') {
      errorType = 'DATABASE_BUSY';
      userMessage = 'Database is busy. Please wait a moment and try again.';
      canRetry = true;
    } else if (error.code === 'SQLITE_LOCKED') {
      errorType = 'DATABASE_LOCKED';
      userMessage = 'Database is locked. Please try again in a moment.';
      canRetry = true;
    } else if (error.code === 'SQLITE_CORRUPT') {
      errorType = 'DATABASE_CORRUPT';
      userMessage = 'Database corruption detected. Please contact support immediately.';
      canRetry = false;
    } else if (error.code === 'SQLITE_FULL') {
      errorType = 'DISK_FULL';
      userMessage = 'Insufficient disk space. Please free up space and try again.';
      canRetry = false;
    } else if (error.code === 'SQLITE_PERM') {
      errorType = 'PERMISSION_ERROR';
      userMessage = 'Database permission error. Please check file permissions.';
      canRetry = false;
    } else if (errorMsg.includes('no such table')) {
      errorType = 'SCHEMA_ERROR';
      userMessage = 'Database schema error. Please restart the application.';
      canRetry = false;
    }

    // Create structured error
    const structuredError = new Error(userMessage);
    structuredError.type = errorType;
    structuredError.canRetry = canRetry;
    structuredError.originalError = error;
    structuredError.context = context;
    structuredError.timestamp = timestamp;

    return structuredError;
  }

  // Helper method to extract error message from sql.js errors
  extractErrorMessage(error) {
    // sql.js errors might have different structures, so we try multiple approaches
    if (error && typeof error === 'object') {
      // Try standard message property
      if (error.message && typeof error.message === 'string') {
        return error.message;
      }

      // Try toString method
      if (typeof error.toString === 'function') {
        const stringified = error.toString();
        if (stringified !== '[object Object]') {
          return stringified;
        }
      }

      // Try to extract from error properties
      if (error.name && error.name !== 'Error') {
        return `${error.name}: ${error.description || 'Unknown error'}`;
      }

      // Try to get any string property that might contain the error
      const stringProps = Object.keys(error).filter(key =>
        typeof error[key] === 'string' && error[key].length > 0
      );

      if (stringProps.length > 0) {
        return error[stringProps[0]];
      }
    }

    // Fallback for primitive values or completely unknown structures
    if (typeof error === 'string') {
      return error;
    }

    return 'Unknown database error occurred';
  }

  // Retry mechanism for database operations
  async executeWithRetry(operation, context = {}) {
    let lastError;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        // Don't retry certain types of errors
        const errorMsg = error.message || error.toString() || 'Unknown error';
        if (error.code === 'SQLITE_CONSTRAINT' ||
            error.code === 'SQLITE_CORRUPT' ||
            error.code === 'SQLITE_PERM' ||
            errorMsg.includes('no such table')) {
          throw this.handleDatabaseError(error, context.operation || 'unknown', context);
        }

        // Don't retry on last attempt
        if (attempt === this.retryAttempts) {
          break;
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));

        // Production mode - no retry logging
      }
    }

    throw this.handleDatabaseError(lastError, context.operation || 'unknown', context);
  }

  async initialize() {
    return this.executeWithRetry(async () => {
      // Step 0: Initialize database path
      this.initializeDatabasePath();

      // Step 1: Verify environment and paths
      await this.verifyEnvironment();

      // Step 2: Ensure data directory exists with proper permissions
      await this.ensureDataDirectory();

      // Step 3: Initialize sql.js library
      const SQL = await this.initializeSqlJs();

      // Step 4: Load or create database
      await this.loadOrCreateDatabase(SQL);

      // Step 5: Configure database settings
      await this.configureDatabaseSettings();

      // Step 6: Create all required tables
      await this.createTables();

      // Step 7: Initialize application settings
      await this.initializeApplicationSettings();

      // Step 8: Create default admin setup if needed
      await this.createDefaultAdmin();

      // Step 9: Verify database integrity and schema
      await this.verifyDatabaseIntegrity();
      await this.verifyUsersTableSchema();
      await this.verifyAllTableSchemas();

      // Step 10: Save database to file
      this.saveDatabase();

      // Step 11: Final verification
      await this.performFinalVerification();

      return {
        success: true,
        dbPath: this.dbPath,
        tablesCreated: await this.getTableCount(),
        setupRequired: await this.isSetupRequired()
      };
    }, { operation: 'initialize' });
  }

  // Step 1: Verify environment and paths
  async verifyEnvironment() {
    try {
      // Verify database path is set
      if (!this.dbPath || typeof this.dbPath !== 'string') {
        throw new Error('Database path not initialized');
      }

      // Verify path module is working
      if (!path || typeof path.join !== 'function') {
        throw new Error('Path module not available');
      }

      // Verify fs module is working
      if (!fs || typeof fs.existsSync !== 'function') {
        throw new Error('File system module not available');
      }

      // Check if we're in Electron environment
      const isElectronEnv = app && typeof app.getPath === 'function';

      if (isElectronEnv) {
        // Verify user data path is accessible in Electron environment
        const userDataPath = app.getPath('userData');
        if (!userDataPath || typeof userDataPath !== 'string') {
          throw new Error('Cannot determine user data directory in Electron environment');
        }

        // Log environment info for debugging (only in development)
        if (process.env.NODE_ENV === 'development') {
          console.log('Database Environment (Electron):', {
            userDataPath,
            dbPath: this.dbPath,
            platform: process.platform,
            nodeVersion: process.version
          });
        }
      } else {
        // Fallback environment (testing or non-Electron)
        if (process.env.NODE_ENV === 'development') {
          console.log('Database Environment (Fallback):', {
            dbPath: this.dbPath,
            platform: process.platform,
            nodeVersion: process.version,
            fallbackMode: true
          });
        }
      }

    } catch (error) {
      throw new Error(`Environment verification failed: ${error.message}`);
    }
  }

  // Step 2: Ensure data directory exists with proper permissions
  async ensureDataDirectory() {
    try {
      const dataDir = path.dirname(this.dbPath);

      // Create directory if it doesn't exist
      if (!fs.existsSync(dataDir)) {
        try {
          fs.mkdirSync(dataDir, { recursive: true });
        } catch (error) {
          throw new Error(`Cannot create data directory: ${error.message}`);
        }
      }

      // Verify directory exists after creation
      if (!fs.existsSync(dataDir)) {
        throw new Error(`Data directory was not created: ${dataDir}`);
      }

      // Test write permissions
      const testFile = path.join(dataDir, '.test-write-' + Date.now());
      try {
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
      } catch (error) {
        throw new Error(`Database directory not writable: ${dataDir} - ${error.message}`);
      }

      // Test read permissions if database file exists
      if (fs.existsSync(this.dbPath)) {
        try {
          const stats = fs.statSync(this.dbPath);
          if (!stats.isFile()) {
            throw new Error(`Database path exists but is not a file: ${this.dbPath}`);
          }
        } catch (error) {
          throw new Error(`Cannot access existing database file: ${error.message}`);
        }
      }

    } catch (error) {
      throw new Error(`Data directory setup failed: ${error.message}`);
    }
  }

  // Step 3: Initialize sql.js library
  async initializeSqlJs() {
    try {
      if (!initSqlJs || typeof initSqlJs !== 'function') {
        throw new Error('sql.js library not available');
      }

      const SQL = await initSqlJs();

      if (!SQL || typeof SQL.Database !== 'function') {
        throw new Error('sql.js initialization failed - Database constructor not available');
      }

      return SQL;
    } catch (error) {
      throw new Error(`sql.js initialization failed: ${error.message}`);
    }
  }

  // Step 4: Load or create database
  async loadOrCreateDatabase(SQL) {
    try {
      let data = null;
      let isNewDatabase = false;

      if (fs.existsSync(this.dbPath)) {
        try {
          data = fs.readFileSync(this.dbPath);
          if (data.length === 0) {
            data = null;
            isNewDatabase = true;
          }
        } catch (error) {
          throw new Error(`Cannot read existing database file: ${error.message}`);
        }
      } else {
        isNewDatabase = true;
      }

      // Create database instance
      try {
        this.db = new SQL.Database(data);
      } catch (error) {
        throw new Error(`Cannot create database instance: ${error.message}`);
      }

      // Verify database instance
      if (!this.db || typeof this.db.exec !== 'function') {
        throw new Error('Database instance creation failed');
      }

      // Test basic database functionality
      try {
        this.db.exec('SELECT 1');
      } catch (error) {
        throw new Error(`Database instance not functional: ${error.message}`);
      }

      return { isNewDatabase };
    } catch (error) {
      throw new Error(`Database loading failed: ${error.message}`);
    }
  }

  // Step 5: Configure database settings
  async configureDatabaseSettings() {
    try {
      // Configure database for optimal performance and reliability
      const pragmaCommands = [
        'PRAGMA journal_mode = WAL',
        'PRAGMA synchronous = NORMAL',
        'PRAGMA cache_size = -64000', // 64MB cache
        'PRAGMA temp_store = MEMORY',
        'PRAGMA foreign_keys = ON',
        'PRAGMA recursive_triggers = ON',
        'PRAGMA secure_delete = ON'
      ];

      for (const command of pragmaCommands) {
        try {
          this.db.exec(command);
        } catch (error) {
          throw new Error(`Failed to execute ${command}: ${error.message}`);
        }
      }

      // Verify foreign keys are enabled
      const fkResult = this.db.exec('PRAGMA foreign_keys');
      if (!fkResult || !fkResult[0] || !fkResult[0].values || fkResult[0].values[0][0] !== 1) {
        throw new Error('Foreign keys could not be enabled');
      }

    } catch (error) {
      throw new Error(`Database configuration failed: ${error.message}`);
    }
  }

  saveDatabase() {
    try {
      if (this.db) {
        const data = this.db.export();
        fs.writeFileSync(this.dbPath, data);
      }
    } catch (error) {
      // Production mode - no console logging, just throw error
      throw error;
    }
  }

  async close() {
    try {
      if (this.db) {
        this.saveDatabase();
        this.db.close();
        this.db = null;
      }
    } catch (error) {
      // Production mode - no console logging, just throw error
      throw error;
    }
  }

  // Database migration for offline-only schema
  async migrateToOfflineSchema() {
    try {
      // Check if migration is needed by looking for email column in users table
      const usersTableInfo = await this.query('PRAGMA table_info(users)');
      const hasEmailColumn = usersTableInfo.some(col => col.name === 'email');
      const hasPhoneColumn = usersTableInfo.some(col => col.name === 'phone');

      if (hasEmailColumn || hasPhoneColumn) {
        console.log('Migrating database to offline-only schema...');

        // Create new users table without email/phone
        await this.run(`
          CREATE TABLE IF NOT EXISTS users_new (
            id TEXT PRIMARY KEY,
            username TEXT UNIQUE NOT NULL CHECK (
              length(username) >= 3 AND
              length(username) <= 30 AND
              username GLOB '[A-Za-z]*' AND
              username NOT GLOB '*[^A-Za-z0-9_-]*'
            ),
            password_hash TEXT NOT NULL CHECK (length(password_hash) > 0),
            first_name TEXT NOT NULL CHECK (
              length(trim(first_name)) > 0 AND
              length(first_name) <= 50 AND
              first_name NOT GLOB '*[0-9]*'
            ),
            last_name TEXT NOT NULL CHECK (
              length(trim(last_name)) > 0 AND
              length(last_name) <= 50 AND
              last_name NOT GLOB '*[0-9]*'
            ),
            role TEXT NOT NULL CHECK (role IN ('super_admin', 'admin', 'teacher', 'student')),
            teacher_id TEXT CHECK (
              teacher_id IS NULL OR
              (length(teacher_id) >= 3 AND length(teacher_id) <= 20)
            ),
            student_id TEXT CHECK (
              student_id IS NULL OR
              (length(student_id) >= 3 AND length(student_id) <= 20)
            ),
            institution_id TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1 CHECK (is_active IN (0, 1)),
            last_login DATETIME,
            password_reset_required BOOLEAN DEFAULT 0 CHECK (password_reset_required IN (0, 1)),
            FOREIGN KEY (institution_id) REFERENCES institution(id) ON DELETE RESTRICT
          )
        `);

        // Dynamically build column list based on what exists in the source table
        const sourceColumns = usersTableInfo.map(col => col.name);

        // Define the mapping of new table columns to source table columns
        const columnMapping = {
          'id': 'id',
          'username': 'username',
          'password_hash': 'password_hash',
          'first_name': 'first_name',
          'last_name': 'last_name',
          'role': 'role',
          'teacher_id': 'teacher_id',
          'student_id': 'student_id', // Will be handled specially if it doesn't exist
          'institution_id': 'institution_id',
          'created_at': 'created_at',
          'updated_at': 'updated_at',
          'is_active': 'is_active',
          'last_login': 'last_login',
          'password_reset_required': 'password_reset_required'
        };

        // Build the SELECT and INSERT column lists
        const insertColumns = [];
        const selectColumns = [];

        for (const [newCol, sourceCol] of Object.entries(columnMapping)) {
          // Skip email and phone columns
          if (newCol === 'email' || newCol === 'phone') continue;

          if (sourceColumns.includes(sourceCol)) {
            insertColumns.push(newCol);
            selectColumns.push(sourceCol);
          } else if (newCol === 'student_id') {
            // If student_id doesn't exist in source, use NULL
            insertColumns.push(newCol);
            selectColumns.push('NULL as student_id');
          } else if (newCol === 'password_reset_required' && !sourceColumns.includes(sourceCol)) {
            // If password_reset_required doesn't exist, default to 0
            insertColumns.push(newCol);
            selectColumns.push('0 as password_reset_required');
          } else if (newCol === 'is_active' && !sourceColumns.includes(sourceCol)) {
            // If is_active doesn't exist, default to 1
            insertColumns.push(newCol);
            selectColumns.push('1 as is_active');
          }
        }

        // Copy data from old table to new table (excluding email/phone)
        await this.run(`
          INSERT INTO users_new (${insertColumns.join(', ')})
          SELECT ${selectColumns.join(', ')}
          FROM users
        `);

        // Drop old table and rename new table
        await this.run('DROP TABLE users');
        await this.run('ALTER TABLE users_new RENAME TO users');

        // Migrate institution table if needed
        const institutionTableInfo = await this.query('PRAGMA table_info(institution)');
        const hasInstitutionEmail = institutionTableInfo.some(col => col.name === 'email');
        const hasInstitutionPhone = institutionTableInfo.some(col => col.name === 'phone');
        const hasWebsiteUrl = institutionTableInfo.some(col => col.name === 'website_url');

        if (hasInstitutionEmail || hasInstitutionPhone || hasWebsiteUrl) {
          // Create new institution table without contact fields
          await this.run(`
            CREATE TABLE IF NOT EXISTS institution_new (
              id TEXT PRIMARY KEY,
              name TEXT NOT NULL CHECK (
                length(trim(name)) >= 2 AND
                length(name) <= 100
              ),
              description TEXT CHECK (
                description IS NULL OR
                length(description) <= 1000
              ),
              address TEXT NOT NULL CHECK (
                length(trim(address)) >= 5 AND
                length(address) <= 500
              ),
              established_year INTEGER CHECK (
                established_year IS NULL OR
                (established_year >= 1800 AND established_year <= strftime('%Y', 'now'))
              ),
              institute_type TEXT CHECK (
                institute_type IS NULL OR
                institute_type IN ('University', 'College', 'School', 'Institute', 'Academy', 'Other')
              ),
              logo_path TEXT,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
          `);

          // Dynamically build column list for institution migration
          const institutionSourceColumns = institutionTableInfo.map(col => col.name);

          // Define institution column mapping (excluding contact fields)
          const institutionColumnMapping = {
            'id': 'id',
            'name': 'name',
            'description': 'description',
            'address': 'address',
            'established_year': 'established_year',
            'institute_type': 'institute_type',
            'logo_path': 'logo_path',
            'created_at': 'created_at',
            'updated_at': 'updated_at'
          };

          const institutionInsertColumns = [];
          const institutionSelectColumns = [];

          for (const [newCol, sourceCol] of Object.entries(institutionColumnMapping)) {
            // Skip contact fields
            if (['email', 'phone', 'website_url'].includes(newCol)) continue;

            if (institutionSourceColumns.includes(sourceCol)) {
              institutionInsertColumns.push(newCol);
              institutionSelectColumns.push(sourceCol);
            } else if (newCol === 'description' && !institutionSourceColumns.includes(sourceCol)) {
              // If description doesn't exist, use NULL
              institutionInsertColumns.push(newCol);
              institutionSelectColumns.push('NULL as description');
            } else if (newCol === 'established_year' && !institutionSourceColumns.includes(sourceCol)) {
              // If established_year doesn't exist, use NULL
              institutionInsertColumns.push(newCol);
              institutionSelectColumns.push('NULL as established_year');
            } else if (newCol === 'institute_type' && !institutionSourceColumns.includes(sourceCol)) {
              // If institute_type doesn't exist, use NULL
              institutionInsertColumns.push(newCol);
              institutionSelectColumns.push('NULL as institute_type');
            } else if (newCol === 'logo_path' && !institutionSourceColumns.includes(sourceCol)) {
              // If logo_path doesn't exist, use NULL
              institutionInsertColumns.push(newCol);
              institutionSelectColumns.push('NULL as logo_path');
            }
          }

          // Copy data excluding contact fields
          await this.run(`
            INSERT INTO institution_new (${institutionInsertColumns.join(', ')})
            SELECT ${institutionSelectColumns.join(', ')}
            FROM institution
          `);

          // Drop old table and rename new table
          await this.run('DROP TABLE institution');
          await this.run('ALTER TABLE institution_new RENAME TO institution');
        }

        // Migrate institution_details table if needed
        const institutionDetailsExists = await this.query("SELECT name FROM sqlite_master WHERE type='table' AND name='institution_details'");
        if (institutionDetailsExists.length > 0) {
          const detailsTableInfo = await this.query('PRAGMA table_info(institution_details)');
          const hasPrimaryEmail = detailsTableInfo.some(col => col.name === 'primary_email');
          const hasContactNumber = detailsTableInfo.some(col => col.name === 'contact_number');
          const hasDetailsWebsiteUrl = detailsTableInfo.some(col => col.name === 'website_url');

          if (hasPrimaryEmail || hasContactNumber || hasDetailsWebsiteUrl) {
            // Create new institution_details table without contact fields
            await this.run(`
              CREATE TABLE IF NOT EXISTS institution_details_new (
                id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
                institution_id TEXT NOT NULL UNIQUE,
                logo_path TEXT,
                description TEXT,
                address_line1 TEXT,
                address_line2 TEXT,
                city TEXT,
                state TEXT,
                postal_code TEXT,
                country TEXT,
                establishment_date DATE,
                accreditation_details TEXT,
                updated_by TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (institution_id) REFERENCES institution(id) ON DELETE CASCADE,
                FOREIGN KEY (updated_by) REFERENCES users(id)
              )
            `);

            // Dynamically build column list for institution_details migration
            const detailsSourceColumns = detailsTableInfo.map(col => col.name);

            // Define institution_details column mapping (excluding contact fields)
            const detailsColumnMapping = {
              'id': 'id',
              'institution_id': 'institution_id',
              'logo_path': 'logo_path',
              'description': 'description',
              'address_line1': 'address_line1',
              'address_line2': 'address_line2',
              'city': 'city',
              'state': 'state',
              'postal_code': 'postal_code',
              'country': 'country',
              'establishment_date': 'establishment_date',
              'accreditation_details': 'accreditation_details',
              'updated_by': 'updated_by',
              'created_at': 'created_at',
              'updated_at': 'updated_at'
            };

            const detailsInsertColumns = [];
            const detailsSelectColumns = [];

            for (const [newCol, sourceCol] of Object.entries(detailsColumnMapping)) {
              // Skip contact fields
              if (['primary_email', 'contact_number', 'website_url'].includes(newCol)) continue;

              if (detailsSourceColumns.includes(sourceCol)) {
                detailsInsertColumns.push(newCol);
                detailsSelectColumns.push(sourceCol);
              } else {
                // If column doesn't exist, use NULL
                detailsInsertColumns.push(newCol);
                detailsSelectColumns.push(`NULL as ${newCol}`);
              }
            }

            // Copy data excluding contact fields
            await this.run(`
              INSERT INTO institution_details_new (${detailsInsertColumns.join(', ')})
              SELECT ${detailsSelectColumns.join(', ')}
              FROM institution_details
            `);

            // Drop old table and rename new table
            await this.run('DROP TABLE institution_details');
            await this.run('ALTER TABLE institution_details_new RENAME TO institution_details');
          }
        }

        console.log('Database migration to offline-only schema completed successfully');
      }
    } catch (error) {
      console.error('Database migration failed:', error);
      throw new Error(`Database migration failed: ${error.message}`);
    }
  }

  async createTables() {
    // Run migration first if needed
    await this.migrateToOfflineSchema();

    const tables = [
      // Users table with enhanced validation constraints
      `CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL CHECK (
          length(username) >= 3 AND
          length(username) <= 30 AND
          username GLOB '[A-Za-z]*' AND
          username NOT GLOB '*[^A-Za-z0-9_-]*'
        ),

        password_hash TEXT NOT NULL CHECK (length(password_hash) > 0),
        first_name TEXT NOT NULL CHECK (
          length(trim(first_name)) > 0 AND
          length(first_name) <= 50 AND
          first_name NOT GLOB '*[0-9]*'
        ),
        last_name TEXT NOT NULL CHECK (
          length(trim(last_name)) > 0 AND
          length(last_name) <= 50 AND
          last_name NOT GLOB '*[0-9]*'
        ),
        role TEXT NOT NULL CHECK (role IN ('super_admin', 'admin', 'teacher', 'student')),
        teacher_id TEXT CHECK (
          teacher_id IS NULL OR
          (length(teacher_id) >= 3 AND length(teacher_id) <= 20)
        ),
        student_id TEXT CHECK (
          student_id IS NULL OR
          (length(student_id) >= 3 AND length(student_id) <= 20)
        ),
        institution_id TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1 CHECK (is_active IN (0, 1)),
        last_login DATETIME,
        password_reset_required BOOLEAN DEFAULT 0 CHECK (password_reset_required IN (0, 1)),
        FOREIGN KEY (institution_id) REFERENCES institution(id) ON DELETE RESTRICT
      )`,

      // Institution table with enhanced validation constraints
      `CREATE TABLE IF NOT EXISTS institution (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL CHECK (
          length(trim(name)) >= 2 AND
          length(name) <= 100
        ),
        description TEXT CHECK (
          description IS NULL OR
          length(description) <= 1000
        ),

        address TEXT NOT NULL CHECK (
          length(trim(address)) >= 5 AND
          length(address) <= 500
        ),

        established_year INTEGER CHECK (
          established_year IS NULL OR
          (established_year >= 1800 AND established_year <= strftime('%Y', 'now'))
        ),
        institute_type TEXT CHECK (
          institute_type IS NULL OR
          institute_type IN ('University', 'College', 'School', 'Institute', 'Academy', 'Other')
        ),
        logo_path TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Batches table
      `CREATE TABLE IF NOT EXISTS batches (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        academic_year TEXT NOT NULL,
        course_type TEXT NOT NULL,
        description TEXT,
        teacher_id TEXT NOT NULL,
        institution_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1,
        FOREIGN KEY (teacher_id) REFERENCES users(id),
        FOREIGN KEY (institution_id) REFERENCES institution(id)
      )`,

      // Batch students table (many-to-many relationship)
      `CREATE TABLE IF NOT EXISTS batch_students (
        id TEXT PRIMARY KEY,
        batch_id TEXT NOT NULL,
        student_id TEXT NOT NULL,
        enrolled_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE(batch_id, student_id)
      )`,

      // Enhanced Assessments table
      `CREATE TABLE IF NOT EXISTS assessments (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        short_description TEXT NOT NULL,
        academic_year TEXT NOT NULL,
        course_type TEXT NOT NULL,
        assessment_type TEXT NOT NULL CHECK (assessment_type IN ('Formative', 'Summative', 'Peer Review')),
        due_date DATETIME NOT NULL,
        instructions TEXT,
        created_by TEXT NOT NULL,
        institution_id TEXT,
        is_published BOOLEAN DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id),
        UNIQUE(name, institution_id)
      )`,

      // Assessment Questions table
      `CREATE TABLE IF NOT EXISTS assessment_questions (
        id TEXT PRIMARY KEY,
        assessment_id TEXT NOT NULL,
        question_text TEXT NOT NULL,
        question_type TEXT NOT NULL DEFAULT 'MCQ',
        question_order INTEGER NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE
      )`,

      // Question Options table (for MCQ)
      `CREATE TABLE IF NOT EXISTS question_options (
        id TEXT PRIMARY KEY,
        question_id TEXT NOT NULL,
        option_text TEXT NOT NULL,
        marks INTEGER NOT NULL DEFAULT 0,
        option_order INTEGER NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (question_id) REFERENCES assessment_questions(id) ON DELETE CASCADE
      )`,

      // Assessment Batch Assignments table
      `CREATE TABLE IF NOT EXISTS assessment_batches (
        id TEXT PRIMARY KEY,
        assessment_id TEXT NOT NULL,
        batch_id TEXT NOT NULL,
        assigned_by TEXT NOT NULL,
        assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,
        FOREIGN KEY (batch_id) REFERENCES batches(id),
        FOREIGN KEY (assigned_by) REFERENCES users(id),
        UNIQUE(assessment_id, batch_id)
      )`,

      // Assessment Responses table for student submissions
      `CREATE TABLE IF NOT EXISTS assessment_responses (
        id TEXT PRIMARY KEY,
        assessment_id TEXT NOT NULL,
        student_id TEXT NOT NULL,
        responses TEXT NOT NULL,
        total_score REAL DEFAULT 0,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_completed BOOLEAN DEFAULT 1,
        time_taken INTEGER DEFAULT 0,
        FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id),
        UNIQUE(assessment_id, student_id)
      )`,

      // Peer reviews table
      `CREATE TABLE IF NOT EXISTS peer_reviews (
        id TEXT PRIMARY KEY,
        assessment_id TEXT NOT NULL,
        reviewer_id TEXT NOT NULL,
        reviewee_id TEXT NOT NULL,
        scores TEXT NOT NULL, -- JSON string of scores for each criterion
        comments TEXT,
        total_score REAL,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,
        FOREIGN KEY (reviewer_id) REFERENCES users(id),
        FOREIGN KEY (reviewee_id) REFERENCES users(id),
        UNIQUE(assessment_id, reviewer_id, reviewee_id)
      )`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Audit log table
      `CREATE TABLE IF NOT EXISTS audit_log (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        action TEXT NOT NULL,
        table_name TEXT,
        record_id TEXT,
        old_values TEXT, -- JSON string
        new_values TEXT, -- JSON string
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`,

      // Assessment statistics table
      `CREATE TABLE IF NOT EXISTS assessment_statistics (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        assessment_id TEXT NOT NULL,
        batch_id TEXT NOT NULL,
        statistics_data TEXT NOT NULL, -- JSON string
        generated_by TEXT NOT NULL,
        generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,
        FOREIGN KEY (batch_id) REFERENCES batches(id),
        FOREIGN KEY (generated_by) REFERENCES users(id),
        UNIQUE(assessment_id, batch_id)
      )`,

      // Assessment PDF reports table
      `CREATE TABLE IF NOT EXISTS assessment_pdf_reports (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        assessment_id TEXT NOT NULL,
        batch_id TEXT NOT NULL,
        reports_data TEXT NOT NULL, -- JSON string
        generated_by TEXT NOT NULL,
        generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,
        FOREIGN KEY (batch_id) REFERENCES batches(id),
        FOREIGN KEY (generated_by) REFERENCES users(id),
        UNIQUE(assessment_id, batch_id)
      )`,

      // User settings table
      `CREATE TABLE IF NOT EXISTS user_settings (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        user_id TEXT NOT NULL,
        setting_key TEXT NOT NULL,
        setting_value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE(user_id, setting_key)
      )`,

      // Application settings table
      `CREATE TABLE IF NOT EXISTS application_settings (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        setting_key TEXT NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Institution details table (enhanced)
      `CREATE TABLE IF NOT EXISTS institution_details (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        institution_id TEXT NOT NULL UNIQUE,
        logo_path TEXT,
        description TEXT,

        address_line1 TEXT,
        address_line2 TEXT,
        city TEXT,
        state TEXT,
        postal_code TEXT,
        country TEXT,
        establishment_date DATE,
        accreditation_details TEXT,

        updated_by TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (institution_id) REFERENCES institution(id) ON DELETE CASCADE,
        FOREIGN KEY (updated_by) REFERENCES users(id)
      )`,

      // Password history table for security
      `CREATE TABLE IF NOT EXISTS password_history (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        user_id TEXT NOT NULL,
        password_hash TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )`,

      // Session management table
      `CREATE TABLE IF NOT EXISTS user_sessions (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        user_id TEXT NOT NULL,
        session_token TEXT NOT NULL UNIQUE,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
        ip_address TEXT,
        user_agent TEXT,
        is_active BOOLEAN DEFAULT 1,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )`
    ];

    // Create tables one by one with better error handling
    for (let i = 0; i < tables.length; i++) {
      try {
        await this.run(tables[i]);
      } catch (error) {
        throw new Error(`Failed to create table ${i + 1}/${tables.length}: ${error.message}`);
      }
    }

    // Add processed_at and processed_by columns to assessments table if they don't exist
    await this.addColumnIfNotExists('assessments', 'processed_at', 'DATETIME');
    await this.addColumnIfNotExists('assessments', 'processed_by', 'TEXT REFERENCES users(id)');
  }

  // Database integrity verification for production deployment
  async verifyDatabaseIntegrity() {
    try {
      // Get list of all tables that actually exist
      const existingTables = await this.query(
        "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
      );

      // Production mode - no console logging

      // Check if core required tables exist (minimum set for basic functionality)
      const coreRequiredTables = [
        'users', 'institution', 'batches', 'batch_students', 'assessments',
        'assessment_questions', 'question_options', 'assessment_batches',
        'assessment_responses', 'peer_reviews', 'settings',
        'application_settings'
      ];

      // Check if core tables exist using the existing tables list
      const existingTableNames = existingTables.map(t => t.name);
      for (const tableName of coreRequiredTables) {
        if (!existingTableNames.includes(tableName)) {
          throw new Error(`Required table '${tableName}' does not exist`);
        }
      }

      // Verify foreign key constraints are properly set
      const foreignKeyCheck = this.db.exec('PRAGMA foreign_key_check');
      if (foreignKeyCheck.length > 0 && foreignKeyCheck[0].values.length > 0) {
        throw new Error('Foreign key constraint violations detected');
      }

      // Verify database integrity
      const integrityCheck = this.db.exec('PRAGMA integrity_check');
      if (integrityCheck.length === 0 ||
          integrityCheck[0].values.length === 0 ||
          integrityCheck[0].values[0][0] !== 'ok') {
        throw new Error('Database integrity check failed');
      }

      // Database integrity verification passed - production mode, no logging
    } catch (error) {
      throw new Error(`Database integrity verification failed: ${error.message}`);
    }
  }

  async verifyUsersTableSchema() {
    try {
      // Get users table schema
      const tableInfo = await this.query('PRAGMA table_info(users)');

      // Define expected columns (offline-only, no email/phone)
      const expectedColumns = [
        'id', 'username', 'password_hash', 'first_name', 'last_name',
        'role', 'teacher_id', 'student_id', 'institution_id', 'created_at', 'updated_at',
        'is_active', 'last_login', 'password_reset_required'
      ];

      const existingColumns = tableInfo.map(col => col.name);

      for (const expectedCol of expectedColumns) {
        if (!existingColumns.includes(expectedCol)) {
          throw new Error(`Users table missing required column: ${expectedCol}`);
        }
      }

      // Verify constraints by attempting a test query
      await this.query('SELECT COUNT(*) FROM users WHERE 1=0');

    } catch (error) {
      throw new Error(`Users table schema verification failed: ${error.message}`);
    }
  }

  // Verify all table schemas
  async verifyAllTableSchemas() {
    try {
      const requiredTables = [
        'users', 'institution', 'batches', 'batch_students', 'assessments',
        'assessment_questions', 'question_options', 'assessment_batches',
        'assessment_responses', 'peer_reviews', 'settings', 'audit_log',
        'application_settings', 'assessment_statistics', 'assessment_pdf_reports',
        'user_settings', 'institution_details', 'password_history', 'user_sessions'
      ];

      for (const tableName of requiredTables) {
        try {
          // Test basic table access
          await this.query(`SELECT COUNT(*) FROM ${tableName} WHERE 1=0`);
        } catch (error) {
          throw new Error(`Table ${tableName} verification failed: ${error.message}`);
        }
      }

    } catch (error) {
      throw new Error(`Table schema verification failed: ${error.message}`);
    }
  }

  // Get table count for verification
  async getTableCount() {
    try {
      const tables = await this.query(
        "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );
      return tables[0]?.count || 0;
    } catch (error) {
      return 0;
    }
  }

  // Check if setup is required
  async isSetupRequired() {
    try {
      const setupCompleted = await this.get(
        'SELECT setting_value FROM application_settings WHERE setting_key = ?',
        ['setup_completed']
      );

      if (!setupCompleted || setupCompleted.setting_value !== 'true') {
        return true;
      }

      // Also check if super admin exists
      const superAdmin = await this.query(
        'SELECT id FROM users WHERE role = ? LIMIT 1',
        ['super_admin']
      );

      return superAdmin.length === 0;
    } catch (error) {
      return true; // If we can't check, assume setup is required
    }
  }

  // Final verification after initialization
  async performFinalVerification() {
    try {
      // Test database connection
      const connectionTest = this.db.exec('SELECT 1 as test');
      if (!connectionTest || !connectionTest[0] || connectionTest[0].values[0][0] !== 1) {
        throw new Error('Database connection test failed');
      }

      // Test file write/read cycle
      this.saveDatabase();

      // Verify file was written
      if (!fs.existsSync(this.dbPath)) {
        throw new Error('Database file was not created');
      }

      // Verify file size is reasonable
      const stats = fs.statSync(this.dbPath);
      if (stats.size < 1024) { // Less than 1KB seems too small
        throw new Error('Database file appears to be too small');
      }

      // Test application settings table exists and has data
      try {
        const settingsCount = await this.query('SELECT COUNT(*) as count FROM application_settings');
        if (!settingsCount || settingsCount.length === 0 || settingsCount[0].count === 0) {
          throw new Error('Application settings table is empty');
        }

        // Check for specific required settings
        const setupStatus = await this.get(
          'SELECT setting_value FROM application_settings WHERE setting_key = ?',
          ['setup_completed']
        );

        if (!setupStatus) {
          // Try to initialize the setting if it doesn't exist
          await this.run(
            'INSERT OR IGNORE INTO application_settings (setting_key, setting_value, description) VALUES (?, ?, ?)',
            ['setup_completed', 'false', 'Indicates whether the initial application setup has been completed']
          );
        }
      } catch (settingsError) {
        throw new Error(`Application settings verification failed: ${settingsError.message}`);
      }

    } catch (error) {
      throw new Error(`Final verification failed: ${error.message}`);
    }
  }

  // Step 7: Initialize application settings
  async initializeApplicationSettings() {
    try {
      // Ensure application_settings table has required entries
      const requiredSettings = [
        {
          key: 'setup_completed',
          value: 'false',
          description: 'Indicates whether the initial application setup has been completed'
        },
        {
          key: 'app_version',
          value: (app && app.getVersion) ? app.getVersion() : '1.0.0',
          description: 'Current application version'
        },
        {
          key: 'database_version',
          value: '1.0',
          description: 'Database schema version'
        },
        {
          key: 'created_at',
          value: new Date().toISOString(),
          description: 'Database creation timestamp'
        }
      ];

      for (const setting of requiredSettings) {
        // Check if setting exists
        const existing = await this.get(
          'SELECT setting_value FROM application_settings WHERE setting_key = ?',
          [setting.key]
        );

        if (!existing) {
          // Insert new setting using INSERT OR IGNORE to prevent duplicates
          await this.run(
            `INSERT OR IGNORE INTO application_settings (setting_key, setting_value, description)
             VALUES (?, ?, ?)`,
            [setting.key, setting.value, setting.description]
          );
        }
      }

    } catch (error) {
      throw new Error(`Application settings initialization failed: ${error.message}`);
    }
  }

  async createDefaultAdmin() {
    try {
      // Always create default admin for simplified card-based navigation
      const existingAdmin = await this.query(
        'SELECT id FROM users WHERE role = ? LIMIT 1',
        ['super_admin']
      );

      if (existingAdmin.length === 0) {
        // Create default institution first (offline-only, no contact info)
        const institutionId = await this.createOrFindInstitution('Default Institution', {
          address: 'Default Address'
        });

        // Create default admin user with hardcoded credentials (offline-only)
        const hashedPassword = await bcrypt.hash('password', 8);
        const userId = uuidv4();

        await this.run(
          `INSERT INTO users (id, username, password_hash, first_name, last_name, role, institution_id, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
          [userId, 'admin', hashedPassword, 'System', 'Administrator', 'super_admin', institutionId]
        );

        // Mark setup as completed
        await this.run(
          `UPDATE application_settings SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
           WHERE setting_key = ?`,
          ['true', 'setup_completed']
        );
      } else {
        // Admin exists, ensure setup is marked as completed
        await this.run(
          `UPDATE application_settings SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
           WHERE setting_key = ?`,
          ['true', 'setup_completed']
        );
      }
    } catch (error) {
      throw new Error(`Default admin setup failed: ${error.message}`);
    }
  }

  // Generic database operations with enhanced error handling
  async query(sql, params = []) {
    return this.executeWithRetry(async () => {
      let stmt = null;
      try {
        // Validate database connection
        if (!this.db) {
          throw new Error('Database connection not established');
        }

        stmt = this.db.prepare(sql);
        const results = [];
        while (stmt.step()) {
          results.push(stmt.getAsObject());
        }
        return results;
      } catch (error) {
        // Enhance error with context
        const errorMessage = this.extractErrorMessage(error);
        const enhancedError = new Error(errorMessage);
        enhancedError.code = error.code || error.errno || 'QUERY_ERROR';
        enhancedError.originalError = error;
        enhancedError.sql = sql;
        enhancedError.params = params;
        throw enhancedError;
      } finally {
        if (stmt) {
          try {
            stmt.free();
          } catch (freeError) {
            // Ignore errors when freeing statement
          }
        }
      }
    }, { operation: 'query', sql, params });
  }

  async run(sql, params = []) {
    return this.executeWithRetry(async () => {
      let stmt = null;
      try {
        // Validate database connection
        if (!this.db) {
          throw new Error('Database connection not established');
        }

        // Prepare statement with error handling
        try {
          stmt = this.db.prepare(sql);
        } catch (prepareError) {
          throw new Error(`SQL preparation failed: ${prepareError.message || prepareError.toString() || 'Invalid SQL statement'}`);
        }

        // Execute statement with enhanced error handling
        try {
          stmt.run(params);
        } catch (runError) {
          // sql.js errors might not have standard properties, so we need to extract information carefully
          const errorMessage = this.extractErrorMessage(runError);
          const errorCode = runError.code || runError.errno || 'UNKNOWN_ERROR';

          // Create a proper error with all available information
          const enhancedError = new Error(errorMessage);
          enhancedError.code = errorCode;
          enhancedError.originalError = runError;
          enhancedError.sql = sql;
          enhancedError.params = params;

          throw enhancedError;
        }

        const changes = this.db.getRowsModified();
        const lastId = this.db.exec('SELECT last_insert_rowid() as id')[0]?.values[0]?.[0] || null;

        this.saveDatabase(); // Save after each write operation
        return { id: lastId, changes };
      } finally {
        // Always free the statement if it was created
        if (stmt) {
          try {
            stmt.free();
          } catch (freeError) {
            // Ignore errors when freeing statement
          }
        }
      }
    }, { operation: 'run', sql, params });
  }

  async get(sql, params = []) {
    return this.executeWithRetry(async () => {
      let stmt = null;
      try {
        // Validate database connection
        if (!this.db) {
          throw new Error('Database connection not established');
        }

        stmt = this.db.prepare(sql);
        let result = null;
        if (stmt.step()) {
          result = stmt.getAsObject();
        }
        return result;
      } catch (error) {
        // Enhance error with context
        const errorMessage = this.extractErrorMessage(error);
        const enhancedError = new Error(errorMessage);
        enhancedError.code = error.code || error.errno || 'GET_ERROR';
        enhancedError.originalError = error;
        enhancedError.sql = sql;
        enhancedError.params = params;
        throw enhancedError;
      } finally {
        if (stmt) {
          try {
            stmt.free();
          } catch (freeError) {
            // Ignore errors when freeing statement
          }
        }
      }
    }, { operation: 'get', sql, params });
  }

  // Transaction support with error handling
  async beginTransaction() {
    try {
      this.db.exec('BEGIN TRANSACTION');
    } catch (error) {
      throw this.handleDatabaseError(error, 'beginTransaction');
    }
  }

  async commitTransaction() {
    try {
      this.db.exec('COMMIT');
      this.saveDatabase(); // Save after commit
    } catch (error) {
      throw this.handleDatabaseError(error, 'commitTransaction');
    }
  }

  async rollbackTransaction() {
    try {
      this.db.exec('ROLLBACK');
    } catch (error) {
      throw this.handleDatabaseError(error, 'rollbackTransaction');
    }
  }

  // Execute multiple operations in a transaction
  async executeInTransaction(operations) {
    try {
      await this.beginTransaction();

      const results = [];
      for (const operation of operations) {
        const result = await operation();
        results.push(result);
      }

      await this.commitTransaction();
      return results;
    } catch (error) {
      try {
        await this.rollbackTransaction();
      } catch (rollbackError) {
        // Production mode - no console logging
      }
      throw this.handleDatabaseError(error, 'executeInTransaction');
    }
  }

  // Helper method to safely add columns if they don't exist
  async addColumnIfNotExists(tableName, columnName, columnDefinition) {
    try {
      // Check if column exists by querying table info
      const tableInfo = await this.query(`PRAGMA table_info(${tableName})`);
      const columnExists = tableInfo.some(column => column.name === columnName);

      if (!columnExists) {
        await this.run(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnDefinition}`);
      }
    } catch (error) {
      // If there's any error, silently ignore it as the column might already exist
      // or there might be other schema-related issues that are not critical
    }
  }

  // Dynamic institution creation for offline-first design
  async createOrFindInstitution(institutionName, defaultData = {}) {
    try {
      // Get all institutions and search for existing one by name (case-insensitive)
      const allInstitutions = await this.query('SELECT * FROM institution');
      const searchName = institutionName.trim().toLowerCase();
      const existingInstitution = allInstitutions.find(inst =>
        inst.name.toLowerCase() === searchName
      );

      if (existingInstitution) {
        return existingInstitution.id;
      }

      // Create new institution with minimal required data (offline-only)
      const institutionId = uuidv4();
      const institutionData = {
        name: institutionName.trim(),
        address: defaultData.address || 'Address not provided'
      };

      await this.run(
        `INSERT INTO institution (id, name, address, created_at, updated_at)
         VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [institutionId, institutionData.name, institutionData.address]
      );

      return institutionId;
    } catch (error) {
      throw new Error(`Failed to create or find institution: ${error.message}`);
    }
  }

  // User management methods
  async createUser(userData) {
    try {
      const { username, password, firstName, lastName, role, teacherId, studentId, institutionId, institutionName, createdBy } = userData;

      // Validate required fields (offline-only, no email required)
      if (!username || !password || !firstName || !lastName || !role) {
        throw new Error('Missing required fields');
      }

      // Handle dynamic institution creation for offline-first design
      let finalInstitutionId = institutionId;
      if (!institutionId && institutionName) {
        // Create or find institution by name
        finalInstitutionId = await this.createOrFindInstitution(institutionName, {
          email: email.split('@')[1] ? `admin@${email.split('@')[1]}` : undefined
        });
      } else if (!institutionId && !institutionName) {
        throw new Error('Either institutionId or institutionName must be provided');
      }

      // Validate password strength
      if (!this.validatePassword(password)) {
        throw new Error('Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character');
      }

      // Comprehensive data validation before database insert (offline-only, no email/phone)
      await this.validateUserData({ username, firstName, lastName, role, teacherId, studentId, institutionId: finalInstitutionId }, true);

      // Check for duplicate username only (no email in offline mode)
      await this.checkUserDuplicates(username);

      // Validate teacher ID if provided
      if (teacherId && role === 'teacher') {
        await this.checkTeacherIdDuplicate(teacherId);
      }

      const hashedPassword = await bcrypt.hash(password, 8);
      const userId = uuidv4();

      // Enhanced logging for debugging - ensure no undefined values (offline-only)
      const insertData = [
        userId,
        username,
        hashedPassword,
        firstName,
        lastName,
        role,
        teacherId || null,
        studentId || null,
        finalInstitutionId
      ];

      try {
        await this.run(
          `INSERT INTO users (id, username, password_hash, first_name, last_name, role, teacher_id, student_id, institution_id)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          insertData
        );
      } catch (insertError) {
        // Add context to the error for better debugging
        const enhancedError = new Error(`User insert failed: ${this.extractErrorMessage(insertError)}`);
        enhancedError.userData = { username, firstName, lastName, role, teacherId, studentId, institutionId };
        enhancedError.originalError = insertError;
        throw enhancedError;
      }

      // Log the user creation
      if (createdBy) {
        await this.logAuditAction(createdBy, 'CREATE_USER', 'users', userId, null, {
          username, email, firstName, lastName, role, teacherId, phone
        });
      }

      return {
        success: true,
        data: { id: userId, username, email, firstName, lastName, role, teacherId, phone }
      };
    } catch (error) {
      // Provide more specific error messages for database constraint failures
      // Ensure error.message exists and provide fallback
      const errorMsg = error.message || error.toString() || 'Unknown database error occurred';
      let errorMessage = errorMsg;

      if (errorMsg.includes('CHECK constraint failed') && errorMsg.includes('username')) {
        errorMessage = 'Username must be 3-30 characters, start with a letter, and contain only letters, numbers, hyphens, and underscores';
      } else if (errorMsg.includes('CHECK constraint failed') && errorMsg.includes('first_name')) {
        errorMessage = 'First name must be 1-50 characters and cannot contain numbers';
      } else if (errorMsg.includes('CHECK constraint failed') && errorMsg.includes('last_name')) {
        errorMessage = 'Last name must be 1-50 characters and cannot contain numbers';
      } else if (errorMsg.includes('CHECK constraint failed') && errorMsg.includes('role')) {
        errorMessage = 'Invalid role. Must be one of: super_admin, admin, teacher, student';
      } else if (errorMsg.includes('UNIQUE constraint failed') && errorMsg.includes('username')) {
        errorMessage = 'Username already exists. Please choose a different username';
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async authenticateUser(credentials) {
    try {
      const { username, password } = credentials;

      // Use query method instead of get for reliable parameter binding
      const userResults = await this.query(
        'SELECT * FROM users WHERE username = ? AND is_active = 1',
        [username]
      );

      if (!userResults || userResults.length === 0) {
        throw new Error('Invalid username or password');
      }

      const user = userResults[0];

      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        throw new Error('Invalid username or password');
      }

      // Remove password hash from response
      const { password_hash, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  async getAllUsers(filters = {}) {
    try {
      let query = `
        SELECT u.*, i.name as institution_name
        FROM users u
        LEFT JOIN institution i ON u.institution_id = i.id
        WHERE u.is_active = 1
      `;
      const params = [];

      if (filters.role) {
        query += ' AND u.role = ?';
        params.push(filters.role);
      }

      if (filters.institutionId) {
        query += ' AND u.institution_id = ?';
        params.push(filters.institutionId);
      }

      query += ' ORDER BY u.created_at DESC';

      const users = await this.query(query, params);
      return users.map(user => {
        const { password_hash, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });
    } catch (error) {
      throw new Error(`Failed to get users: ${error.message}`);
    }
  }

  // Enhanced admin management methods
  async updateUser(userId, userData, updatedBy) {
    try {
      const { firstName, lastName, email, phone, teacherId, role, isActive } = userData;

      // Get current user data for audit log - use query method for reliable parameter binding
      const currentUserResults = await this.query('SELECT * FROM users WHERE id = ?', [userId]);
      if (!currentUserResults || currentUserResults.length === 0) {
        throw new Error('User not found');
      }
      const currentUser = currentUserResults[0];

      // Check for email duplicates if email is being changed
      if (email && email !== currentUser.email) {
        const existingUserResults = await this.query('SELECT id FROM users WHERE email = ? AND id != ?', [email, userId]);
        if (existingUserResults && existingUserResults.length > 0) {
          throw new Error('Email already exists');
        }
      }

      // Check for teacher ID duplicates if being changed
      if (teacherId && teacherId !== currentUser.teacher_id) {
        await this.checkTeacherIdDuplicate(teacherId, userId);
      }

      await this.run(
        `UPDATE users SET
         first_name = COALESCE(?, first_name),
         last_name = COALESCE(?, last_name),
         email = COALESCE(?, email),
         phone = COALESCE(?, phone),
         teacher_id = COALESCE(?, teacher_id),
         role = COALESCE(?, role),
         is_active = COALESCE(?, is_active),
         updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [firstName, lastName, email, phone, teacherId, role, isActive, userId]
      );

      // Log the update
      await this.logAuditAction(updatedBy, 'UPDATE_USER', 'users', userId,
        { firstName: currentUser.first_name, lastName: currentUser.last_name, email: currentUser.email },
        { firstName, lastName, email, phone, teacherId, role, isActive }
      );

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to update user: ${error.message}`);
    }
  }

  // resetUserPassword method moved to line 3845 with enhanced password history checking

  async deactivateUser(userId, deactivatedBy) {
    try {
      await this.run(
        'UPDATE users SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [userId]
      );

      // Log the deactivation
      await this.logAuditAction(deactivatedBy, 'DEACTIVATE_USER', 'users', userId,
        { isActive: true }, { isActive: false }
      );

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to deactivate user: ${error.message}`);
    }
  }

  async activateUser(userId, activatedBy) {
    try {
      await this.run(
        'UPDATE users SET is_active = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [userId]
      );

      // Log the activation
      await this.logAuditAction(activatedBy, 'ACTIVATE_USER', 'users', userId,
        { isActive: false }, { isActive: true }
      );

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to activate user: ${error.message}`);
    }
  }

  // Batch management methods
  async createBatch(batchData) {
    try {
      const { name, description, academicYear, courseType, teacherId, institutionId, studentIds = [] } = batchData;
      const batchId = uuidv4();

      await this.run(
        `INSERT INTO batches (id, name, description, academic_year, course_type, teacher_id, institution_id)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [batchId, name, description, academicYear || '2024-25', courseType || 'Regular', teacherId, institutionId]
      );

      // Add students to batch
      for (const studentId of studentIds) {
        await this.run(
          'INSERT INTO batch_students (id, batch_id, student_id) VALUES (?, ?, ?)',
          [uuidv4(), batchId, studentId]
        );
      }

      return { id: batchId, name, description, academicYear, courseType, teacherId, institutionId };
    } catch (error) {
      throw new Error(`Failed to create batch: ${error.message}`);
    }
  }

  // Enhanced batch creation with inline student creation
  async createBatchWithStudents(batchData, studentsData, createdBy) {
    try {
      const {
        name,
        academicYear,
        courseType,
        description,
        teacherId,
        institutionId
      } = batchData;

      // Validate batch data
      if (!name || !academicYear || !courseType || !teacherId) {
        throw new Error('Batch name, academic year, course type, and teacher ID are required');
      }

      // Check for duplicate batch name within teacher's scope
      const existingBatch = await this.get(
        'SELECT id FROM batches WHERE name = ? AND teacher_id = ? AND is_active = 1',
        [name, teacherId]
      );

      if (existingBatch) {
        throw new Error('A batch with this name already exists for this teacher');
      }

      // Validate students data
      if (!studentsData || studentsData.length === 0) {
        throw new Error('At least one student is required');
      }

      if (studentsData.length > 50) {
        throw new Error('Maximum 50 students allowed per batch');
      }

      // Check for duplicate student IDs within the institution
      const studentIds = studentsData.map(s => s.studentId);
      const duplicateCheck = await this.query(
        `SELECT student_id FROM users WHERE student_id IN (${studentIds.map(() => '?').join(',')}) AND institution_id = ?`,
        [...studentIds, institutionId]
      );

      if (duplicateCheck.length > 0) {
        const duplicateIds = duplicateCheck.map(d => d.student_id);
        throw new Error(`Student IDs already exist: ${duplicateIds.join(', ')}`);
      }

      // Start transaction
      const batchId = uuidv4();
      const createdStudentIds = [];

      try {
        // Create batch
        await this.run(
          `INSERT INTO batches (id, name, academic_year, course_type, description, teacher_id, institution_id)
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [batchId, name, academicYear, courseType, description, teacherId, institutionId]
        );

        // Create students and add to batch
        for (const studentData of studentsData) {
          const {
            fullName,
            studentId,
            password,
            yearOfBirth
          } = studentData;

          // Split full name into first and last name
          const nameParts = fullName.trim().split(' ');
          const firstName = nameParts[0];
          const lastName = nameParts.slice(1).join(' ') || firstName;

          // Generate username from student ID
          const username = studentId.toLowerCase();

          // Hash password
          const passwordHash = await bcrypt.hash(password, 8);

          // Create user account (offline-only, no email)
          const userId = uuidv4();
          await this.run(
            `INSERT INTO users (id, username, password_hash, first_name, last_name, role, student_id, institution_id, password_reset_required)
             VALUES (?, ?, ?, ?, ?, 'student', ?, ?, 1)`,
            [userId, username, passwordHash, firstName, lastName, studentId, institutionId]
          );

          createdStudentIds.push(userId);

          // Add student to batch
          await this.run(
            'INSERT INTO batch_students (id, batch_id, student_id) VALUES (?, ?, ?)',
            [uuidv4(), batchId, userId]
          );

          // Log student creation
          if (createdBy) {
            await this.logAuditAction(createdBy, 'CREATE_STUDENT', 'users', userId, null,
              {
                fullName,
                studentId,
                role: 'student',
                batchId,
                yearOfBirth
              }
            );
          }
        }

        // Log batch creation
        if (createdBy) {
          await this.logAuditAction(createdBy, 'CREATE_BATCH', 'batches', batchId, null,
            {
              name,
              academicYear,
              courseType,
              description,
              studentCount: studentsData.length
            }
          );
        }

        return {
          batch: {
            id: batchId,
            name,
            academicYear,
            courseType,
            description,
            teacherId,
            institutionId,
            studentCount: studentsData.length
          },
          students: createdStudentIds
        };

      } catch (error) {
        // Rollback: Delete created students if batch creation fails
        for (const studentId of createdStudentIds) {
          try {
            await this.run('DELETE FROM users WHERE id = ?', [studentId]);
          } catch (rollbackError) {
            console.error('Rollback error:', rollbackError);
          }
        }
        throw error;
      }

    } catch (error) {
      throw new Error(`Failed to create batch with students: ${error.message}`);
    }
  }

  async getAllBatches(filters = {}) {
    try {
      let query = `
        SELECT b.*, u.first_name || ' ' || u.last_name as teacher_name,
               i.name as institution_name,
               COUNT(bs.student_id) as student_count
        FROM batches b
        LEFT JOIN users u ON b.teacher_id = u.id
        LEFT JOIN institution i ON b.institution_id = i.id
        LEFT JOIN batch_students bs ON b.id = bs.batch_id
        WHERE b.is_active = 1
      `;
      const params = [];

      if (filters.teacherId) {
        query += ' AND b.teacher_id = ?';
        params.push(filters.teacherId);
      }

      if (filters.institutionId) {
        query += ' AND b.institution_id = ?';
        params.push(filters.institutionId);
      }

      if (filters.academicYear) {
        query += ' AND b.academic_year = ?';
        params.push(filters.academicYear);
      }

      if (filters.courseType) {
        query += ' AND b.course_type = ?';
        params.push(filters.courseType);
      }

      query += ' GROUP BY b.id ORDER BY b.created_at DESC';

      return await this.query(query, params);
    } catch (error) {
      throw new Error(`Failed to get batches: ${error.message}`);
    }
  }

  // Check if batch name is unique for teacher
  async checkBatchNameUnique(name, teacherId, excludeBatchId = null) {
    try {
      let query = 'SELECT id FROM batches WHERE name = ? AND teacher_id = ? AND is_active = 1';
      const params = [name, teacherId];

      if (excludeBatchId) {
        query += ' AND id != ?';
        params.push(excludeBatchId);
      }

      const existing = await this.get(query, params);
      return !existing;
    } catch (error) {
      throw new Error(`Failed to check batch name uniqueness: ${error.message}`);
    }
  }

  // Check if student IDs are unique within institution
  async checkStudentIdsUnique(studentIds, institutionId) {
    try {
      if (!studentIds || studentIds.length === 0) {return { unique: true, duplicates: [] };}

      const placeholders = studentIds.map(() => '?').join(',');
      const query = `SELECT student_id FROM users WHERE student_id IN (${placeholders}) AND institution_id = ?`;
      const params = [...studentIds, institutionId];

      const existing = await this.query(query, params);
      const duplicates = existing.map(row => row.student_id);

      return {
        unique: duplicates.length === 0,
        duplicates
      };
    } catch (error) {
      throw new Error(`Failed to check student ID uniqueness: ${error.message}`);
    }
  }

  // Get detailed batch information with students
  async getBatchDetails(batchId, teacherId = null) {
    try {
      let batchQuery = `
        SELECT b.*, u.first_name || ' ' || u.last_name as teacher_name,
               i.name as institution_name
        FROM batches b
        LEFT JOIN users u ON b.teacher_id = u.id
        LEFT JOIN institution i ON b.institution_id = i.id
        WHERE b.id = ? AND b.is_active = 1
      `;
      const batchParams = [batchId];

      // If teacherId is provided, ensure teacher can only access their own batches
      if (teacherId) {
        batchQuery += ' AND b.teacher_id = ?';
        batchParams.push(teacherId);
      }

      const batch = await this.get(batchQuery, batchParams);
      if (!batch) {
        throw new Error('Batch not found or access denied');
      }

      // Get students in the batch
      const studentsQuery = `
        SELECT u.*, bs.created_at as added_to_batch
        FROM users u
        INNER JOIN batch_students bs ON u.id = bs.student_id
        WHERE bs.batch_id = ? AND u.is_active = 1
        ORDER BY u.first_name, u.last_name
      `;
      const students = await this.query(studentsQuery, [batchId]);

      return {
        ...batch,
        students,
        student_count: students.length
      };
    } catch (error) {
      throw new Error(`Failed to get batch details: ${error.message}`);
    }
  }

  // Update batch information
  async updateBatch(batchId, batchData, updatedBy, teacherId = null) {
    try {
      const { name, academicYear, courseType, description } = batchData;

      // Validate required fields
      if (!name || !academicYear || !courseType) {
        throw new Error('Batch name, academic year, and course type are required');
      }

      // Get existing batch to verify ownership and get old values
      let existingQuery = 'SELECT * FROM batches WHERE id = ? AND is_active = 1';
      const existingParams = [batchId];

      if (teacherId) {
        existingQuery += ' AND teacher_id = ?';
        existingParams.push(teacherId);
      }

      const existingBatch = await this.get(existingQuery, existingParams);
      if (!existingBatch) {
        throw new Error('Batch not found or access denied');
      }

      // Check for duplicate batch name (excluding current batch)
      const duplicateCheck = await this.get(
        'SELECT id FROM batches WHERE name = ? AND teacher_id = ? AND id != ? AND is_active = 1',
        [name, existingBatch.teacher_id, batchId]
      );

      if (duplicateCheck) {
        throw new Error('A batch with this name already exists for this teacher');
      }

      // Update batch
      await this.run(
        `UPDATE batches SET
         name = ?, academic_year = ?, course_type = ?, description = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [name, academicYear, courseType, description, batchId]
      );

      // Log the update
      if (updatedBy) {
        await this.logAuditAction(updatedBy, 'UPDATE_BATCH', 'batches', batchId,
          {
            name: existingBatch.name,
            academicYear: existingBatch.academic_year,
            courseType: existingBatch.course_type,
            description: existingBatch.description
          },
          { name, academicYear, courseType, description }
        );
      }

      return { id: batchId, name, academicYear, courseType, description };
    } catch (error) {
      throw new Error(`Failed to update batch: ${error.message}`);
    }
  }

  // Add students to existing batch
  async addStudentsToBatch(batchId, studentsData, addedBy, teacherId = null) {
    try {
      // Verify batch exists and teacher has access
      let batchQuery = 'SELECT * FROM batches WHERE id = ? AND is_active = 1';
      const batchParams = [batchId];

      if (teacherId) {
        batchQuery += ' AND teacher_id = ?';
        batchParams.push(teacherId);
      }

      const batch = await this.get(batchQuery, batchParams);
      if (!batch) {
        throw new Error('Batch not found or access denied');
      }

      // Validate students data
      if (!studentsData || studentsData.length === 0) {
        throw new Error('At least one student is required');
      }

      // Check current batch size
      const currentCount = await this.get(
        'SELECT COUNT(*) as count FROM batch_students WHERE batch_id = ?',
        [batchId]
      );

      if (currentCount.count + studentsData.length > 50) {
        throw new Error(`Adding ${studentsData.length} students would exceed the maximum batch size of 50`);
      }

      // Check for duplicate student IDs
      const studentIds = studentsData.map(s => s.studentId);
      const duplicateCheck = await this.checkStudentIdsUnique(studentIds, batch.institution_id);
      if (!duplicateCheck.unique) {
        throw new Error(`Student IDs already exist: ${duplicateCheck.duplicates.join(', ')}`);
      }

      const addedStudentIds = [];

      try {
        // Create students and add to batch
        for (const studentData of studentsData) {
          const {
            fullName,
            studentId,
            password
          } = studentData;

          // Split full name
          const nameParts = fullName.trim().split(' ');
          const firstName = nameParts[0];
          const lastName = nameParts.slice(1).join(' ') || firstName;

          // Generate username from student ID
          const username = studentId.toLowerCase();

          // Hash password
          const passwordHash = await bcrypt.hash(password, 8);

          // Create user account (offline-only, no email)
          const userId = uuidv4();
          await this.run(
            `INSERT INTO users (id, username, password_hash, first_name, last_name, role, student_id, institution_id, password_reset_required)
             VALUES (?, ?, ?, ?, ?, 'student', ?, ?, 1)`,
            [userId, username, passwordHash, firstName, lastName, studentId, batch.institution_id]
          );

          addedStudentIds.push(userId);

          // Add student to batch
          await this.run(
            'INSERT INTO batch_students (id, batch_id, student_id) VALUES (?, ?, ?)',
            [uuidv4(), batchId, userId]
          );

          // Log student creation
          if (addedBy) {
            await this.logAuditAction(addedBy, 'ADD_STUDENT_TO_BATCH', 'users', userId, null,
              {
                fullName,
                studentId,
                batchId,
                batchName: batch.name
              }
            );
          }
        }

        return {
          addedCount: studentsData.length,
          studentIds: addedStudentIds
        };

      } catch (error) {
        // Rollback: Delete created students
        for (const studentId of addedStudentIds) {
          try {
            await this.run('DELETE FROM users WHERE id = ?', [studentId]);
            await this.run('DELETE FROM batch_students WHERE student_id = ?', [studentId]);
          } catch (rollbackError) {
            console.error('Rollback error:', rollbackError);
          }
        }
        throw error;
      }

    } catch (error) {
      throw new Error(`Failed to add students to batch: ${error.message}`);
    }
  }

  // Remove student from batch
  async removeStudentFromBatch(batchId, studentId, removedBy, teacherId = null) {
    try {
      // Verify batch exists and teacher has access
      let batchQuery = 'SELECT * FROM batches WHERE id = ? AND is_active = 1';
      const batchParams = [batchId];

      if (teacherId) {
        batchQuery += ' AND teacher_id = ?';
        batchParams.push(teacherId);
      }

      const batch = await this.get(batchQuery, batchParams);
      if (!batch) {
        throw new Error('Batch not found or access denied');
      }

      // Get student information
      const student = await this.get(
        'SELECT u.*, bs.id as batch_student_id FROM users u INNER JOIN batch_students bs ON u.id = bs.student_id WHERE bs.batch_id = ? AND u.id = ?',
        [batchId, studentId]
      );

      if (!student) {
        throw new Error('Student not found in this batch');
      }

      // Remove student from batch
      await this.run('DELETE FROM batch_students WHERE batch_id = ? AND student_id = ?', [batchId, studentId]);

      // Log the removal
      if (removedBy) {
        await this.logAuditAction(removedBy, 'REMOVE_STUDENT_FROM_BATCH', 'batch_students', student.batch_student_id,
          {
            studentName: `${student.first_name} ${student.last_name}`,
            studentId: student.student_id,
            batchId,
            batchName: batch.name
          },
          null
        );
      }

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to remove student from batch: ${error.message}`);
    }
  }

  // Archive/deactivate batch
  async archiveBatch(batchId, archivedBy, teacherId = null) {
    try {
      // Verify batch exists and teacher has access
      let batchQuery = 'SELECT * FROM batches WHERE id = ? AND is_active = 1';
      const batchParams = [batchId];

      if (teacherId) {
        batchQuery += ' AND teacher_id = ?';
        batchParams.push(teacherId);
      }

      const batch = await this.get(batchQuery, batchParams);
      if (!batch) {
        throw new Error('Batch not found or access denied');
      }

      // Archive batch
      await this.run('UPDATE batches SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [batchId]);

      // Log the archival
      if (archivedBy) {
        await this.logAuditAction(archivedBy, 'ARCHIVE_BATCH', 'batches', batchId,
          { name: batch.name, isActive: true },
          { name: batch.name, isActive: false }
        );
      }

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to archive batch: ${error.message}`);
    }
  }

  // Activate batch
  async activateBatch(batchId, activatedBy, teacherId = null) {
    try {
      // Verify batch exists and teacher has access
      let batchQuery = 'SELECT * FROM batches WHERE id = ? AND is_active = 0';
      const batchParams = [batchId];

      if (teacherId) {
        batchQuery += ' AND teacher_id = ?';
        batchParams.push(teacherId);
      }

      const batch = await this.get(batchQuery, batchParams);
      if (!batch) {
        throw new Error('Batch not found or access denied');
      }

      // Activate batch
      await this.run('UPDATE batches SET is_active = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [batchId]);

      // Log the activation
      if (activatedBy) {
        await this.logAuditAction(activatedBy, 'ACTIVATE_BATCH', 'batches', batchId,
          { name: batch.name, isActive: false },
          { name: batch.name, isActive: true }
        );
      }

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to activate batch: ${error.message}`);
    }
  }

  // Get batch statistics for teacher
  async getBatchStatistics(teacherId) {
    try {
      const stats = await this.get(`
        SELECT
          COUNT(*) as total_batches,
          COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_batches,
          COUNT(CASE WHEN is_active = 0 THEN 1 END) as archived_batches
        FROM batches
        WHERE teacher_id = ?
      `, [teacherId]);

      const studentStats = await this.get(`
        SELECT COUNT(DISTINCT bs.student_id) as total_students
        FROM batches b
        INNER JOIN batch_students bs ON b.id = bs.batch_id
        WHERE b.teacher_id = ? AND b.is_active = 1
      `, [teacherId]);

      const recentActivity = await this.query(`
        SELECT b.name, b.created_at, COUNT(bs.student_id) as student_count
        FROM batches b
        LEFT JOIN batch_students bs ON b.id = bs.batch_id
        WHERE b.teacher_id = ? AND b.is_active = 1
        GROUP BY b.id
        ORDER BY b.created_at DESC
        LIMIT 5
      `, [teacherId]);

      return {
        ...stats,
        total_students: studentStats.total_students || 0,
        recent_activity: recentActivity
      };
    } catch (error) {
      throw new Error(`Failed to get batch statistics: ${error.message}`);
    }
  }

  // Enhanced Assessment management methods
  async createAssessmentWithQuestions(assessmentData, questionsData, createdBy) {
    try {
      const {
        name,
        shortDescription,
        academicYear,
        courseType,
        assessmentType,
        dueDate,
        instructions,
        institutionId
      } = assessmentData;

      // Validate assessment data
      if (!name || !shortDescription || !academicYear || !courseType || !assessmentType || !dueDate) {
        throw new Error('All required assessment fields must be provided');
      }

      // Check for duplicate assessment name within institution
      const existingAssessment = await this.get(
        'SELECT id FROM assessments WHERE name = ? AND institution_id = ? AND is_active = 1',
        [name, institutionId]
      );

      if (existingAssessment) {
        throw new Error('An assessment with this name already exists in your institution');
      }

      // Validate questions data
      if (!questionsData || questionsData.length === 0) {
        throw new Error('At least one question is required');
      }

      const assessmentId = uuidv4();

      try {
        // Create assessment
        await this.run(
          `INSERT INTO assessments (id, name, short_description, academic_year, course_type, assessment_type, due_date, instructions, created_by, institution_id)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [assessmentId, name, shortDescription, academicYear, courseType, assessmentType, dueDate, instructions, createdBy, institutionId]
        );

        // Create questions and options
        for (let i = 0; i < questionsData.length; i++) {
          const questionData = questionsData[i];
          const questionId = uuidv4();

          // Create question
          await this.run(
            `INSERT INTO assessment_questions (id, assessment_id, question_text, question_type, question_order)
             VALUES (?, ?, ?, ?, ?)`,
            [questionId, assessmentId, questionData.questionText, questionData.questionType || 'MCQ', i + 1]
          );

          // Create options for MCQ questions
          if (questionData.questionType === 'MCQ' && questionData.options) {
            for (let j = 0; j < questionData.options.length; j++) {
              const option = questionData.options[j];
              await this.run(
                `INSERT INTO question_options (id, question_id, option_text, marks, option_order)
                 VALUES (?, ?, ?, ?, ?)`,
                [uuidv4(), questionId, option.text, option.marks, j + 1]
              );
            }
          }
        }

        // Log the creation
        if (createdBy) {
          await this.logAuditAction(createdBy, 'CREATE_ASSESSMENT', 'assessments', assessmentId, null,
            {
              name,
              academicYear,
              courseType,
              assessmentType,
              questionCount: questionsData.length
            }
          );
        }

        return {
          id: assessmentId,
          name,
          shortDescription,
          academicYear,
          courseType,
          assessmentType,
          questionCount: questionsData.length
        };

      } catch (error) {
        // Rollback: Delete created assessment and related data
        try {
          await this.run('DELETE FROM assessments WHERE id = ?', [assessmentId]);
        } catch (rollbackError) {
          console.error('Rollback error:', rollbackError);
        }
        throw error;
      }

    } catch (error) {
      throw new Error(`Failed to create assessment: ${error.message}`);
    }
  }

  // Check if assessment name is unique within institution
  async checkAssessmentNameUnique(name, institutionId, excludeAssessmentId) {
    try {
      let query = 'SELECT id FROM assessments WHERE name = ? AND institution_id = ? AND is_active = 1';
      const params = [name, institutionId];

      if (excludeAssessmentId) {
        query += ' AND id != ?';
        params.push(excludeAssessmentId);
      }

      const existing = await this.get(query, params);
      return { unique: !existing };
    } catch (error) {
      throw new Error(`Failed to check assessment name uniqueness: ${error.message}`);
    }
  }

  // Get all assessments with enhanced filtering
  async getAllAssessments(filters = {}) {
    try {
      let query = `
        SELECT a.*, u.first_name || ' ' || u.last_name as created_by_name,
               COUNT(aq.id) as question_count,
               COUNT(ab.batch_id) as assigned_batch_count
        FROM assessments a
        LEFT JOIN users u ON a.created_by = u.id
        LEFT JOIN assessment_questions aq ON a.id = aq.assessment_id AND aq.is_active = 1
        LEFT JOIN assessment_batches ab ON a.id = ab.assessment_id
        WHERE a.is_active = 1
      `;
      const params = [];

      if (filters.institutionId) {
        query += ' AND a.institution_id = ?';
        params.push(filters.institutionId);
      }

      if (filters.createdBy) {
        query += ' AND a.created_by = ?';
        params.push(filters.createdBy);
      }

      if (filters.academicYear) {
        query += ' AND a.academic_year = ?';
        params.push(filters.academicYear);
      }

      if (filters.courseType) {
        query += ' AND a.course_type = ?';
        params.push(filters.courseType);
      }

      if (filters.assessmentType) {
        query += ' AND a.assessment_type = ?';
        params.push(filters.assessmentType);
      }

      if (filters.isPublished !== undefined) {
        query += ' AND a.is_published = ?';
        params.push(filters.isPublished ? 1 : 0);
      }

      query += ' GROUP BY a.id ORDER BY a.created_at DESC';

      const assessments = await this.query(query, params);
      return assessments;
    } catch (error) {
      throw new Error(`Failed to get assessments: ${error.message}`);
    }
  }

  // Get assessment details with questions and options
  async getAssessmentDetails(assessmentId, institutionId = null) {
    try {
      let assessmentQuery = `
        SELECT a.*, u.first_name || ' ' || u.last_name as created_by_name
        FROM assessments a
        LEFT JOIN users u ON a.created_by = u.id
        WHERE a.id = ? AND a.is_active = 1
      `;
      const assessmentParams = [assessmentId];

      if (institutionId) {
        assessmentQuery += ' AND a.institution_id = ?';
        assessmentParams.push(institutionId);
      }

      const assessment = await this.get(assessmentQuery, assessmentParams);
      if (!assessment) {
        throw new Error('Assessment not found or access denied');
      }

      // Get questions with options
      const questionsQuery = `
        SELECT aq.*,
               GROUP_CONCAT(
                 json_object(
                   'id', qo.id,
                   'text', qo.option_text,
                   'marks', qo.marks,
                   'order', qo.option_order
                 ) ORDER BY qo.option_order
               ) as options
        FROM assessment_questions aq
        LEFT JOIN question_options qo ON aq.id = qo.question_id
        WHERE aq.assessment_id = ? AND aq.is_active = 1
        GROUP BY aq.id
        ORDER BY aq.question_order
      `;

      const questions = await this.query(questionsQuery, [assessmentId]);

      // Parse options JSON
      const questionsWithOptions = questions.map(question => ({
        ...question,
        options: question.options ?
          question.options.split(',').map(opt => JSON.parse(opt)) : []
      }));

      return {
        ...assessment,
        questions: questionsWithOptions
      };
    } catch (error) {
      throw new Error(`Failed to get assessment details: ${error.message}`);
    }
  }

  // Publish assessment
  async publishAssessment(assessmentId, publishedBy, institutionId = null) {
    try {
      let query = 'SELECT * FROM assessments WHERE id = ? AND is_active = 1';
      const params = [assessmentId];

      if (institutionId) {
        query += ' AND institution_id = ?';
        params.push(institutionId);
      }

      const assessment = await this.get(query, params);
      if (!assessment) {
        throw new Error('Assessment not found or access denied');
      }

      if (assessment.is_published) {
        throw new Error('Assessment is already published');
      }

      // Check if assessment has questions
      const questionCount = await this.get(
        'SELECT COUNT(*) as count FROM assessment_questions WHERE assessment_id = ? AND is_active = 1',
        [assessmentId]
      );

      if (questionCount.count === 0) {
        throw new Error('Cannot publish assessment without questions');
      }

      // Publish assessment
      await this.run(
        'UPDATE assessments SET is_published = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [assessmentId]
      );

      // Log the publication
      if (publishedBy) {
        await this.logAuditAction(publishedBy, 'PUBLISH_ASSESSMENT', 'assessments', assessmentId,
          { isPublished: false },
          { isPublished: true }
        );
      }

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to publish assessment: ${error.message}`);
    }
  }

  // Assign assessment to batches
  async assignAssessmentToBatches(assessmentId, batchIds, assignedBy, institutionId = null) {
    try {
      // Verify assessment exists and user has access
      let assessmentQuery = 'SELECT * FROM assessments WHERE id = ? AND is_active = 1';
      const assessmentParams = [assessmentId];

      if (institutionId) {
        assessmentQuery += ' AND institution_id = ?';
        assessmentParams.push(institutionId);
      }

      const assessment = await this.get(assessmentQuery, assessmentParams);
      if (!assessment) {
        throw new Error('Assessment not found or access denied');
      }

      if (!assessment.is_published) {
        throw new Error('Cannot assign unpublished assessment to batches');
      }

      // Remove existing assignments
      await this.run('DELETE FROM assessment_batches WHERE assessment_id = ?', [assessmentId]);

      // Add new assignments
      for (const batchId of batchIds) {
        await this.run(
          'INSERT INTO assessment_batches (id, assessment_id, batch_id, assigned_by) VALUES (?, ?, ?, ?)',
          [uuidv4(), assessmentId, batchId, assignedBy]
        );
      }

      // Log the assignment
      if (assignedBy) {
        await this.logAuditAction(assignedBy, 'ASSIGN_ASSESSMENT_TO_BATCHES', 'assessment_batches', assessmentId, null,
          {
            assessmentName: assessment.name,
            batchCount: batchIds.length,
            batchIds
          }
        );
      }

      return { success: true, assignedBatches: batchIds.length };
    } catch (error) {
      throw new Error(`Failed to assign assessment to batches: ${error.message}`);
    }
  }

  // Delete assessment (soft delete)
  async deleteAssessment(assessmentId, deletedBy, institutionId = null) {
    try {
      let query = 'SELECT * FROM assessments WHERE id = ? AND is_active = 1';
      const params = [assessmentId];

      if (institutionId) {
        query += ' AND institution_id = ?';
        params.push(institutionId);
      }

      const assessment = await this.get(query, params);
      if (!assessment) {
        throw new Error('Assessment not found or access denied');
      }

      // Soft delete assessment
      await this.run(
        'UPDATE assessments SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [assessmentId]
      );

      // Log the deletion
      if (deletedBy) {
        await this.logAuditAction(deletedBy, 'DELETE_ASSESSMENT', 'assessments', assessmentId,
          { name: assessment.name, isActive: true },
          { name: assessment.name, isActive: false }
        );
      }

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to delete assessment: ${error.message}`);
    }
  }

  // Get batches accessible to user (teachers get their batches, students get their assigned batch)
  async getUserAccessibleBatches(userId, userRole, institutionId = null) {
    try {
      let query, params;

      if (userRole === 'teacher') {
        // Teachers can access batches they created
        query = `
          SELECT b.*, COUNT(bs.student_id) as student_count
          FROM batches b
          LEFT JOIN batch_students bs ON b.id = bs.batch_id
          WHERE b.teacher_id = ? AND b.is_active = 1
        `;
        params = [userId];

        if (institutionId) {
          query += ' AND b.institution_id = ?';
          params.push(institutionId);
        }

        query += ' GROUP BY b.id ORDER BY b.created_at DESC';
      } else if (userRole === 'student') {
        // Students can only access batches they are enrolled in
        query = `
          SELECT b.*, COUNT(bs.student_id) as student_count
          FROM batches b
          INNER JOIN batch_students bs ON b.id = bs.batch_id
          LEFT JOIN batch_students bs2 ON b.id = bs2.batch_id
          WHERE bs.student_id = ? AND b.is_active = 1
        `;
        params = [userId];

        if (institutionId) {
          query += ' AND b.institution_id = ?';
          params.push(institutionId);
        }

        query += ' GROUP BY b.id ORDER BY b.created_at DESC';
      } else {
        // Admins and super_admins can access all batches in their institution
        query = `
          SELECT b.*, COUNT(bs.student_id) as student_count
          FROM batches b
          LEFT JOIN batch_students bs ON b.id = bs.batch_id
          WHERE b.is_active = 1
        `;
        params = [];

        if (institutionId) {
          query += ' AND b.institution_id = ?';
          params.push(institutionId);
        }

        query += ' GROUP BY b.id ORDER BY b.created_at DESC';
      }

      const batches = await this.query(query, params);
      return batches;
    } catch (error) {
      throw new Error(`Failed to get user accessible batches: ${error.message}`);
    }
  }

  // Get assessments assigned to a specific batch
  async getAssessmentsForBatch(batchId, userId = null, userRole = null) {
    try {
      let query = `
        SELECT a.*, u.first_name || ' ' || u.last_name as created_by_name,
               COUNT(aq.id) as question_count,
               ab.assigned_at,
               CASE
                 WHEN datetime('now') < a.due_date THEN 'active'
                 WHEN datetime('now') > a.due_date THEN 'overdue'
                 ELSE 'active'
               END as status
        FROM assessments a
        INNER JOIN assessment_batches ab ON a.id = ab.assessment_id
        LEFT JOIN users u ON a.created_by = u.id
        LEFT JOIN assessment_questions aq ON a.id = aq.assessment_id AND aq.is_active = 1
        WHERE ab.batch_id = ? AND a.is_active = 1 AND a.is_published = 1
      `;
      const params = [batchId];

      // Additional access control for students
      if (userRole === 'student' && userId) {
        // Verify student is in the batch
        const studentInBatch = await this.get(
          'SELECT id FROM batch_students WHERE batch_id = ? AND student_id = ?',
          [batchId, userId]
        );

        if (!studentInBatch) {
          throw new Error('Access denied: Student not enrolled in this batch');
        }
      }

      query += ' GROUP BY a.id ORDER BY a.due_date ASC, a.created_at DESC';

      const assessments = await this.query(query, params);
      return assessments;
    } catch (error) {
      throw new Error(`Failed to get assessments for batch: ${error.message}`);
    }
  }

  // Get students in a batch with their assessment progress
  async getBatchStudentsWithProgress(batchId, assessmentId = null, userId = null, userRole = null) {
    try {
      // Verify user has access to this batch
      if (userRole === 'teacher') {
        const teacherBatch = await this.get(
          'SELECT id FROM batches WHERE id = ? AND teacher_id = ? AND is_active = 1',
          [batchId, userId]
        );
        if (!teacherBatch) {
          throw new Error('Access denied: Teacher does not manage this batch');
        }
      } else if (userRole === 'student') {
        const studentInBatch = await this.get(
          'SELECT id FROM batch_students WHERE batch_id = ? AND student_id = ?',
          [batchId, userId]
        );
        if (!studentInBatch) {
          throw new Error('Access denied: Student not enrolled in this batch');
        }
      }

      let query = `
        SELECT u.id, u.first_name, u.last_name, u.student_id, u.email,
               bs.created_at as enrolled_at,
               u.is_active as student_active
        FROM users u
        INNER JOIN batch_students bs ON u.id = bs.student_id
        WHERE bs.batch_id = ? AND u.role = 'student' AND u.is_active = 1
      `;
      const params = [batchId];

      // If specific assessment is requested, add progress information
      if (assessmentId) {
        query = `
          SELECT u.id, u.first_name, u.last_name, u.student_id, u.email,
                 bs.created_at as enrolled_at,
                 u.is_active as student_active,
                 CASE
                   WHEN ar.id IS NOT NULL THEN 'completed'
                   ELSE 'not_started'
                 END as assessment_status,
                 ar.submitted_at,
                 ar.total_score
          FROM users u
          INNER JOIN batch_students bs ON u.id = bs.student_id
          LEFT JOIN assessment_responses ar ON u.id = ar.student_id AND ar.assessment_id = ?
          WHERE bs.batch_id = ? AND u.role = 'student' AND u.is_active = 1
        `;
        params.unshift(assessmentId);
        params.push(batchId);
      }

      // For students, only return their own record
      if (userRole === 'student') {
        query += ' AND u.id = ?';
        params.push(userId);
      }

      query += ' ORDER BY u.first_name, u.last_name';

      const students = await this.query(query, params);
      return students;
    } catch (error) {
      throw new Error(`Failed to get batch students with progress: ${error.message}`);
    }
  }

  // Get student's assessment attempt history
  async getStudentAssessmentHistory(studentId, assessmentId = null, batchId = null) {
    try {
      let query = `
        SELECT ar.*, a.name as assessment_name, a.assessment_type, a.due_date,
               b.name as batch_name
        FROM assessment_responses ar
        INNER JOIN assessments a ON ar.assessment_id = a.id
        INNER JOIN assessment_batches ab ON a.id = ab.assessment_id
        INNER JOIN batches b ON ab.batch_id = b.id
        WHERE ar.student_id = ? AND a.is_active = 1
      `;
      const params = [studentId];

      if (assessmentId) {
        query += ' AND ar.assessment_id = ?';
        params.push(assessmentId);
      }

      if (batchId) {
        query += ' AND ab.batch_id = ?';
        params.push(batchId);
      }

      query += ' ORDER BY ar.submitted_at DESC';

      const history = await this.query(query, params);
      return history;
    } catch (error) {
      throw new Error(`Failed to get student assessment history: ${error.message}`);
    }
  }

  // Check if student can access assessment
  async canStudentAccessAssessment(studentId, assessmentId) {
    try {
      const access = await this.get(`
        SELECT a.id, a.name, a.due_date, a.is_published,
               ab.batch_id, b.name as batch_name
        FROM assessments a
        INNER JOIN assessment_batches ab ON a.id = ab.assessment_id
        INNER JOIN batches b ON ab.batch_id = b.id
        INNER JOIN batch_students bs ON b.id = bs.batch_id
        WHERE a.id = ? AND bs.student_id = ? AND a.is_active = 1 AND a.is_published = 1 AND b.is_active = 1
      `, [assessmentId, studentId]);

      if (!access) {
        return { canAccess: false, reason: 'Assessment not found or not assigned to student' };
      }

      // Check if assessment is still available (not past due date)
      const now = new Date();
      const dueDate = new Date(access.due_date);

      if (now > dueDate) {
        return { canAccess: false, reason: 'Assessment is past due date', assessment: access };
      }

      return { canAccess: true, assessment: access };
    } catch (error) {
      throw new Error(`Failed to check student assessment access: ${error.message}`);
    }
  }

  // Generate assessment form HTML for offline completion
  async generateAssessmentForm(assessmentId, teacherId, institutionId = null) {
    try {
      // Verify teacher has access to this assessment
      const assessment = await this.getAssessmentDetails(assessmentId, institutionId);
      if (!assessment) {
        throw new Error('Assessment not found or access denied');
      }

      // Verify teacher has access to batches assigned to this assessment
      const teacherBatches = await this.query(`
        SELECT DISTINCT b.id, b.name
        FROM batches b
        INNER JOIN assessment_batches ab ON b.id = ab.batch_id
        WHERE ab.assessment_id = ? AND b.teacher_id = ? AND b.is_active = 1
      `, [assessmentId, teacherId]);

      if (teacherBatches.length === 0) {
        throw new Error('Access denied: Assessment not assigned to your batches');
      }

      // Get assessment questions and options
      const questions = await this.query(`
        SELECT aq.*, qo.id as option_id, qo.option_text, qo.marks, qo.option_order
        FROM assessment_questions aq
        LEFT JOIN question_options qo ON aq.id = qo.question_id
        WHERE aq.assessment_id = ? AND aq.is_active = 1
        ORDER BY aq.question_order, qo.option_order
      `, [assessmentId]);

      // Group questions with their options
      const groupedQuestions = {};
      questions.forEach(row => {
        if (!groupedQuestions[row.id]) {
          groupedQuestions[row.id] = {
            id: row.id,
            question_text: row.question_text,
            question_type: row.question_type,
            question_order: row.question_order,
            options: []
          };
        }
        if (row.option_id) {
          groupedQuestions[row.id].options.push({
            id: row.option_id,
            text: row.option_text,
            marks: row.marks,
            order: row.option_order
          });
        }
      });

      const questionsList = Object.values(groupedQuestions).sort((a, b) => a.question_order - b.question_order);

      // Generate HTML form
      const htmlContent = this.generateAssessmentHTML(assessment, questionsList);

      // Log the form generation
      await this.logAuditAction(teacherId, 'GENERATE_ASSESSMENT_FORM', 'assessments', assessmentId, null, {
        assessmentName: assessment.name,
        questionCount: questionsList.length
      });

      return {
        html: htmlContent,
        filename: `${assessment.name.replace(/[^a-zA-Z0-9]/g, '_')}_Form.html`,
        assessment: assessment
      };
    } catch (error) {
      throw new Error(`Failed to generate assessment form: ${error.message}`);
    }
  }

  // Generate HTML content for assessment form
  generateAssessmentHTML(assessment, questions) {
    const currentDate = new Date().toLocaleDateString();

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${assessment.name} - Assessment Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .assessment-info {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .question {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .question-header {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .options {
            margin-left: 20px;
        }
        .option {
            margin-bottom: 10px;
            padding: 8px;
            border: 1px solid #eee;
            border-radius: 3px;
            display: flex;
            align-items: center;
        }
        .option input {
            margin-right: 10px;
        }
        .option-text {
            flex: 1;
        }
        .marks {
            font-weight: bold;
            color: #27ae60;
            margin-left: 10px;
        }
        .student-info {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${assessment.name}</h1>
        <p><strong>Assessment Type:</strong> ${assessment.assessment_type}</p>
        <p><strong>Due Date:</strong> ${new Date(assessment.due_date).toLocaleDateString()}</p>
        <p><strong>Generated:</strong> ${currentDate}</p>
    </div>

    <div class="student-info">
        <h3>Student Information</h3>
        <p><strong>Name:</strong> _________________________________</p>
        <p><strong>Student ID:</strong> _________________________________</p>
        <p><strong>Date Completed:</strong> _________________________________</p>
    </div>

    ${assessment.instructions ? `
    <div class="instructions">
        <h3>Instructions</h3>
        <p>${assessment.instructions}</p>
    </div>
    ` : ''}

    <div class="assessment-info">
        <h3>Assessment Information</h3>
        <p><strong>Description:</strong> ${assessment.short_description}</p>
        <p><strong>Total Questions:</strong> ${questions.length}</p>
        <p><strong>Academic Year:</strong> ${assessment.academic_year}</p>
        <p><strong>Course Type:</strong> ${assessment.course_type}</p>
    </div>

    ${questions.map((question, index) => `
    <div class="question">
        <div class="question-header">
            Question ${index + 1}: ${question.question_text}
        </div>
        <div class="options">
            ${question.options.map((option, optionIndex) => `
            <div class="option">
                <input type="radio" name="question_${question.id}" value="${option.id}" id="q${question.id}_o${option.id}">
                <label for="q${question.id}_o${option.id}" class="option-text">
                    ${String.fromCharCode(65 + optionIndex)}. ${option.text}
                </label>
                <span class="marks">(${option.marks} marks)</span>
            </div>
            `).join('')}
        </div>
    </div>
    `).join('')}

    <div class="no-print" style="margin-top: 40px; text-align: center; color: #666;">
        <p><em>This form can be printed and completed offline. Responses can be uploaded by your teacher.</em></p>
    </div>
</body>
</html>
    `.trim();
  }

  // Upload student response on behalf of student
  async uploadStudentResponse(assessmentId, studentId, responses, uploadedBy, institutionId = null) {
    try {
      // Verify teacher has access to upload for this student
      const teacherAccess = await this.query(`
        SELECT b.id, b.name
        FROM batches b
        INNER JOIN batch_students bs ON b.id = bs.batch_id
        INNER JOIN assessment_batches ab ON b.id = ab.batch_id
        WHERE bs.student_id = ? AND ab.assessment_id = ? AND b.teacher_id = ? AND b.is_active = 1
      `, [studentId, assessmentId, uploadedBy]);

      if (teacherAccess.length === 0) {
        throw new Error('Access denied: Cannot upload response for this student');
      }

      // Verify assessment exists and is published
      const assessment = await this.getAssessmentDetails(assessmentId, institutionId);
      if (!assessment || !assessment.is_published) {
        throw new Error('Assessment not found or not published');
      }

      // Check if student already has a response
      const existingResponse = await this.get(
        'SELECT id FROM assessment_responses WHERE assessment_id = ? AND student_id = ?',
        [assessmentId, studentId]
      );

      if (existingResponse) {
        throw new Error('Student has already submitted a response for this assessment');
      }

      // Calculate total score based on responses
      let totalScore = 0;
      const responseData = {};

      for (const [questionId, selectedOptionId] of Object.entries(responses)) {
        const option = await this.get(
          'SELECT marks FROM question_options WHERE id = ? AND question_id = ?',
          [selectedOptionId, questionId]
        );

        if (option) {
          totalScore += option.marks;
          responseData[questionId] = selectedOptionId;
        }
      }

      // Insert response
      const responseId = uuidv4();
      await this.run(
        `INSERT INTO assessment_responses (id, assessment_id, student_id, responses, total_score, submitted_at)
         VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
        [responseId, assessmentId, studentId, JSON.stringify(responseData), totalScore]
      );

      // Log the upload
      await this.logAuditAction(uploadedBy, 'UPLOAD_STUDENT_RESPONSE', 'assessment_responses', responseId, null, {
        assessmentName: assessment.name,
        studentId: studentId,
        totalScore: totalScore,
        uploadedBy: uploadedBy
      });

      return {
        success: true,
        responseId: responseId,
        totalScore: totalScore,
        submittedAt: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Failed to upload student response: ${error.message}`);
    }
  }

  // Get student statistics for specific assessment
  async getStudentAssessmentStatistics(studentId, assessmentId, teacherId, institutionId = null) {
    try {
      // Verify teacher has access to this student
      const teacherAccess = await this.query(`
        SELECT b.id, b.name
        FROM batches b
        INNER JOIN batch_students bs ON b.id = bs.batch_id
        INNER JOIN assessment_batches ab ON b.id = ab.batch_id
        WHERE bs.student_id = ? AND ab.assessment_id = ? AND b.teacher_id = ? AND b.is_active = 1
      `, [studentId, assessmentId, teacherId]);

      if (teacherAccess.length === 0) {
        throw new Error('Access denied: Cannot view statistics for this student');
      }

      // Get student basic info
      const student = await this.get(
        'SELECT id, first_name, last_name, student_id, email FROM users WHERE id = ? AND is_active = 1',
        [studentId]
      );

      if (!student) {
        throw new Error('Student not found');
      }

      // Get assessment info
      const assessment = await this.getAssessmentDetails(assessmentId, institutionId);
      if (!assessment) {
        throw new Error('Assessment not found');
      }

      // Get student response
      const response = await this.get(`
        SELECT ar.*, a.name as assessment_name
        FROM assessment_responses ar
        INNER JOIN assessments a ON ar.assessment_id = a.id
        WHERE ar.student_id = ? AND ar.assessment_id = ?
      `, [studentId, assessmentId]);

      // Get detailed question responses
      const questionStats = [];
      if (response) {
        const responses = JSON.parse(response.responses);

        for (const [questionId, selectedOptionId] of Object.entries(responses)) {
          const questionDetail = await this.get(`
            SELECT aq.question_text, aq.question_order,
                   qo.option_text, qo.marks,
                   (SELECT MAX(marks) FROM question_options WHERE question_id = aq.id) as max_marks
            FROM assessment_questions aq
            INNER JOIN question_options qo ON qo.id = ?
            WHERE aq.id = ?
          `, [selectedOptionId, questionId]);

          if (questionDetail) {
            questionStats.push({
              questionId: questionId,
              questionText: questionDetail.question_text,
              questionOrder: questionDetail.question_order,
              selectedOption: questionDetail.option_text,
              marksEarned: questionDetail.marks,
              maxMarks: questionDetail.max_marks,
              percentage: (questionDetail.marks / questionDetail.max_marks) * 100
            });
          }
        }

        questionStats.sort((a, b) => a.questionOrder - b.questionOrder);
      }

      // Get batch statistics for comparison
      const batchStats = await this.query(`
        SELECT ar.total_score
        FROM assessment_responses ar
        INNER JOIN batch_students bs ON ar.student_id = bs.student_id
        INNER JOIN batches b ON bs.batch_id = b.id
        WHERE ar.assessment_id = ? AND b.teacher_id = ? AND ar.is_completed = 1
      `, [assessmentId, teacherId]);

      let batchComparison = null;
      if (batchStats.length > 0 && response) {
        const scores = batchStats.map(s => s.total_score);
        const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
        const maxScore = Math.max(...scores);
        const minScore = Math.min(...scores);

        batchComparison = {
          studentScore: response.total_score,
          batchAverage: avgScore,
          batchMax: maxScore,
          batchMin: minScore,
          studentRank: scores.filter(s => s > response.total_score).length + 1,
          totalStudents: scores.length
        };
      }

      return {
        student: student,
        assessment: assessment,
        response: response,
        questionStats: questionStats,
        batchComparison: batchComparison,
        hasSubmitted: !!response
      };
    } catch (error) {
      throw new Error(`Failed to get student statistics: ${error.message}`);
    }
  }

  // Get batch completion status for assessment processing
  async getBatchCompletionStatus(batchId, assessmentId, teacherId) {
    try {
      // Verify teacher has access to this batch and assessment
      const teacherAccess = await this.query(`
        SELECT b.id, b.name, a.name as assessment_name
        FROM batches b
        INNER JOIN assessment_batches ab ON b.id = ab.batch_id
        INNER JOIN assessments a ON ab.assessment_id = a.id
        WHERE b.id = ? AND a.id = ? AND b.teacher_id = ? AND b.is_active = 1 AND a.is_published = 1
      `, [batchId, assessmentId, teacherId]);

      if (teacherAccess.length === 0) {
        throw new Error('Access denied: Cannot view completion status for this batch/assessment');
      }

      // Get all students in the batch with their submission status
      const students = await this.query(`
        SELECT u.id, u.first_name, u.last_name, u.student_id, u.email,
               bs.created_at as enrolled_at,
               CASE
                 WHEN ar.id IS NOT NULL THEN 'completed'
                 ELSE 'not_started'
               END as submission_status,
               ar.submitted_at,
               ar.total_score,
               ar.id as response_id
        FROM users u
        INNER JOIN batch_students bs ON u.id = bs.student_id
        LEFT JOIN assessment_responses ar ON u.id = ar.student_id AND ar.assessment_id = ?
        WHERE bs.batch_id = ? AND u.role = 'student' AND u.is_active = 1
        ORDER BY u.first_name, u.last_name
      `, [assessmentId, batchId]);

      // Calculate completion statistics
      const totalStudents = students.length;
      const completedStudents = students.filter(s => s.submission_status === 'completed').length;
      const pendingStudents = totalStudents - completedStudents;
      const completionPercentage = totalStudents > 0 ? Math.round((completedStudents / totalStudents) * 100) : 0;
      const allCompleted = completedStudents === totalStudents && totalStudents > 0;

      // Check if assessment has already been processed
      const processedStatus = await this.get(`
        SELECT processed_at, processed_by
        FROM assessments
        WHERE id = ? AND processed_at IS NOT NULL
      `, [assessmentId]);

      return {
        batchInfo: teacherAccess[0],
        students: students,
        statistics: {
          totalStudents,
          completedStudents,
          pendingStudents,
          completionPercentage,
          allCompleted
        },
        processedStatus: processedStatus,
        canProcess: allCompleted && !processedStatus
      };
    } catch (error) {
      throw new Error(`Failed to get batch completion status: ${error.message}`);
    }
  }

  // Process assessment for peer review allocation
  async processAssessmentForPeerReview(assessmentId, batchId, processedBy, institutionId = null) {
    try {
      // Verify teacher has access and all students have completed
      const completionStatus = await this.getBatchCompletionStatus(batchId, assessmentId, processedBy, institutionId);

      if (!completionStatus.canProcess) {
        if (completionStatus.processedStatus) {
          throw new Error('Assessment has already been processed for peer review');
        }
        if (!completionStatus.statistics.allCompleted) {
          throw new Error(`Cannot process assessment: ${completionStatus.statistics.pendingStudents} students have not completed their responses`);
        }
        throw new Error('Assessment cannot be processed at this time');
      }

      // Begin transaction for processing
      await this.run('BEGIN TRANSACTION');

      try {
        // Mark assessment as processed
        await this.run(`
          UPDATE assessments
          SET processed_at = CURRENT_TIMESTAMP, processed_by = ?
          WHERE id = ?
        `, [processedBy, assessmentId]);

        // Log the processing action
        await this.logAuditAction(processedBy, 'PROCESS_ASSESSMENT', 'assessments', assessmentId, null, {
          assessmentName: completionStatus.batchInfo.assessment_name,
          batchName: completionStatus.batchInfo.name,
          totalStudents: completionStatus.statistics.totalStudents,
          completedStudents: completionStatus.statistics.completedStudents
        });

        // Generate comprehensive statistics and PDF reports
        const statisticsResult = await this.generateAssessmentStatistics(assessmentId, batchId, processedBy);

        // Generate individual PDF reports for all students
        await this.generateStudentPDFReports(assessmentId, batchId, processedBy, statisticsResult);

        await this.run('COMMIT');

        return {
          success: true,
          processedAt: new Date().toISOString(),
          message: `Assessment "${completionStatus.batchInfo.assessment_name}" has been processed successfully. ${completionStatus.statistics.totalStudents} students are ready for peer review allocation.`,
          statistics: completionStatus.statistics
        };
      } catch (error) {
        await this.run('ROLLBACK');
        throw error;
      }
    } catch (error) {
      throw new Error(`Failed to process assessment: ${error.message}`);
    }
  }

  // Check if assessment can be processed (quick check for UI updates)
  async canProcessAssessment(assessmentId, batchId, teacherId) {
    try {
      const completionStatus = await this.getBatchCompletionStatus(batchId, assessmentId, teacherId);
      return {
        canProcess: completionStatus.canProcess,
        allCompleted: completionStatus.statistics.allCompleted,
        completionPercentage: completionStatus.statistics.completionPercentage,
        completedStudents: completionStatus.statistics.completedStudents,
        totalStudents: completionStatus.statistics.totalStudents,
        isProcessed: !!completionStatus.processedStatus
      };
    } catch (error) {
      throw new Error(`Failed to check processing status: ${error.message}`);
    }
  }

  // Get real-time submission status for a batch
  async getBatchSubmissionStatus(batchId, assessmentId, teacherId) {
    try {
      // Verify teacher access
      const teacherAccess = await this.get(`
        SELECT b.id
        FROM batches b
        INNER JOIN assessment_batches ab ON b.id = ab.batch_id
        WHERE b.id = ? AND ab.assessment_id = ? AND b.teacher_id = ? AND b.is_active = 1
      `, [batchId, assessmentId, teacherId]);

      if (!teacherAccess) {
        throw new Error('Access denied: Cannot view submission status for this batch');
      }

      // Get quick submission counts
      const statusCounts = await this.get(`
        SELECT
          COUNT(u.id) as total_students,
          COUNT(ar.id) as completed_students,
          (COUNT(u.id) - COUNT(ar.id)) as pending_students
        FROM users u
        INNER JOIN batch_students bs ON u.id = bs.student_id
        LEFT JOIN assessment_responses ar ON u.id = ar.student_id AND ar.assessment_id = ?
        WHERE bs.batch_id = ? AND u.role = 'student' AND u.is_active = 1
      `, [assessmentId, batchId]);

      const completionPercentage = statusCounts.total_students > 0
        ? Math.round((statusCounts.completed_students / statusCounts.total_students) * 100)
        : 0;

      const allCompleted = statusCounts.completed_students === statusCounts.total_students && statusCounts.total_students > 0;

      return {
        totalStudents: statusCounts.total_students,
        completedStudents: statusCounts.completed_students,
        pendingStudents: statusCounts.pending_students,
        completionPercentage,
        allCompleted,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Failed to get submission status: ${error.message}`);
    }
  }

  // Generate comprehensive assessment statistics
  async generateAssessmentStatistics(assessmentId, batchId, processedBy) {
    try {
      // Get assessment details
      const assessment = await this.get(`
        SELECT a.*, b.name as batch_name
        FROM assessments a
        INNER JOIN assessment_batches ab ON a.id = ab.assessment_id
        INNER JOIN batches b ON ab.batch_id = b.id
        WHERE a.id = ? AND b.id = ?
      `, [assessmentId, batchId]);

      if (!assessment) {
        throw new Error('Assessment not found');
      }

      // Get all questions for the assessment
      const questions = await this.query(`
        SELECT aq.*, qo.id as option_id, qo.option_text, qo.marks, qo.option_order
        FROM assessment_questions aq
        LEFT JOIN question_options qo ON aq.id = qo.question_id
        WHERE aq.assessment_id = ? AND aq.is_active = 1
        ORDER BY aq.question_order, qo.option_order
      `, [assessmentId]);

      // Group questions with their options
      const questionsMap = {};
      questions.forEach(q => {
        if (!questionsMap[q.id]) {
          questionsMap[q.id] = {
            id: q.id,
            question_text: q.question_text,
            question_order: q.question_order,
            options: []
          };
        }
        if (q.option_id) {
          questionsMap[q.id].options.push({
            id: q.option_id,
            option_text: q.option_text,
            marks: q.marks,
            option_order: q.option_order
          });
        }
      });

      const questionsList = Object.values(questionsMap).sort((a, b) => a.question_order - b.question_order);

      // Get all student responses
      const responses = await this.query(`
        SELECT ar.*, u.first_name, u.last_name, u.student_id
        FROM assessment_responses ar
        INNER JOIN users u ON ar.student_id = u.id
        INNER JOIN batch_students bs ON u.id = bs.student_id
        WHERE ar.assessment_id = ? AND bs.batch_id = ?
        ORDER BY u.first_name, u.last_name
      `, [assessmentId, batchId]);

      // Parse and analyze responses
      const studentResponses = responses.map(r => ({
        ...r,
        responses: JSON.parse(r.responses)
      }));

      // Calculate question-wise statistics
      const questionStats = questionsList.map(question => {
        const optionCounts = {};
        const optionPercentages = {};
        let totalResponses = 0;

        // Initialize option counts
        question.options.forEach(option => {
          optionCounts[option.id] = 0;
        });

        // Count responses for each option
        studentResponses.forEach(student => {
          const studentAnswer = student.responses[question.id];
          if (studentAnswer && optionCounts.hasOwnProperty(studentAnswer)) {
            optionCounts[studentAnswer]++;
            totalResponses++;
          }
        });

        // Calculate percentages
        question.options.forEach(option => {
          optionPercentages[option.id] = totalResponses > 0
            ? Math.round((optionCounts[option.id] / totalResponses) * 100)
            : 0;
        });

        // Find most selected option (recommended answer)
        let mostSelectedOption = null;
        let maxCount = 0;
        question.options.forEach(option => {
          if (optionCounts[option.id] > maxCount) {
            maxCount = optionCounts[option.id];
            mostSelectedOption = option;
          }
        });

        return {
          questionId: question.id,
          questionText: question.question_text,
          questionOrder: question.question_order,
          options: question.options.map(option => ({
            ...option,
            count: optionCounts[option.id],
            percentage: optionPercentages[option.id]
          })),
          mostSelectedOption,
          totalResponses,
          responseRate: studentResponses.length > 0
            ? Math.round((Number(totalResponses) / Number(studentResponses.length)) * 100)
            : 0
        };
      });

      // Calculate batch statistics
      const scores = studentResponses.map(s => s.total_score);
      const batchStats = {
        totalStudents: studentResponses.length,
        averageScore: scores.length > 0 ? Math.round((scores.reduce((a, b) => Number(a) + Number(b), 0) / scores.length) * 100) / 100 : 0,
        highestScore: scores.length > 0 ? Math.max(...scores) : 0,
        lowestScore: scores.length > 0 ? Math.min(...scores) : 0,
        scoreDistribution: this.calculateScoreDistribution(scores),
        passRate: this.calculatePassRate(scores, 60) // Assuming 60% is passing
      };

      // Calculate student rankings
      const studentRankings = studentResponses
        .sort((a, b) => b.total_score - a.total_score)
        .map((student, index) => ({
          ...student,
          rank: index + 1,
          percentile: Math.round(((Number(studentResponses.length) - Number(index)) / Number(studentResponses.length)) * 100)
        }));

      const statisticsData = {
        assessmentInfo: {
          id: assessment.id,
          name: assessment.name,
          batchName: assessment.batch_name,
          processedAt: new Date().toISOString(),
          processedBy
        },
        questionStats,
        batchStats,
        studentRankings,
        totalQuestions: questionsList.length,
        generatedAt: new Date().toISOString()
      };

      // Store statistics in database
      await this.run(`
        INSERT OR REPLACE INTO assessment_statistics
        (assessment_id, batch_id, statistics_data, generated_by, generated_at)
        VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
      `, [assessmentId, batchId, JSON.stringify(statisticsData), processedBy]);

      return statisticsData;
    } catch (error) {
      throw new Error(`Failed to generate assessment statistics: ${error.message}`);
    }
  }

  // Helper method to calculate score distribution
  calculateScoreDistribution(scores) {
    const ranges = [
      { label: '90-100%', min: 90, max: 100, count: 0 },
      { label: '80-89%', min: 80, max: 89, count: 0 },
      { label: '70-79%', min: 70, max: 79, count: 0 },
      { label: '60-69%', min: 60, max: 69, count: 0 },
      { label: '50-59%', min: 50, max: 59, count: 0 },
      { label: 'Below 50%', min: 0, max: 49, count: 0 }
    ];

    scores.forEach(score => {
      const numericScore = Number(score);
      if (!isNaN(numericScore)) {
        const range = ranges.find(r => numericScore >= r.min && numericScore <= r.max);
        if (range) {range.count++;}
      }
    });

    return ranges;
  }

  // Helper method to calculate pass rate
  calculatePassRate(scores, passingScore = 60) {
    if (scores.length === 0) {return 0;}
    const numericPassingScore = Number(passingScore);
    const passedCount = scores.filter(score => {
      const numericScore = Number(score);
      return !isNaN(numericScore) && numericScore >= numericPassingScore;
    }).length;
    return Math.round((Number(passedCount) / Number(scores.length)) * 100);
  }

  // Generate individual PDF reports for all students
  async generateStudentPDFReports(assessmentId, batchId, processedBy, statisticsData) {
    try {
      const reportsDir = path.join(__dirname, '..', 'reports', 'assessments');

      // Ensure reports directory exists
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
      }

      const generatedReports = [];

      // Generate PDF for each student
      for (const student of statisticsData.studentRankings) {
        try {
          const pdfPath = await this.generateIndividualStudentPDF(
            student,
            statisticsData,
            reportsDir
          );

          generatedReports.push({
            studentId: student.student_id,
            studentName: `${student.first_name} ${student.last_name}`,
            pdfPath,
            generatedAt: new Date().toISOString()
          });
        } catch (error) {
          console.error(`Failed to generate PDF for student ${student.student_id}:`, error);
        }
      }

      // Store PDF generation record
      await this.run(`
        INSERT INTO assessment_pdf_reports
        (assessment_id, batch_id, reports_data, generated_by, generated_at)
        VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
      `, [assessmentId, batchId, JSON.stringify(generatedReports), processedBy]);

      return {
        totalReports: generatedReports.length,
        reportsDirectory: reportsDir,
        reports: generatedReports
      };
    } catch (error) {
      throw new Error(`Failed to generate student PDF reports: ${error.message}`);
    }
  }

  // Generate individual student PDF report
  async generateIndividualStudentPDF(student, statisticsData, reportsDir) {
    return new Promise((resolve, reject) => {
      try {
        const fileName = `${student.student_id}_${statisticsData.assessmentInfo.name.replace(/[^a-zA-Z0-9]/g, '_')}_Report.pdf`;
        const filePath = path.join(reportsDir, fileName);

        const doc = new PDFDocument({ margin: 50 });
        doc.pipe(fs.createWriteStream(filePath));

        // Header
        doc.fontSize(20).font('Helvetica-Bold').text('Assessment Report', { align: 'center' });
        doc.moveDown();

        // Assessment Information
        doc.fontSize(14).font('Helvetica-Bold').text('Assessment Information');
        doc.fontSize(12).font('Helvetica')
          .text(`Assessment: ${statisticsData.assessmentInfo.name}`)
          .text(`Batch: ${statisticsData.assessmentInfo.batchName}`)
          .text(`Date Processed: ${new Date(statisticsData.assessmentInfo.processedAt).toLocaleDateString()}`)
          .moveDown();

        // Student Information
        doc.fontSize(14).font('Helvetica-Bold').text('Student Information');
        doc.fontSize(12).font('Helvetica')
          .text(`Name: ${student.first_name} ${student.last_name}`)
          .text(`Student ID: ${student.student_id}`)
          .text(`Rank: ${student.rank} of ${statisticsData.batchStats.totalStudents}`)
          .text(`Percentile: ${student.percentile}%`)
          .moveDown();

        // Performance Summary
        doc.fontSize(14).font('Helvetica-Bold').text('Performance Summary');
        doc.fontSize(12).font('Helvetica')
          .text(`Total Score: ${student.total_score}%`)
          .text(`Batch Average: ${statisticsData.batchStats.averageScore}%`)
          .text(`Highest Score: ${statisticsData.batchStats.highestScore}%`)
          .text(`Lowest Score: ${statisticsData.batchStats.lowestScore}%`)
          .moveDown();

        // Question-wise Analysis
        doc.fontSize(14).font('Helvetica-Bold').text('Question-wise Analysis');
        doc.moveDown(0.5);

        const studentResponses = JSON.parse(student.responses);

        statisticsData.questionStats.forEach((questionStat) => {
          if (doc.y > 700) { // Start new page if needed
            doc.addPage();
          }

          const studentAnswer = studentResponses[questionStat.questionId];
          const studentOption = questionStat.options.find(opt => opt.id === studentAnswer);
          const recommendedOption = questionStat.mostSelectedOption;

          doc.fontSize(12).font('Helvetica-Bold')
            .text(`Question ${questionStat.questionOrder}: ${questionStat.questionText}`);

          doc.fontSize(10).font('Helvetica')
            .text(`Your Answer: ${studentOption ? studentOption.option_text : 'Not answered'}`)
            .text(`Recommended Answer: ${recommendedOption ? recommendedOption.option_text : 'N/A'}`)
            .text(`Your Marks: ${studentOption ? studentOption.marks : 0}`)
            .text(`Recommended Marks: ${recommendedOption ? recommendedOption.marks : 0}`)
            .moveDown(0.5);
        });

        // Batch Comparison
        if (doc.y > 600) {
          doc.addPage();
        }

        doc.fontSize(14).font('Helvetica-Bold').text('Batch Comparison');
        doc.fontSize(12).font('Helvetica')
          .text(`Your Performance: ${student.total_score >= statisticsData.batchStats.averageScore ? 'Above' : 'Below'} Average`)
          .text(`Pass Rate: ${statisticsData.batchStats.passRate}%`)
          .moveDown();

        // Score Distribution
        doc.fontSize(12).font('Helvetica-Bold').text('Score Distribution:');
        statisticsData.batchStats.scoreDistribution.forEach(range => {
          doc.fontSize(10).font('Helvetica')
            .text(`${range.label}: ${range.count} students`);
        });

        doc.end();

        doc.on('end', () => {
          resolve(filePath);
        });

        doc.on('error', (error) => {
          reject(error);
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  // Get assessment statistics
  async getAssessmentStatistics(assessmentId, batchId, teacherId) {
    try {
      // Verify teacher access
      const teacherAccess = await this.get(`
        SELECT b.id
        FROM batches b
        INNER JOIN assessment_batches ab ON b.id = ab.batch_id
        WHERE b.id = ? AND ab.assessment_id = ? AND b.teacher_id = ?
      `, [batchId, assessmentId, teacherId]);

      if (!teacherAccess) {
        throw new Error('Access denied: Cannot view statistics for this assessment');
      }

      // Get stored statistics
      const statisticsRecord = await this.get(`
        SELECT statistics_data, generated_at
        FROM assessment_statistics
        WHERE assessment_id = ? AND batch_id = ?
        ORDER BY generated_at DESC
        LIMIT 1
      `, [assessmentId, batchId]);

      if (!statisticsRecord) {
        throw new Error('Statistics not found. Please process the assessment first.');
      }

      return {
        ...JSON.parse(statisticsRecord.statistics_data),
        lastGenerated: statisticsRecord.generated_at
      };
    } catch (error) {
      throw new Error(`Failed to get assessment statistics: ${error.message}`);
    }
  }

  // Settings management methods
  async getUserSettings(userId) {
    try {
      const settings = await this.query(
        'SELECT setting_key, setting_value FROM user_settings WHERE user_id = ?',
        [userId]
      );

      const settingsObj = {};
      settings.forEach(setting => {
        settingsObj[setting.setting_key] = setting.setting_value;
      });

      return settingsObj;
    } catch (error) {
      throw new Error(`Failed to get user settings: ${error.message}`);
    }
  }

  async updateUserSetting(userId, settingKey, settingValue) {
    try {
      await this.run(
        `INSERT OR REPLACE INTO user_settings (user_id, setting_key, setting_value, updated_at)
         VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
        [userId, settingKey, settingValue]
      );

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to update user setting: ${error.message}`);
    }
  }

  async getApplicationSettings() {
    try {
      const settings = await this.query(
        'SELECT setting_key, setting_value, description FROM application_settings'
      );

      const settingsObj = {};
      settings.forEach(setting => {
        settingsObj[setting.setting_key] = {
          value: setting.setting_value,
          description: setting.description
        };
      });

      return settingsObj;
    } catch (error) {
      throw new Error(`Failed to get application settings: ${error.message}`);
    }
  }

  async updateApplicationSetting(settingKey, settingValue, description = null) {
    try {
      await this.run(
        `INSERT OR REPLACE INTO application_settings (setting_key, setting_value, description, updated_at)
         VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
        [settingKey, settingValue, description]
      );

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to update application setting: ${error.message}`);
    }
  }

  // Institution management methods
  async getInstitutionDetails(institutionId) {
    try {
      const institution = await this.get(
        'SELECT * FROM institutions WHERE id = ?',
        [institutionId]
      );

      if (!institution) {
        throw new Error('Institution not found');
      }

      const details = await this.get(
        'SELECT * FROM institution_details WHERE institution_id = ?',
        [institutionId]
      );

      return {
        ...institution,
        details: details || {}
      };
    } catch (error) {
      throw new Error(`Failed to get institution details: ${error.message}`);
    }
  }

  async updateInstitutionDetails(institutionId, institutionData, detailsData, updatedBy) {
    try {
      // Update basic institution info
      if (institutionData.name) {
        await this.run(
          'UPDATE institutions SET name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [institutionData.name, institutionId]
        );
      }

      // Update or insert detailed information
      const existingDetails = await this.get(
        'SELECT id FROM institution_details WHERE institution_id = ?',
        [institutionId]
      );

      if (existingDetails) {
        // Update existing details
        await this.run(
          `UPDATE institution_details SET
           logo_path = COALESCE(?, logo_path),
           description = COALESCE(?, description),
           primary_email = COALESCE(?, primary_email),
           contact_number = COALESCE(?, contact_number),
           address_line1 = COALESCE(?, address_line1),
           address_line2 = COALESCE(?, address_line2),
           city = COALESCE(?, city),
           state = COALESCE(?, state),
           postal_code = COALESCE(?, postal_code),
           country = COALESCE(?, country),
           establishment_date = COALESCE(?, establishment_date),
           accreditation_details = COALESCE(?, accreditation_details),
           website_url = COALESCE(?, website_url),
           updated_by = ?,
           updated_at = CURRENT_TIMESTAMP
           WHERE institution_id = ?`,
          [
            detailsData.logoPath,
            detailsData.description,
            detailsData.primaryEmail,
            detailsData.contactNumber,
            detailsData.addressLine1,
            detailsData.addressLine2,
            detailsData.city,
            detailsData.state,
            detailsData.postalCode,
            detailsData.country,
            detailsData.establishmentDate,
            detailsData.accreditationDetails,
            detailsData.websiteUrl,
            updatedBy,
            institutionId
          ]
        );
      } else {
        // Insert new details
        await this.run(
          `INSERT INTO institution_details (
            institution_id, logo_path, description, primary_email, contact_number,
            address_line1, address_line2, city, state, postal_code, country,
            establishment_date, accreditation_details, website_url, updated_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            institutionId,
            detailsData.logoPath,
            detailsData.description,
            detailsData.primaryEmail,
            detailsData.contactNumber,
            detailsData.addressLine1,
            detailsData.addressLine2,
            detailsData.city,
            detailsData.state,
            detailsData.postalCode,
            detailsData.country,
            detailsData.establishmentDate,
            detailsData.accreditationDetails,
            detailsData.websiteUrl,
            updatedBy
          ]
        );
      }

      // Log the update
      await this.logAuditAction(updatedBy, 'UPDATE_INSTITUTION', 'institutions', institutionId, null, {
        institutionData,
        detailsData
      });

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to update institution details: ${error.message}`);
    }
  }

  // Dashboard statistics methods
  async getDashboardStatistics(userId, userRole, institutionId) {
    try {
      const stats = {};

      // Get assessment statistics
      if (userRole === 'teacher') {
        const assessmentStats = await this.get(`
          SELECT
            COUNT(*) as total_assessments,
            COUNT(CASE WHEN is_published = 1 THEN 1 END) as published_assessments,
            COUNT(CASE WHEN processed_at IS NOT NULL THEN 1 END) as processed_assessments
          FROM assessments
          WHERE created_by = ?
        `, [userId]);

        stats.assessments = assessmentStats;

        // Get batch statistics
        const batchStats = await this.get(`
          SELECT
            COUNT(*) as total_batches,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_batches
          FROM batches
          WHERE teacher_id = ?
        `, [userId]);

        stats.batches = batchStats;

        // Get student statistics
        const studentStats = await this.get(`
          SELECT COUNT(DISTINCT bs.student_id) as total_students
          FROM batches b
          INNER JOIN batch_students bs ON b.id = bs.batch_id
          WHERE b.teacher_id = ?
        `, [userId]);

        stats.students = studentStats;

      } else if (userRole === 'admin' || userRole === 'super_admin') {
        // Institution-wide statistics
        const userStats = await this.get(`
          SELECT
            COUNT(*) as total_users,
            COUNT(CASE WHEN role = 'teacher' THEN 1 END) as total_teachers,
            COUNT(CASE WHEN role = 'student' THEN 1 END) as total_students,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users
          FROM users
          WHERE institution_id = ?
        `, [institutionId]);

        stats.users = userStats;

        const assessmentStats = await this.get(`
          SELECT
            COUNT(*) as total_assessments,
            COUNT(CASE WHEN is_published = 1 THEN 1 END) as published_assessments,
            COUNT(CASE WHEN processed_at IS NOT NULL THEN 1 END) as processed_assessments
          FROM assessments a
          INNER JOIN users u ON a.created_by = u.id
          WHERE u.institution_id = ?
        `, [institutionId]);

        stats.assessments = assessmentStats;
      }

      // Get recent activity
      const recentActivity = await this.query(`
        SELECT action, table_name, timestamp, new_values
        FROM audit_log
        WHERE user_id = ?
        ORDER BY timestamp DESC
        LIMIT 10
      `, [userId]);

      stats.recentActivity = recentActivity;

      return stats;
    } catch (error) {
      throw new Error(`Failed to get dashboard statistics: ${error.message}`);
    }
  }

  // Peer review methods
  async submitReview(reviewData) {
    try {
      const { assessmentId, reviewerId, revieweeId, scores, comments, totalScore } = reviewData;
      const reviewId = uuidv4();

      await this.run(
        `INSERT INTO peer_reviews (id, assessment_id, reviewer_id, reviewee_id, scores, comments, total_score)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [reviewId, assessmentId, reviewerId, revieweeId, JSON.stringify(scores), comments, totalScore]
      );

      return { id: reviewId, assessmentId, reviewerId, revieweeId, totalScore };
    } catch (error) {
      throw new Error(`Failed to submit review: ${error.message}`);
    }
  }

  async getReviewStats(filters = {}) {
    try {
      let query = `
        SELECT
          pr.*,
          reviewer.first_name || ' ' || reviewer.last_name as reviewer_name,
          reviewee.first_name || ' ' || reviewee.last_name as reviewee_name,
          a.title as assessment_title
        FROM peer_reviews pr
        LEFT JOIN users reviewer ON pr.reviewer_id = reviewer.id
        LEFT JOIN users reviewee ON pr.reviewee_id = reviewee.id
        LEFT JOIN assessments a ON pr.assessment_id = a.id
        WHERE 1=1
      `;
      const params = [];

      if (filters.assessmentId) {
        query += ' AND pr.assessment_id = ?';
        params.push(filters.assessmentId);
      }

      if (filters.batchId) {
        query += ' AND a.batch_id = ?';
        params.push(filters.batchId);
      }

      query += ' ORDER BY pr.submitted_at DESC';

      const reviews = await this.query(query, params);
      return reviews.map(review => ({
        ...review,
        scores: JSON.parse(review.scores)
      }));
    } catch (error) {
      throw new Error(`Failed to get review stats: ${error.message}`);
    }
  }

  // Settings methods
  async getSetting(key) {
    try {
      const setting = await this.get('SELECT value FROM settings WHERE key = ?', [key]);
      return setting ? setting.value : null;
    } catch (error) {
      throw new Error(`Failed to get setting: ${error.message}`);
    }
  }

  async setSetting(key, value) {
    try {
      await this.run(
        'INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
        [key, value]
      );
      return { key, value };
    } catch (error) {
      throw new Error(`Failed to set setting: ${error.message}`);
    }
  }

  // Institution methods
  async getInstitution() {
    try {
      const institution = await this.get('SELECT * FROM institution LIMIT 1');
      return institution;
    } catch (error) {
      throw new Error(`Failed to get institution: ${error.message}`);
    }
  }

  async createInstitution(institutionData) {
    try {
      const { name, address, phone, email } = institutionData;

      // Validate required fields
      if (!name || !address || !phone || !email) {
        throw new Error('Missing required institution fields');
      }

      // Additional validation to provide better error messages
      if (!this.validateEmail(email)) {
        throw new Error('Invalid email format. Please enter a valid email address (e.g., <EMAIL>)');
      }

      if (!this.validatePhone(phone)) {
        throw new Error('Invalid phone number format. Please enter a valid phone number');
      }

      if (name.length < 2 || name.length > 100) {
        throw new Error('Institution name must be between 2 and 100 characters');
      }

      if (address.length < 5 || address.length > 500) {
        throw new Error('Institution address must be between 5 and 500 characters');
      }

      const institutionId = uuidv4();

      await this.run(
        `INSERT INTO institution (id, name, address, phone, email, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [institutionId, name, address, phone, email]
      );

      return {
        success: true,
        data: {
          id: institutionId,
          name,
          address,
          phone,
          email
        }
      };
    } catch (error) {
      // Provide more specific error messages for database constraint failures
      // Ensure error.message exists and provide fallback
      const errorMsg = error.message || error.toString() || 'Unknown database error occurred';
      let errorMessage = errorMsg;

      if (errorMsg.includes('CHECK constraint failed') && errorMsg.includes('name')) {
        errorMessage = 'Institution name must be between 2 and 100 characters and cannot contain numbers';
      } else if (errorMsg.includes('CHECK constraint failed') && errorMsg.includes('address')) {
        errorMessage = 'Institution address must be between 5 and 500 characters';
      } else if (errorMsg.includes('UNIQUE constraint failed')) {
        errorMessage = 'An institution with this email already exists';
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async updateInstitution(institutionData, updatedBy = null) {
    try {
      const {
        name,
        description,
        email,
        phone,
        address,
        websiteUrl,
        establishedYear,
        instituteType,
        logoPath
      } = institutionData;

      // Validate required fields
      if (!name || !email || !phone || !address) {
        throw new Error('Name, email, phone, and address are required fields');
      }

      // Validate email format
      if (!this.validateEmail(email)) {
        throw new Error('Invalid email format');
      }

      // Validate phone format
      if (!this.validatePhone(phone)) {
        throw new Error('Invalid phone format');
      }

      // Validate website URL if provided
      if (websiteUrl && !this.validateURL(websiteUrl)) {
        throw new Error('Invalid website URL format');
      }

      // Validate established year if provided
      if (establishedYear && !this.validateYear(establishedYear)) {
        throw new Error('Invalid established year');
      }

      const existing = await this.getInstitution();

      if (existing) {
        // Log the update for audit trail
        if (updatedBy) {
          await this.logAuditAction(updatedBy, 'UPDATE_INSTITUTION', 'institution', existing.id,
            {
              name: existing.name,
              email: existing.email,
              phone: existing.phone,
              address: existing.address
            },
            { name, email, phone, address, description, websiteUrl, establishedYear, instituteType }
          );
        }

        await this.run(
          `UPDATE institution SET
           name = ?, description = ?, email = ?, phone = ?, address = ?,
           website_url = ?, established_year = ?, institute_type = ?, logo_path = ?,
           updated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [name, description, email, phone, address, websiteUrl, establishedYear, instituteType, logoPath, existing.id]
        );
      } else {
        const institutionId = uuidv4();

        // Log the creation for audit trail
        if (updatedBy) {
          await this.logAuditAction(updatedBy, 'CREATE_INSTITUTION', 'institution', institutionId, null,
            { name, email, phone, address, description, websiteUrl, establishedYear, instituteType }
          );
        }

        await this.run(
          `INSERT INTO institution
           (id, name, description, email, phone, address, website_url, established_year, institute_type, logo_path)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [institutionId, name, description, email, phone, address, websiteUrl, establishedYear, instituteType, logoPath]
        );
      }

      return {
        name,
        description,
        email,
        phone,
        address,
        websiteUrl,
        establishedYear,
        instituteType,
        logoPath
      };
    } catch (error) {
      throw new Error(`Failed to update institution: ${error.message}`);
    }
  }

  // PDF generation method
  async generatePDFReport(reportData) {
    try {
      const { studentId, assessmentId, filePath } = reportData;

      // Get student data
      const student = await this.get('SELECT * FROM users WHERE id = ?', [studentId]);

      // Get assessment data
      const assessment = await this.get('SELECT * FROM assessments WHERE id = ?', [assessmentId]);

      // Get reviews for this student
      const reviews = await this.query(
        'SELECT * FROM peer_reviews WHERE assessment_id = ? AND reviewee_id = ?',
        [assessmentId, studentId]
      );

      // Create PDF
      const doc = new PDFDocument();
      doc.pipe(fs.createWriteStream(filePath));

      // Add content to PDF
      doc.fontSize(20).text('Peer Review Report', 100, 100);
      doc.fontSize(14).text(`Student: ${student.first_name} ${student.last_name}`, 100, 140);
      doc.text(`Assessment: ${assessment.title}`, 100, 160);
      doc.text(`Date: ${new Date().toLocaleDateString()}`, 100, 180);

      let yPosition = 220;
      reviews.forEach((review, index) => {
        doc.text(`Review ${index + 1}:`, 100, yPosition);
        doc.text(`Score: ${review.total_score}`, 100, yPosition + 20);
        doc.text(`Comments: ${review.comments || 'No comments'}`, 100, yPosition + 40);
        yPosition += 80;
      });

      doc.end();

      return { success: true, filePath };
    } catch (error) {
      throw new Error(`Failed to generate PDF report: ${error.message}`);
    }
  }

  // Enhanced validation and utility methods
  validatePassword(password) {
    if (!password || typeof password !== 'string') {
      return false;
    }

    // Enhanced password requirements:
    // - At least 8 characters
    // - At least one uppercase letter
    // - At least one lowercase letter
    // - At least one number
    // - At least one special character
    const hasMinLength = password.length >= 8;
    const hasMaxLength = password.length <= 128;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

    return hasMinLength && hasMaxLength && hasUppercase && hasLowercase && hasNumber && hasSpecialChar;
  }

  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  validatePhone(phone) {
    if (!phone) {return false;} // Phone is required for institution
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  validateURL(url) {
    if (!url) {return true;} // URL is optional
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  validateYear(year) {
    if (!year) {return true;} // Year is optional

    // Handle edge cases for input validation
    const yearStr = String(year).trim();
    if (yearStr === '' || yearStr === 'null' || yearStr === 'undefined') {
      return true; // Treat as empty input
    }

    const currentYear = new Date().getFullYear();
    const yearNum = parseInt(yearStr, 10);

    if (isNaN(yearNum) || !Number.isInteger(yearNum)) {
      return false;
    }

    return yearNum >= 1800 && yearNum <= currentYear;
  }

  // Comprehensive user data validation method (offline-only, no email/phone)
  async validateUserData({ username, firstName, lastName, role, teacherId, studentId, institutionId }, skipInstitutionValidation = false) {
    const errors = [];

    // Username validation
    if (username) {
      if (username.length < 3 || username.length > 30) {
        errors.push('Username must be between 3 and 30 characters');
      }
      if (!/^[A-Za-z]/.test(username)) {
        errors.push('Username must start with a letter');
      }
      if (!/^[A-Za-z0-9_-]+$/.test(username)) {
        errors.push('Username can only contain letters, numbers, hyphens, and underscores');
      }
    }

    // First name validation
    if (firstName) {
      if (firstName.trim().length === 0 || firstName.length > 50) {
        errors.push('First name must be between 1 and 50 characters');
      }
      if (/\d/.test(firstName)) {
        errors.push('First name cannot contain numbers');
      }
    }

    // Last name validation
    if (lastName) {
      if (lastName.trim().length === 0 || lastName.length > 50) {
        errors.push('Last name must be between 1 and 50 characters');
      }
      if (/\d/.test(lastName)) {
        errors.push('Last name cannot contain numbers');
      }
    }

    // Role validation
    if (role) {
      const validRoles = ['super_admin', 'admin', 'teacher', 'student'];
      if (!validRoles.includes(role)) {
        errors.push(`Invalid role. Must be one of: ${validRoles.join(', ')}`);
      }
    }

    // Teacher ID validation
    if (teacherId) {
      if (teacherId.length < 3 || teacherId.length > 20) {
        errors.push('Teacher ID must be between 3 and 20 characters');
      }
    }

    // Student ID validation
    if (studentId) {
      if (studentId.length < 3 || studentId.length > 20) {
        errors.push('Student ID must be between 3 and 20 characters');
      }
    }

    // Institution ID validation (skip for dynamic institution creation)
    if (institutionId && !skipInstitutionValidation) {
      // Check if institution exists
      try {
        const institution = await this.get('SELECT id FROM institution WHERE id = ?', [institutionId]);
        if (!institution) {
          errors.push('Institution does not exist');
        }
      } catch (error) {
        errors.push('Failed to validate institution');
      }
    }

    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join('; ')}`);
    }
  }

  async checkUserDuplicates(username, excludeId = null) {
    const usernameCheck = await this.get(
      'SELECT id FROM users WHERE username = ?' + (excludeId ? ' AND id != ?' : ''),
      excludeId ? [username, excludeId] : [username]
    );

    if (usernameCheck) {
      throw new Error('Username already exists');
    }
  }

  async checkTeacherIdDuplicate(teacherId, excludeId = null) {
    const teacherIdCheck = await this.get(
      'SELECT id FROM users WHERE teacher_id = ?' + (excludeId ? ' AND id != ?' : ''),
      excludeId ? [teacherId, excludeId] : [teacherId]
    );

    if (teacherIdCheck) {
      throw new Error('Teacher ID already exists');
    }
  }

  async logAuditAction(userId, action, tableName, recordId, oldValues, newValues) {
    try {
      const auditId = uuidv4();
      await this.run(
        `INSERT INTO audit_log (id, user_id, action, table_name, record_id, old_values, new_values)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          auditId,
          userId,
          action,
          tableName,
          recordId,
          oldValues ? JSON.stringify(oldValues) : null,
          newValues ? JSON.stringify(newValues) : null
        ]
      );
    } catch (error) {
      // Don't throw error here to avoid breaking the main operation
      console.error('Failed to log audit action:', error.message);
    }
  }

  // Password history management methods
  async addPasswordToHistory(userId, passwordHash) {
    try {
      const historyId = uuidv4();
      await this.run(
        'INSERT INTO password_history (id, user_id, password_hash) VALUES (?, ?, ?)',
        [historyId, userId, passwordHash]
      );

      // Keep only the last 5 passwords
      const oldPasswords = await this.query(
        'SELECT id FROM password_history WHERE user_id = ? ORDER BY created_at DESC LIMIT -1 OFFSET 5',
        [userId]
      );

      if (oldPasswords.length > 0) {
        const idsToDelete = oldPasswords.map(p => p.id);
        await this.run(
          `DELETE FROM password_history WHERE id IN (${idsToDelete.map(() => '?').join(',')})`,
          idsToDelete
        );
      }
    } catch (error) {
      console.error('Failed to add password to history:', error);
      // Don't throw error to avoid breaking password change operation
    }
  }

  async checkPasswordHistory(userId, newPassword) {
    try {
      const passwordHistory = await this.query(
        'SELECT password_hash FROM password_history WHERE user_id = ? ORDER BY created_at DESC LIMIT 5',
        [userId]
      );

      for (const historyEntry of passwordHistory) {
        const isMatch = await bcrypt.compare(newPassword, historyEntry.password_hash);
        if (isMatch) {
          return false; // Password was used recently
        }
      }

      return true; // Password is not in recent history
    } catch (error) {
      console.error('Failed to check password history:', error);
      return true; // Allow password change if check fails
    }
  }

  async resetUserPassword(userId, newPassword, resetBy) {
    try {
      // Validate new password
      if (!this.validatePassword(newPassword)) {
        throw new Error('Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character');
      }

      // Check password history
      const isPasswordUnique = await this.checkPasswordHistory(userId, newPassword);
      if (!isPasswordUnique) {
        throw new Error('Password has been used recently. Please choose a different password.');
      }

      // Get current user data for audit log - use query method for reliable parameter binding
      const currentUserResults = await this.query('SELECT * FROM users WHERE id = ?', [userId]);
      if (!currentUserResults || currentUserResults.length === 0) {
        throw new Error('User not found');
      }
      const currentUser = currentUserResults[0];

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 12);

      // Add current password to history before changing
      await this.addPasswordToHistory(userId, currentUser.password_hash);

      // Update password and set reset required flag
      await this.run(
        'UPDATE users SET password_hash = ?, password_reset_required = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [hashedPassword, userId]
      );

      // Log the password reset
      if (resetBy) {
        await this.logAuditAction(resetBy, 'RESET_PASSWORD', 'users', userId, null, {
          passwordResetRequired: true,
          resetBy: resetBy
        });
      }

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to reset password: ${error.message}`);
    }
  }

  async changeUserPassword(userId, currentPassword, newPassword) {
    try {
      // Get current user - use query method for reliable parameter binding
      const userResults = await this.query('SELECT * FROM users WHERE id = ?', [userId]);
      if (!userResults || userResults.length === 0) {
        throw new Error('User not found');
      }
      const user = userResults[0];

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Validate new password
      if (!this.validatePassword(newPassword)) {
        throw new Error('Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character');
      }

      // Check password history
      const isPasswordUnique = await this.checkPasswordHistory(userId, newPassword);
      if (!isPasswordUnique) {
        throw new Error('Password has been used recently. Please choose a different password.');
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 12);

      // Add current password to history
      await this.addPasswordToHistory(userId, user.password_hash);

      // Update password and clear reset required flag
      await this.run(
        'UPDATE users SET password_hash = ?, password_reset_required = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [hashedPassword, userId]
      );

      // Log the password change
      await this.logAuditAction(userId, 'CHANGE_PASSWORD', 'users', userId, null, {
        passwordChanged: true,
        resetRequired: false
      });

      return { success: true };
    } catch (error) {
      throw new Error(`Failed to change password: ${error.message}`);
    }
  }

  async generateSecurePassword() {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@$!%*?&';
    let password = '';

    // Ensure at least one character from each required category
    password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase
    password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase
    password += '0123456789'[Math.floor(Math.random() * 10)]; // Number
    password += '@$!%*?&'[Math.floor(Math.random() * 7)]; // Special character

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  async getAuditLog(filters = {}) {
    try {
      let query = `
        SELECT al.*, u.first_name || ' ' || u.last_name as user_name
        FROM audit_log al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE 1=1
      `;
      const params = [];

      if (filters.userId) {
        query += ' AND al.user_id = ?';
        params.push(filters.userId);
      }

      if (filters.action) {
        query += ' AND al.action = ?';
        params.push(filters.action);
      }

      if (filters.tableName) {
        query += ' AND al.table_name = ?';
        params.push(filters.tableName);
      }

      if (filters.startDate) {
        query += ' AND al.timestamp >= ?';
        params.push(filters.startDate);
      }

      if (filters.endDate) {
        query += ' AND al.timestamp <= ?';
        params.push(filters.endDate);
      }

      query += ' ORDER BY al.timestamp DESC LIMIT ?';
      params.push(filters.limit || 100);

      return await this.query(query, params);
    } catch (error) {
      throw new Error(`Failed to get audit log: ${error.message}`);
    }
  }

  // File management methods for logo uploads
  async saveInstitutionLogo(logoData, updatedBy) {
    try {
      const { fileName, fileData, mimeType } = logoData;

      // Validate file type
      const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];
      if (!allowedTypes.includes(mimeType)) {
        throw new Error('Invalid file type. Only PNG, JPG, JPEG, and SVG files are allowed.');
      }

      // Validate file size (2MB limit)
      const maxSize = 2 * 1024 * 1024; // 2MB in bytes
      if (fileData.length > maxSize) {
        throw new Error('File size too large. Maximum size is 2MB.');
      }

      // Create assets directory if it doesn't exist
      const assetsDir = path.join(__dirname, '../assets/logos');
      if (!fs.existsSync(assetsDir)) {
        fs.mkdirSync(assetsDir, { recursive: true });
      }

      // Generate unique filename
      const timestamp = Date.now();
      const extension = fileName.split('.').pop();
      const uniqueFileName = `logo_${timestamp}.${extension}`;
      const filePath = path.join(assetsDir, uniqueFileName);

      // Save file
      fs.writeFileSync(filePath, fileData);

      // Update institution with new logo path
      const institution = await this.getInstitution();
      if (institution) {
        // Remove old logo file if it exists
        if (institution.logo_path) {
          const oldLogoPath = path.join(__dirname, '../assets/logos', path.basename(institution.logo_path));
          if (fs.existsSync(oldLogoPath)) {
            fs.unlinkSync(oldLogoPath);
          }
        }

        await this.run(
          'UPDATE institution SET logo_path = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [uniqueFileName, institution.id]
        );

        // Log the logo update
        if (updatedBy) {
          await this.logAuditAction(updatedBy, 'UPDATE_LOGO', 'institution', institution.id,
            { logoPath: institution.logo_path },
            { logoPath: uniqueFileName }
          );
        }
      }

      return { logoPath: uniqueFileName };
    } catch (error) {
      throw new Error(`Failed to save logo: ${error.message}`);
    }
  }

  async removeInstitutionLogo(updatedBy) {
    try {
      const institution = await this.getInstitution();
      if (institution && institution.logo_path) {
        // Remove file from filesystem
        const logoPath = path.join(__dirname, '../assets/logos', path.basename(institution.logo_path));
        if (fs.existsSync(logoPath)) {
          fs.unlinkSync(logoPath);
        }

        // Update database
        await this.run(
          'UPDATE institution SET logo_path = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [institution.id]
        );

        // Log the logo removal
        if (updatedBy) {
          await this.logAuditAction(updatedBy, 'REMOVE_LOGO', 'institution', institution.id,
            { logoPath: institution.logo_path },
            { logoPath: null }
          );
        }

        return { success: true };
      }

      return { success: false, message: 'No logo to remove' };
    } catch (error) {
      throw new Error(`Failed to remove logo: ${error.message}`);
    }
  }
}

module.exports = PeerReviewDatabase;
